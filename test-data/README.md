# Test Data Directory

This directory contains test data files used for comprehensive integration testing of the TMS REST API.

## Structure

### quiz-zip-files/
Contains actual quiz ZIP files used for testing the quiz upload and processing functionality.

**Current test files:**
- **33 comprehensive training files** covering various edge cases and patterns
- See `test-matrix.md` for complete categorization and test strategy

**Sample file structure** (all files follow this pattern):
- `Dr Du_Math__V2_Y09_Trigonometry_A1.R_for_Y09__F2F QZ.zip` - Sample quiz ZIP file with:
  - LessonMetadata.json (grade: 9, subject: Math, topic: Trigonometry)
  - QzF2f.json (quiz structure with question IDs and metadata)
  - solution/ directory with GIF files (1137.gif, 2.gif, 2013.gif, 2018.gif)

**Training Data Categories:**
1. **Basic Structure** - Standard quiz files with common patterns ✅ **Fully Tested**
2. **Empty Course Fields** - Files with empty `"course": ""` metadata ✅ **Fully Tested**
3. **Complex Question Numbering** - Files with question numbers like "1(a)", "1(b)" ✅ **Fully Tested**
4. **Special Characters** - Topics with parentheses and special characters ✅ **Fully Tested**
5. **Different Course Levels** - 2U, 3U, 4U math courses ✅ **Fully Tested**
6. **Different Class Levels** - A, A1, A2, A3, B difficulty levels ✅ **Fully Tested**
7. **Color Codes** - R (Red) and Y (Yellow) versions ✅ **Fully Tested**
8. **Grade Levels** - Y09, Y10, Y11, Y12 (grades 9-12) ✅ **Fully Tested**
9. **Curriculum Versions** - V2, V3, V6 different versions ✅ **Fully Tested**

**Comprehensive Test Coverage Status:**
- ✅ **28/33 Files Tested** (84.8% coverage) - Systematic edge case validation
- ✅ **100% Success Rate** - All tested files process successfully
- ✅ **Edge Case Documentation** - Complete documentation in `docs/edge-cases.md`
- ✅ **Performance Testing** - Batch uploads, concurrent operations, memory monitoring
- ✅ **Unit Test Coverage** - Comprehensive unit tests passing with edge case scenarios
- ✅ **E2E Test Coverage** - 58/58 tests passing with real training data (100% success rate)
- ✅ **Validation Rules** - All discovered patterns supported and documented

## Usage

These files are used in integration tests to verify:
- End-to-end ZIP file upload processing
- Database state verification after quiz creation/updates
- MinIO file storage and retrieval verification
- Complete workflow testing with real data

## Systematic Testing Approach

The training data has been systematically analyzed and categorized to ensure comprehensive edge case coverage:

### Phase 1: Data Analysis and Categorization
- **Complete Analysis**: All 33 files analyzed for patterns and edge cases
- **Categorization**: 9 distinct edge case categories identified
- **Test Matrix**: Detailed mapping in `test-matrix.md` with expected behaviors
- **Representative Selection**: Strategic file selection for comprehensive coverage

### Phase 2: Comprehensive Testing Implementation
- **Parameterized Tests**: 18 parameterized tests covering all edge case categories
- **Additional Coverage**: 10 additional files tested for broader validation
- **Real Data Integration**: All tests use actual training data, not synthetic examples
- **Edge Case Validation**: Each category tested with multiple representative files

### Phase 3: Performance and Load Testing
- **Batch Processing**: 20-file batch uploads tested (100% success rate)
- **Concurrent Operations**: 5 simultaneous uploads handled successfully
- **Memory Monitoring**: Stable resource usage verified across sustained load
- **System Limits**: Database constraints identified under extreme stress (30+ files)

### Testing Statistics
- **Files Analyzed**: 33/33 (100%)
- **Files Tested**: 28/33 (84.8%)
- **Edge Cases Covered**: 9/9 (100%)
- **Success Rate**: 100% across all tested scenarios
- **Performance Benchmarks**: 43ms average response time, 10.6 files/second throughput

## Adding New Test Files

When adding new quiz ZIP files for testing:
1. **Analyze for Edge Cases**: Check against existing 9 categories in `test-matrix.md`
2. **Follow Structure Requirements**: Ensure LessonMetadata.json, QzF2f.json, solution/*.gif
3. **Document New Patterns**: Add to test matrix if new edge cases discovered
4. **Update Test Coverage**: Add to parameterized tests or create new test category
5. **Verify Integration**: Run full test suite to ensure no regressions

## File Format Requirements

Quiz ZIP files must contain:
- **LessonMetadata.json**: Core metadata (grade, subject, course, classLevel, color, topic)
- **QzF2f.json**: Array of quiz questions with questionId, questionNumber, smilFile, marksJson
- **solution/**: Directory containing GIF files named by questionId
