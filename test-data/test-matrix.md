# Test Data Matrix - Quiz ZIP Files

This document categorizes all training data files by their characteristics and edge cases to ensure comprehensive test coverage.

## Test Categories

### Category 1: Basic Structure Tests
**Purpose**: Verify standard quiz processing with common patterns

| File | Grade | Course | Class Level | Color | Topic | Question Count | Notes |
|------|-------|--------|-------------|-------|-------|----------------|-------|
| `Dr Du_Math_2U_V6 (2024)_Y11_TF(II)_A_for_Y11_2U_F2F QZ.zip` | 11 | 2U | A | R | Trigonometric Functions (II) | 3 | Standard structure |
| `Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A1.R_for_Y11_3U_F2F QZ.zip` | 11 | 3U | A1 | R | Polynomial Functions (II) | 4 | Standard structure |

### Category 2: Empty Course Field Tests
**Purpose**: Test handling of empty course metadata

| File | Grade | Course | Class Level | Color | Topic | Question Count | Notes |
|------|-------|--------|-------------|-------|-------|----------------|-------|
| `Dr Du_Math__V6_Y09_Plane Geometry (IV)_A1.R_for_Y09__F2F QZ.zip` | 9 | "" | A1 | R | Plane Geometry (IV) | 4 | Empty course field |
| `Dr Du_Math__V6_Y10_PF(II)_A1.R_for_Y10__F2F QZ.zip` | 10 | "" | A1 | R | Polynomial Functions (II) | 4 | Empty course field |

### Category 3: Complex Question Numbering Tests
**Purpose**: Test parsing of complex question numbering schemes

| File | Grade | Course | Class Level | Color | Topic | Question Count | Notes |
|------|-------|--------|-------------|-------|-------|----------------|-------|
| `Dr Du_Math_3U_V6 (2023)_Y11_Binomial Expansion_B.Y_for_Y11_3U_F2F QZ.zip` | 11 | 3U | B | Y | Binomial Expansion | 6 | Complex numbering: 1(a), 1(b), 2(a), 2(b) |

### Category 4: Special Characters in Topics Tests
**Purpose**: Test handling of special characters and parentheses

| File | Grade | Course | Class Level | Color | Topic | Question Count | Notes |
|------|-------|--------|-------------|-------|-------|----------------|-------|
| `Dr Du_Math__V6_Y09_Plane Geometry (IV)_A1.R_for_Y09__F2F QZ.zip` | 9 | "" | A1 | R | Plane Geometry (IV) | 4 | Parentheses in topic |
| `Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A1.R_for_Y11_3U_F2F QZ.zip` | 11 | 3U | A1 | R | Polynomial Functions (II) | 4 | Parentheses in topic |

### Category 5: Different Course Levels Tests
**Purpose**: Test different math course levels (2U, 3U, 4U)

| File | Grade | Course | Class Level | Color | Topic | Question Count | Notes |
|------|-------|--------|-------------|-------|-------|----------------|-------|
| `Dr Du_Math_2U_V6 (2024)_Y12_TE(I)_A_for_Y12_2U_F2F QZ.zip` | 12 | 2U | A | R | Term Exam (I) | 3 | 2U Course |
| `Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_A1.R_for_Y12_3U_F2F QZ.zip` | 12 | 3U | A1 | R | Term Exam (I) | 4 | 3U Course |
| `Dr Du_Math_4U_V6 (2024)_Y12_TE(I)_A1.R_for_Y12_4U_F2F QZ.zip` | 12 | 4U | A1 | R | Term Exam (I) | 4 | 4U Course |

### Category 6: Different Class Levels Tests
**Purpose**: Test various class level patterns (A, A1, A2, A3, B)

| File | Grade | Course | Class Level | Color | Topic | Question Count | Notes |
|------|-------|--------|-------------|-------|-------|----------------|-------|
| `Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A1.R_for_Y11_3U_F2F QZ.zip` | 11 | 3U | A1 | R | Polynomial Functions (II) | 4 | A1 Level |
| `Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A2.R_for_Y11_3U_F2F QZ.zip` | 11 | 3U | A2 | R | Polynomial Functions (II) | 4 | A2 Level |
| `Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A3.R_for_Y11_3U_F2F QZ.zip` | 11 | 3U | A3 | R | Polynomial Functions (II) | 4 | A3 Level |
| `Dr Du_Math_3U_V6 (2023)_Y11_Binomial Expansion_B.R_for_Y11_3U_F2F QZ.zip` | 11 | 3U | B | R | Binomial Expansion | 6 | B Level |

### Category 7: Color Code Tests (Student vs Worked Solutions)
**Purpose**: Test different color codes (R = Red, Y = Yellow)

| File | Grade | Course | Class Level | Color | Topic | Question Count | Notes |
|------|-------|--------|-------------|-------|-------|----------------|-------|
| `Dr Du_Math_3U_V6 (2023)_Y11_Binomial Expansion_B.R_for_Y11_3U_F2F QZ.zip` | 11 | 3U | B | R | Binomial Expansion | 6 | Red version |
| `Dr Du_Math_3U_V6 (2023)_Y11_Binomial Expansion_B.Y_for_Y11_3U_F2F QZ.zip` | 11 | 3U | B | Y | Binomial Expansion | 6 | Yellow version |

### Category 8: Different Grade Levels Tests
**Purpose**: Test all supported grade levels (Y09-Y12)

| File | Grade | Course | Class Level | Color | Topic | Question Count | Notes |
|------|-------|--------|-------------|-------|-------|----------------|-------|
| `Dr Du_Math__V6_Y09_Plane Geometry (IV)_A1.R_for_Y09__F2F QZ.zip` | 9 | "" | A1 | R | Plane Geometry (IV) | 4 | Grade 9 |
| `Dr Du_Math__V6_Y10_PF(II)_A1.R_for_Y10__F2F QZ.zip` | 10 | "" | A1 | R | Polynomial Functions (II) | 4 | Grade 10 |
| `Dr Du_Math_2U_V6 (2024)_Y11_TF(II)_A_for_Y11_2U_F2F QZ.zip` | 11 | 2U | A | R | Trigonometric Functions (II) | 3 | Grade 11 |
| `Dr Du_Math_2U_V6 (2024)_Y12_TE(I)_A_for_Y12_2U_F2F QZ.zip` | 12 | 2U | A | R | Term Exam (I) | 3 | Grade 12 |

### Category 9: Different Curriculum Versions Tests
**Purpose**: Test different curriculum versions (V2, V3, V6)

| File | Grade | Course | Class Level | Color | Topic | Question Count | Notes |
|------|-------|--------|-------------|-------|-------|----------------|-------|
| `Dr Du_Math__V2_Y09_Trigonometry_A1.R_for_Y09__F2F QZ.zip` | 9 | "" | A1 | R | Trigonometry | 4 | Version 2 |
| `Dr Du_Math__V3_Y09_Plane Geometry (IV)_B_for_Y09__F2F QZ.zip` | 9 | "" | B | R | Plane Geometry (IV) | 4 | Version 3 |
| `Dr Du_Math__V6_Y09_Plane Geometry (IV)_A1.R_for_Y09__F2F QZ.zip` | 9 | "" | A1 | R | Plane Geometry (IV) | 4 | Version 6 |

## Test Execution Strategy

### Phase 1: Representative Sample Testing
Select one file from each category for initial comprehensive testing:
- Basic Structure: `Dr Du_Math_2U_V6 (2024)_Y11_TF(II)_A_for_Y11_2U_F2F QZ.zip`
- Empty Course: `Dr Du_Math__V6_Y09_Plane Geometry (IV)_A1.R_for_Y09__F2F QZ.zip`
- Complex Numbering: `Dr Du_Math_3U_V6 (2023)_Y11_Binomial Expansion_B.Y_for_Y11_3U_F2F QZ.zip`

### Phase 2: Edge Case Validation
Test all files in categories with potential edge cases:
- All empty course field files
- All complex question numbering files
- All special character topic files

### Phase 3: Comprehensive Coverage
Test all 33 files to ensure complete system compatibility

## Expected Behaviors

### Empty Course Field
- System should accept empty string in course field
- Database should store empty string correctly
- API responses should handle empty course gracefully

### Complex Question Numbering
- System should parse question numbers like "1(a)", "1(b)", "2(a)" correctly
- Question numbering should be stored as-is in database
- No validation errors for non-numeric question numbers

### Special Characters
- Topics with parentheses should be handled correctly
- No encoding issues with special characters
- Database storage should preserve all characters

### Different Course/Grade Levels
- All course levels (2U, 3U, 4U) should be supported
- All grade levels (9-12) should be supported
- All class levels (A, A1-A3, B) should be supported

## Validation Criteria

For each test file, verify:
1. ✅ ZIP extraction completes without errors
2. ✅ LessonMetadata.json parses correctly
3. ✅ QzF2f.json parses correctly
4. ✅ All GIF files are extracted and uploaded to MinIO
5. ✅ Quiz record is created in database with correct metadata
6. ✅ QuizAsset records are created for all GIF files
7. ✅ API response contains correct data structure
8. ✅ Database cleanup works correctly after test
