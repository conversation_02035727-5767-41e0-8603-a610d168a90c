# TMS REST API - Production Deployment Guide

## 🚨 SECURITY REQUIREMENTS

**CRITICAL**: This application contains security-sensitive configurations that must be properly configured before production deployment.

### Prerequisites

1. **Secure Credentials**: Generate strong, unique credentials (minimum 32 characters)
2. **SSL Certificates**: Obtain valid SSL certificates for HTTPS
3. **Environment Isolation**: Separate production environment from development
4. **Access Control**: Restrict access to production systems

## 📋 Production Environment Setup

### Step 1: Environment Configuration

1. Copy the production environment template:
   ```bash
   cp .env.production.template .env.production
   ```

2. Generate secure credentials using a password manager or:
   ```bash
   # Generate secure passwords (32+ characters)
   openssl rand -base64 32
   ```

3. Update `.env.production` with your secure credentials:
   - Replace ALL placeholder values
   - Use unique credentials for each service
   - Ensure minimum 32 character length for passwords

### Step 2: Database Setup

1. Create production database:
   ```sql
   CREATE DATABASE tms_production_database;
   CREATE USER tms_prod_db_user_2025_secure WITH PASSWORD 'your-secure-password';
   GRANT ALL PRIVILEGES ON DATABASE tms_production_database TO tms_prod_db_user_2025_secure;
   ```

2. **CRITICAL**: Verify `DB_SYNC=false` in production to prevent data loss

### Step 3: MinIO Setup

1. Configure MinIO with SSL:
   ```bash
   # Ensure SSL certificates are in place
   # Set MINIO_USE_SSL=true in .env.production
   ```

2. Create production bucket with proper permissions

### Step 4: SSL/HTTPS Configuration

1. Obtain SSL certificates (Let's Encrypt recommended)
2. Configure reverse proxy (nginx/Apache) for HTTPS termination
3. Set `TRUST_PROXY=true` in environment

## 🔒 Security Checklist

Before deployment, verify:

- [ ] All hardcoded credentials removed from source code
- [ ] Strong, unique production credentials generated (32+ chars)
- [ ] `DB_SYNC=false` to prevent data loss
- [ ] `DB_LOGGING=false` to prevent credential exposure
- [ ] `MINIO_USE_SSL=true` for encrypted storage
- [ ] `NODE_ENV=production` set
- [ ] SSL certificates configured
- [ ] Admin interfaces removed from production
- [ ] Rate limiting configured
- [ ] Security headers implemented

## 🚀 Deployment Commands

### Production Build
```bash
# Build production Docker image
npm run docker:build:prod

# Start production services
docker-compose -f docker-compose.prod.yml up -d
```

### Health Check
```bash
# Verify deployment
curl -k https://your-domain.com/health
```

## 📊 Monitoring

- Health endpoint: `/health`
- API documentation: `/api/docs` (consider disabling in production)
- Logs: `/var/log/tms-api/`

## 🔧 Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Verify credentials in `.env.production`
   - Check database server accessibility
   - Ensure `DB_SYNC=false`

2. **MinIO Connection Failed**
   - Verify MinIO credentials
   - Check SSL configuration
   - Ensure bucket exists

3. **Authentication Failed**
   - Verify API credentials
   - Check Basic Auth header format

## 🔄 Maintenance

### Regular Tasks

1. **Credential Rotation** (Quarterly)
   - Generate new credentials
   - Update environment configuration
   - Restart services

2. **Security Updates**
   - Monitor for dependency vulnerabilities
   - Apply security patches promptly

3. **Backup Verification**
   - Test database backups
   - Verify MinIO data integrity

## ⚠️ IMPORTANT WARNINGS

1. **Never commit `.env.production` to version control**
2. **Always use HTTPS in production**
3. **Regularly rotate credentials**
4. **Monitor for security vulnerabilities**
5. **Keep backups of production data**

## 📞 Support

For deployment issues, refer to:
- Application logs: `/var/log/tms-api/`
- Health check endpoint: `/health`
- API documentation: `/api/docs`
