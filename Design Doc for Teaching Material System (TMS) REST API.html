






<!DOCTYPE html>
<html>
    <head>
        <title>View Source</title>
        <link rel="canonical" href="/pages/viewpage.action?pageId=$action.page.id" />
        <script>
window.WRM=window.WRM||{};window.WRM._unparsedData=window.WRM._unparsedData||{};window.WRM._unparsedErrors=window.WRM._unparsedErrors||{};
WRM._unparsedData["com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path.context-path"]="\u0022\u0022";
if(window.WRM._dataArrived)window.WRM._dataArrived();</script>
<link rel="stylesheet" href="/s/1db211956c40c339a25803ef0ed9a6e1-CDN/-em83mw/9012/8yg2g7/60134ddd3f759aa8d3a53dd0125fd71d/_/download/contextbatch/css/_super/batch.css" data-wrm-key="_super" data-wrm-batch-type="context" media="all">
<link rel="stylesheet" href="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/-em83mw/9012/8yg2g7/0cfc208fd34b88450a8d51a9cea46d13/_/download/contextbatch/css/plugin.viewsource,-_super/batch.css" data-wrm-key="plugin.viewsource,-_super" data-wrm-batch-type="context" media="all">
<link rel="stylesheet" href="/s/93f9116e99e6c90afc84e0c2a1115e99-CDN/-em83mw/9012/8yg2g7/c167f3c7776570a81b92fb64a9b2a320/_/download/contextbatch/css/page,-_super/batch.css?cefp_collab_enabled=true&amp;cefp_ed_perm=false&amp;cefp_is_admin=true&amp;gatekeeper-ui-v2=true" data-wrm-key="page,-_super" data-wrm-batch-type="context" media="all">
<link rel="stylesheet" href="/s/7be457410cca5d0a66a95adf63a4ba92-CDN/-em83mw/9012/8yg2g7/7fee62789979f7103a0aaab09f274287/_/download/contextbatch/css/editor-content,-_super/batch.css" data-wrm-key="editor-content,-_super" data-wrm-batch-type="context" media="all">

    </head>

    <body class="mceContentBody aui-theme-default wiki-content fullsize">
        <p>&nbsp;</p>         <h2>1. Introduction</h2><h3>1.1 Purpose</h3><p>This document outlines the design for our Teaching Material System (TMS) REST API.</p><h3>1.2 Service Owner</h3><p><a class="confluence-link confluence-userlink user-mention current-user-mention" data-username="williamdu" href="/display/~williamdu" data-linked-resource-id="119537675" data-linked-resource-version="1" data-linked-resource-type="userinfo" userkey="2c9082bd92ceb93e0192d10ba4d60000" data-linked-resource-default-alias="William Du" data-base-url="http://drdudevserver:8090">William Du</a> </p><h3>1.3 Scope</h3><p>TMS will support creating, reading, updating, and deleting teaching material. In particular:</p><ul><li>Lecture Notes</li><li>Homework</li><li>Quizzes</li></ul><p>for both worked solutions and blanks.</p><h3>1.4 Definitions</h3><ul class="tight"><li><p><strong>TMS: </strong>Teaching Material System.</p></li></ul><h2>2. System Overview</h2><p>The TMS API is a stateless, HTTP-based service built using Node.js with Nest.js, connected to a PostgreSQL database. Basic auth is used to secure endpoints.</p><h2>3. Architecture</h2><h3>3.1 Components</h3><ul class="tight"><li><p><strong>API Server</strong>: Handles HTTP requests and responses.</p></li><li><p><strong>Database</strong>: PostgreSQL for persistent storage of tasks and user data.</p></li><li><p><strong>Authentication Middleware</strong>: Basic Auth.</p></li><li><p><strong>Controller Layer</strong>: Defines endpoints, request handling and response generation.</p></li><li><p><strong>Service Layer</strong>: Contains business logic for task operations.</p></li><li><p><strong>Repository Layer</strong>: Interacts with the database.</p></li></ul><h3>3.2 Deployment</h3><p>The API will be deployed on <code>server76</code>  using <code>pm2</code> .</p><h2>4. API Design</h2><h3>4.1 Endpoints</h3><table class="wrapped relative-table confluenceTable" style="width: 59.4808%;"><colgroup><col style="width: 6.9331%;" /><col style="width: 37.6097%;" /><col style="width: 36.7549%;" /><col style="width: 18.7023%;" /></colgroup><tbody><tr><th class="confluenceTh"><p>Method</p></th><th class="confluenceTh"><p>Endpoint</p></th><th class="confluenceTh"><p>Description</p></th><th class="confluenceTh"><p>Authentication</p></th></tr><tr><td class="confluenceTd"><p>GET</p></td><td class="confluenceTd"><p>/quiz/f2f/paperless-marking-worked-solutions</p></td><td class="confluenceTd"><p>Retrieve worked solutions for one paperless marking quiz.</p></td><td class="confluenceTd"><p>EMSx (Basic Auth)</p></td></tr><tr><td class="confluenceTd"><p>POST</p></td><td class="confluenceTd"><p>/quiz/f2f/paperless-marking-worked-solutions</p></td><td class="confluenceTd"><p>Upload worked solutions .zip file for one paperless marking quiz.</p></td><td class="confluenceTd"><p>EMSx (Basic Auth)</p></td></tr></tbody></table><h3>4.2 HTTP headers</h3><p>Every request will contain a Correlation ID as an HTTP header from EMSx.</p><table class="wrapped confluenceTable"><colgroup><col /><col /><col /><col /><col /></colgroup><tbody><tr><th scope="col" class="confluenceTh">HTTP Header</th><th scope="col" class="confluenceTh">Type</th><th scope="col" class="confluenceTh">Description</th><th scope="col" class="confluenceTh">Required</th><th scope="col" class="confluenceTh">Example Value</th></tr><tr><td class="confluenceTd">X-Correlation-ID</td><td class="confluenceTd">string</td><td class="confluenceTd">Correlation ID identifying the root source of the request. Should be logged.</td><td class="confluenceTd">Y</td><td class="confluenceTd"><p>123e4567-e89b-12d3-a456-426614174000</p></td></tr></tbody></table><h3>4.3 Data Models</h3><h4>Paperless Marking Worked Solutions</h4><table class="wysiwyg-macro" data-macro-name="code" data-macro-id="b0dee56d-1a4c-48b6-8733-98121650dfc3" aria-label="code macro" data-macro-parameters="title=json" data-macro-schema-version="1" style="background-image: url(http://drdudevserver:8090/plugins/servlet/confluence/placeholder/macro-heading?definition=e2NvZGU6dGl0bGU9anNvbn0&amp;locale=en_GB&amp;version=2); background-repeat: no-repeat;" data-macro-body-type="PLAIN_TEXT"><tr><td class="wysiwyg-macro-body"><pre>{
	"id": 1,
	"retrievedMetadata": {
	    "subject": "Math",
	    "grade": 9,
	    "classLevel": "A1",
	    "lessonName": "Formulae",
	    "color": "R",
	    "year": 2025,
	    "term": 1,
	    "week": 1,
	    "weekType": "normal",
	    "course": "",
	    "teachingProgram": "",
	    "internalMetadata": [&lt;contents of QzF2f.json from .zip file>],
   		"originalFilename": "Dr Du_Math__V6_Y09_Formulae_A1.R_for_Y09__F2F QZ.zip",
    	"uploadTimestamp": "2025-05-02T01:38:23.926Z"
    },
   	"gifUrls": [
   		{
	    	"id": "3421",
       		"url": "https://tms-minio:9000/questions-work-solutions/3421.gif?X-Amz-Algorithm=AWS4-HMAX-SHA..."
    	},
    	{
       		"id": "1234",
       		"url": "https://tms-minio:9000/questions-work-solutions/1234.gif?X-Amz-Algorithm=AWS4-HMAX-SHA..."
    	}
  	]
}</pre></td></tr></table><h3>4.4 Request/Response Examples</h3><p>New endpoints to be created are marked in <span style="color: rgb(255,153,0);">orange</span>.</p><h4><u><span style="color: rgb(0,0,0);">GET /quiz/f2f/paperless-marking-worked-solutions</span></u></h4><p><strong>Purpose: </strong>Retrieve worked solutions for one paperless marking quiz. Basically it is the same data contained in a <code>QzF2f.zip</code>  file from Dr. Du, with the <code>solution</code>  GIFs translated into pre-signed URLs from the TMS MinIO.</p><p><strong>Query Parameters:</strong></p><table class="wrapped confluenceTable"><colgroup><col /><col /><col /><col /><col /></colgroup><tbody><tr><th scope="col" class="confluenceTh">Parameter</th><th scope="col" class="confluenceTh">Type</th><th scope="col" class="confluenceTh">Description</th><th scope="col" class="confluenceTh">Required</th><th scope="col" class="confluenceTh">Example Value</th></tr><tr><td class="confluenceTd">grade</td><td class="confluenceTd">integer</td><td class="confluenceTd"><br /></td><td class="confluenceTd">Y</td><td class="confluenceTd">12</td></tr><tr><td class="confluenceTd">subject</td><td class="confluenceTd">string</td><td class="confluenceTd"><br /></td><td class="confluenceTd">Y</td><td class="confluenceTd">Math, Phys, Chem, Engl</td></tr><tr><td class="confluenceTd">course</td><td class="confluenceTd">string</td><td class="confluenceTd"><br /></td><td class="confluenceTd">Y</td><td class="confluenceTd">3U. <em>For Y9 and Y10 where there is no course, please still have this param present but input as an empty param<code>.</code>  </em></td></tr><tr><td class="confluenceTd">classLevel</td><td class="confluenceTd">string</td><td class="confluenceTd"><br /></td><td class="confluenceTd">Y</td><td class="confluenceTd">A1</td></tr><tr><td class="confluenceTd">color</td><td class="confluenceTd">string</td><td class="confluenceTd"><br /></td><td class="confluenceTd">Y</td><td class="confluenceTd">R (for red quiz). <em>If there is only one quiz version, it will be R.</em></td></tr><tr><td class="confluenceTd">year</td><td class="confluenceTd">integer</td><td class="confluenceTd"><br /></td><td class="confluenceTd">Y</td><td class="confluenceTd">2025</td></tr><tr><td class="confluenceTd">term</td><td class="confluenceTd">integer</td><td class="confluenceTd"><br /></td><td class="confluenceTd">Y</td><td class="confluenceTd">2</td></tr><tr><td class="confluenceTd">week</td><td class="confluenceTd">integer</td><td class="confluenceTd"><br /></td><td class="confluenceTd">Y</td><td class="confluenceTd">4</td></tr><tr><td class="confluenceTd">weekType</td><td class="confluenceTd">string</td><td class="confluenceTd"><br /></td><td class="confluenceTd">Y</td><td class="confluenceTd">Either <code>normal</code>  or <code>holiday</code> </td></tr><tr><td class="confluenceTd">teachingProgram</td><td class="confluenceTd">string</td><td class="confluenceTd"><br /></td><td class="confluenceTd">N</td><td class="confluenceTd"><code>St George Girls</code> </td></tr><tr><td class="confluenceTd">lessonName</td><td class="confluenceTd">string</td><td class="confluenceTd">For future reference and logging.</td><td class="confluenceTd">N</td><td class="confluenceTd">Formulae</td></tr></tbody></table><p><strong>Request Example:</strong></p><table class="wysiwyg-macro" data-macro-name="code" data-macro-id="81d1f077-fa5b-4464-8ade-d191aca77b6b" aria-label="code macro" data-macro-parameters="title=Http" data-macro-schema-version="1" style="background-image: url(http://drdudevserver:8090/plugins/servlet/confluence/placeholder/macro-heading?definition=e2NvZGU6dGl0bGU9SHR0cH0&amp;locale=en_GB&amp;version=2); background-repeat: no-repeat;" data-macro-body-type="PLAIN_TEXT"><tr><td class="wysiwyg-macro-body"><pre>GET /quiz/f2f/paperless-marking-worked-solutions?grade=12&amp;subject=Math&amp;course=3U&amp;classLevel=A1&amp;year=2025&amp;term=2&amp;week=4&amp;weekType=normal&amp;lessonName=Formulae</pre></td></tr></table><p><strong>Response (200 OK)</strong>:</p><table class="wysiwyg-macro" data-macro-name="code" data-macro-id="e1cbce11-f6a3-4ea5-9a0b-17d49cd78674" aria-label="code macro" data-macro-parameters="title=json" data-macro-schema-version="1" style="background-image: url(http://drdudevserver:8090/plugins/servlet/confluence/placeholder/macro-heading?definition=e2NvZGU6dGl0bGU9anNvbn0&amp;locale=en_GB&amp;version=2); background-repeat: no-repeat;" data-macro-body-type="PLAIN_TEXT"><tr><td class="wysiwyg-macro-body"><pre>See `Paperless Marked Worked Solutions` Data Model</pre></td></tr></table><h4><u><span style="color: rgb(0,0,0);">POST /quiz/f2f/paperless-marking-worked-solutions</span></u></h4><p><strong><span style="color: rgb(0,0,0);">Purpose:</span></strong><span style="color: rgb(0,0,0);"> Upload worked solutions .zip file for one paperless marking quiz.</span></p><p><strong>Query Parameters:</strong></p><table class="wrapped confluenceTable"><colgroup class=""><col class="" /><col class="" /><col class="" /><col class="" /><col class="" /></colgroup><tbody class=""><tr class=""><th class="confluenceTh"><span style="color: rgb(255,102,0);">Parameter</span></th><th class="confluenceTh"><span style="color: rgb(255,102,0);">Type</span></th><th class="confluenceTh"><span style="color: rgb(255,102,0);">Description</span></th><th class="confluenceTh"><span style="color: rgb(255,102,0);">Required</span></th><th class="confluenceTh"><span style="color: rgb(255,102,0);">Example Value</span></th></tr><tr class=""><td class="confluenceTd"><span style="color: rgb(255,102,0);">year</span></td><td class="confluenceTd"><span style="color: rgb(255,102,0);">integer</span></td><td class="confluenceTd"><br /></td><td class="confluenceTd"><span style="color: rgb(255,102,0);">Y</span></td><td class="confluenceTd"><span style="color: rgb(255,102,0);">2025</span></td></tr><tr class=""><td class="confluenceTd"><span style="color: rgb(255,102,0);">term</span></td><td class="confluenceTd"><span style="color: rgb(255,102,0);">integer</span></td><td class="confluenceTd"><br /></td><td class="confluenceTd"><span style="color: rgb(255,102,0);">Y</span></td><td class="confluenceTd"><span style="color: rgb(255,102,0);">2</span></td></tr><tr class=""><td class="confluenceTd"><span style="color: rgb(255,102,0);">week</span></td><td class="confluenceTd"><span style="color: rgb(255,102,0);">integer</span></td><td class="confluenceTd"><br /></td><td class="confluenceTd"><span style="color: rgb(255,102,0);">Y</span></td><td class="confluenceTd"><span style="color: rgb(255,102,0);">4</span></td></tr><tr class=""><td class="confluenceTd"><span style="color: rgb(255,102,0);">weekType</span></td><td class="confluenceTd"><span style="color: rgb(255,102,0);">string</span></td><td class="confluenceTd"><br /></td><td class="confluenceTd"><span style="color: rgb(255,102,0);">Y</span></td><td class="confluenceTd"><span style="color: rgb(255,102,0);">Either <code>normal</code>  or <code>holiday</code> </span></td></tr><tr class=""><td class="confluenceTd"><span style="color: rgb(255,102,0);">teachingProgram</span></td><td class="confluenceTd"><span style="color: rgb(255,102,0);">string</span></td><td class="confluenceTd"><br /></td><td class="confluenceTd"><span style="color: rgb(255,102,0);">N</span></td><td class="confluenceTd"><span style="color: rgb(255,102,0);"><code>St George Girls</code> </span></td></tr></tbody></table><p><strong>Request Body:</strong></p><p><strong>Content-Type:</strong> multipart/form-data</p><table class="relative-table wrapped confluenceTable" style="width: 53.6383%;"><colgroup class=""><col class="" style="width: 20.3158%;" /><col class="" style="width: 8.41099%;" /><col class="" style="width: 22.7744%;" /><col class="" style="width: 10.352%;" /><col class="" style="width: 38.1468%;" /></colgroup><tbody class=""><tr class=""><th class="confluenceTh">Field Name</th><th class="confluenceTh">Type</th><th class="confluenceTh">Description</th><th class="confluenceTh">Required</th><th class="confluenceTh">Example Value</th></tr><tr class=""><td class="confluenceTd">file</td><td class="confluenceTd">file</td><td class="confluenceTd">QzF2f.zip file</td><td class="confluenceTd">Y</td><td class="confluenceTd"><p><span style="color: rgb(255,102,0);">Note that this .zip file will contain <code>LessonMetadata.json</code> , that contains</span></p><p><span style="color: rgb(255,102,0);"><code>{&quot;grade&quot;: 9, &quot;subject&quot;: &quot;Math&quot;, &quot;course&quot;: &quot;&quot;, &quot;classLevel&quot;: &quot;A1&quot;, &quot;color&quot;: &quot;R&quot;, &quot;topic&quot;: &quot;Trigonometry&quot;}</code></span> </p></td></tr></tbody></table><p><strong>Request Example:</strong></p><table class="wysiwyg-macro" data-macro-name="code" data-macro-id="1c52c5ed-c4b6-40e0-8b06-c2e8739f96b5" aria-label="code macro" data-macro-parameters="title=Http" data-macro-schema-version="1" style="background-image: url(http://drdudevserver:8090/plugins/servlet/confluence/placeholder/macro-heading?definition=e2NvZGU6dGl0bGU9SHR0cH0&amp;locale=en_GB&amp;version=2); background-repeat: no-repeat;" data-macro-body-type="PLAIN_TEXT"><tr><td class="wysiwyg-macro-body"><pre>POST /quiz/f2f/paperless-marking-worked-solutions?year=2025&amp;term=2&amp;week=4&amp;weekType=normal</pre></td></tr></table><p><strong>Response (201 Created)</strong>:</p><table class="wysiwyg-macro" data-macro-name="code" data-macro-id="5e727930-a502-430b-b433-1fa4c829160b" aria-label="code macro" data-macro-parameters="title=json" data-macro-schema-version="1" style="background-image: url(http://drdudevserver:8090/plugins/servlet/confluence/placeholder/macro-heading?definition=e2NvZGU6dGl0bGU9anNvbn0&amp;locale=en_GB&amp;version=2); background-repeat: no-repeat;" data-macro-body-type="PLAIN_TEXT"><tr><td class="wysiwyg-macro-body"><pre>{
    "message": "Quiz worked solutions uploaded and processed successfully",
    "data": {
        "quizId": 42,
        "metadataPath": "metadata/Math/9/A1/2025/2/3/normal/metadata.json",
        "uploadedGifs": [
            {
                "questionId": "2013",
                "objectName": "gifs/6a2e4ff8-34a3-44cd-a587-d9baabf5544c.gif"
            },
            {
                "questionId": "1137",
                "objectName": "gifs/c6fa9f4c-6a6e-48eb-950b-38baf25375c5.gif"
            },
            {
                "questionId": "3421",
                "objectName": "gifs/44b6aa6b-fe6d-499a-b623-974b32e5f62b.gif"
            },
            {
                "questionId": "1234",
                "objectName": "gifs/741876d0-f7a5-42a9-bfd7-440912bed735.gif"
            }
        ],
        "gifCount": 4,
        "extractedMetadata": {
            "subject": "Math",
            "grade": 9,
            "classLevel": "A1",
            "color": "R",
            "course": "",
            "topic": "Trigonometry"
        },
        "uploadTimestamp": "2025-05-24T21:45:23.926Z",
        "originalFilename": "Dr Du_Math__V2_Y09_Trigonometry_A1.R_for_Y09__F2F QZ.zip"
    }
}</pre></td></tr></table><h3>4.5 Error Handling</h3><p>Errors return comprehensive JSON responses with standardized structure for better debugging and monitoring:</p><table class="wysiwyg-macro" data-macro-name="code" data-macro-id="503d3c3e-c071-4c53-9e86-fae963e8a1d3" aria-label="code macro" data-macro-parameters="title=json" data-macro-schema-version="1" style="background-image: url(http://drdudevserver:8090/plugins/servlet/confluence/placeholder/macro-heading?definition=e2NvZGU6dGl0bGU9anNvbn0&amp;locale=en_GB&amp;version=2); background-repeat: no-repeat;" data-macro-body-type="PLAIN_TEXT"><tr><td class="wysiwyg-macro-body"><pre>{
  "statusCode": 404,
  "message": "No quiz found matching the provided criteria",
  "details": "Additional error information (optional)",
  "correlationId": "123e4567-e89b-12d3-a456-426614174000",
  "timestamp": "2025-05-24T21:45:23.926Z",
  "path": "/quiz/f2f/paperless-marking-worked-solutions"
}</pre></td></tr></table><p><strong>Error Response Fields:</strong></p><ul><li><strong>statusCode</strong>: HTTP status code (400, 401, 404, 500, etc.)</li><li><strong>message</strong>: Human-readable error description</li><li><strong>details</strong>: Optional additional error information (validation errors, stack traces in development)</li><li><strong>correlationId</strong>: Request correlation ID for tracing (if provided in request)</li><li><strong>timestamp</strong>: ISO 8601 timestamp when error occurred</li><li><strong>path</strong>: Request path that caused the error</li></ul><h2>5. Service Infrastructure</h2><p>The API is deployed on servers with the following IP addresses.</p><table class="relative-table wrapped confluenceTable" style="width: 44.7678%;"><colgroup><col style="width: 19.3902%;" /><col style="width: 16.4429%;" /><col style="width: 21.0966%;" /><col style="width: 43.0728%;" /></colgroup><tbody><tr><th scope="col" class="confluenceTh">IP address</th><th scope="col" class="confluenceTh">Port</th><th scope="col" class="confluenceTh">Environment</th><th scope="col" class="confluenceTh">Authentication</th></tr><tr><td class="confluenceTd"><p><code>***********</code> </p></td><td class="confluenceTd"><code>3000</code> </td><td class="confluenceTd">Production</td><td class="confluenceTd"><br /></td></tr><tr><td class="confluenceTd"><p><code>***********</code> </p></td><td class="confluenceTd"><code>3000</code> </td><td class="confluenceTd">Test</td><td class="confluenceTd"><p>Username: <code>tms-test</code> </p><p>Password: <code>dr2025du</code> </p></td></tr></tbody></table><h2>6. Authentication</h2><ul class="tight"><li><p><strong>Mechanism</strong>: Basic Auth</p></li><li><p><strong>Process</strong>:</p><ol class="tight"><li>Basic Auth is required and must be included in the <span>Authorization</span> header (<span>Basic &lt;base64(username:password)&gt;</span>) for protected endpoints.</li></ol></li></ul><h2>7. Security Considerations</h2><ul class="tight"><li><p><strong>HTTPS</strong>: All API requests are served over HTTPS.</p></li><li><p><strong>Input Validation</strong>: Sanitize and validate all inputs to prevent injection attacks.</p></li><li><p><strong>Rate Limiting</strong>: Implement rate limiting to prevent abuse.</p></li><li><p><strong>IP Whitelisting: </strong>Only allowed IPs can communicate with TMS. A subset of allowed IPs includes (EMSx, Dr Du's computer, <a class="confluence-link confluence-userlink user-mention current-user-mention" data-username="williamdu" href="/display/~williamdu" data-linked-resource-id="119537675" data-linked-resource-version="1" data-linked-resource-type="userinfo" userkey="2c9082bd92ceb93e0192d10ba4d60000" data-linked-resource-default-alias="William Du" data-base-url="http://drdudevserver:8090">William Du</a> 's computer).</p></li></ul><h2>8. Maintenance and Observability/Monitoring</h2><ul><li><strong>Logging: </strong>Comprehensive logging of all requests including a correlation ID from EMSx queries.</li></ul><ul class="tight"><li><p><strong>Monitoring</strong>: Manual inspection of the log files.</p></li><li><p><strong>API Updates</strong>:</p><ul class="tight"><li><p>Update the code, and this design document.</p></li><li><p>Notify the other teams that use TMS.</p></li><li><p>As TMS is a REST API only used by other internal services, we will not do API versioning.</p></li></ul></li></ul><h2>9. Future Enhancements</h2><ul class="tight"><li>Have a better, automated way to monitor the logs eg. Datadog, Splunk, New Relic, Grafana Loki.</li></ul>
        <p>&nbsp;</p>
    </body>
</html>
