/**
 * Deployment Documentation Generator
 *
 * This script generates comprehensive deployment documentation for API consumers,
 * including authentication examples, usage guides, and integration instructions.
 *
 * Usage:
 *   npx ts-node scripts/generate-deployment-docs.ts
 *
 * Output:
 *   - docs/api/README.md: Main API documentation
 *   - docs/api/authentication.md: Authentication guide
 *   - docs/api/examples.md: Usage examples
 *   - docs/api/integration-guide.md: Integration instructions
 */

import { writeFileSync, mkdirSync, existsSync, readFileSync } from 'fs';
import { join } from 'path';

interface ApiSummary {
  title: string;
  version: string;
  description: string;
  openApiVersion: string;
  generatedAt: string;
  endpoints: number;
  schemas: number;
  securitySchemes: number;
}

function generateDeploymentDocumentation() {
  console.log('📚 Starting deployment documentation generation...');

  try {
    // Ensure output directory exists
    const outputDir = join(process.cwd(), 'docs', 'api');
    mkdirSync(outputDir, { recursive: true });

    // Load API summary if available
    let apiSummary: ApiSummary | null = null;
    const summaryPath = join(outputDir, 'api-summary.json');
    if (existsSync(summaryPath)) {
      apiSummary = JSON.parse(readFileSync(summaryPath, 'utf8')) as ApiSummary;
    }

    // Generate main README
    generateMainReadme(outputDir, apiSummary);

    // Generate authentication guide
    generateAuthenticationGuide(outputDir);

    // Generate usage examples
    generateUsageExamples(outputDir);

    // Generate integration guide
    generateIntegrationGuide(outputDir);

    console.log(
      '🎉 Deployment documentation generation completed successfully!',
    );
  } catch (error) {
    console.error('❌ Error generating deployment documentation:', error);
    process.exit(1);
  }
}

function generateMainReadme(outputDir: string, apiSummary: ApiSummary | null) {
  const content = `# Teaching Material System (TMS) REST API

${apiSummary ? `**Version:** ${apiSummary.version} | **OpenAPI:** ${apiSummary.openApiVersion}` : ''}

A centralized service for storing, retrieving, updating, and deleting F2F paperless marking worked solution quiz materials.

## 📋 Overview

The TMS REST API provides a comprehensive solution for managing educational quiz materials with the following key features:

- **Quiz Management**: Upload, retrieve, update, and delete quiz materials
- **File Storage**: Secure storage of quiz assets (GIF files) with MinIO
- **Metadata Extraction**: Automatic extraction of quiz metadata from uploaded ZIP files
- **Authentication**: Basic HTTP authentication for secure access
- **Request Tracing**: Correlation ID support for request tracking
- **Comprehensive Validation**: Input validation with detailed error messages

${
  apiSummary
    ? `
## 📊 API Statistics

- **Endpoints**: ${apiSummary.endpoints}
- **Data Models**: ${apiSummary.schemas}
- **Security Schemes**: ${apiSummary.securitySchemes}
- **Last Updated**: ${new Date(apiSummary.generatedAt).toLocaleString()}
`
    : ''
}

## 🚀 Quick Start

### 1. Authentication

All API endpoints (except health check) require Basic HTTP authentication:

\`\`\`bash
# Base64 encode your credentials
echo -n "username:password" | base64

# Use in requests
curl -H "Authorization: Basic <base64-credentials>" \\
     -H "X-Correlation-ID: $(uuidgen)" \\
     https://api.example.com/quiz/f2f/paperless-marking-worked-solutions
\`\`\`

### 2. Required Headers

- **Authorization**: \`Basic <base64-credentials>\`
- **X-Correlation-ID**: UUID for request tracing

### 3. Base URL

\`\`\`
Production: https://api.tms.example.com
Development: http://localhost:3000
\`\`\`

## 📖 Documentation

- **[Authentication Guide](./authentication.md)** - Detailed authentication setup
- **[Usage Examples](./examples.md)** - Code examples and use cases
- **[Integration Guide](./integration-guide.md)** - Step-by-step integration
- **[OpenAPI Specification](./api-spec.json)** - Complete API specification
- **[Interactive Documentation](http://localhost:3000/api/docs)** - Swagger UI (development)

## 🔗 Available Endpoints

### Quiz Management

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | \`/quiz/f2f/paperless-marking-worked-solutions\` | Retrieve quiz materials |
| POST | \`/quiz/f2f/paperless-marking-worked-solutions\` | Upload new quiz materials |
| PUT | \`/quiz/{id}\` | Update existing quiz |
| DELETE | \`/quiz/{id}\` | Delete quiz materials |

### System

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | \`/health\` | Health check (no auth required) |

## 📝 Request/Response Format

### Successful Response
\`\`\`json
{
  "message": "Success message",
  "data": {
    // Response data
  }
}
\`\`\`

### Error Response
\`\`\`json
{
  "statusCode": 400,
  "message": "Error description",
  "details": "Additional error details",
  "correlationId": "123e4567-e89b-12d3-a456-************"
}
\`\`\`

## 🛠️ Development

### Local Setup

1. Clone the repository
2. Install dependencies: \`npm install\`
3. Start services: \`npm run docker:up\`
4. Run the API: \`npm run start:dev\`
5. Access documentation: http://localhost:3000/api/docs

### Testing

\`\`\`bash
# Run all tests
npm run test:all

# Run API documentation tests
npm run test:api-docs

# Generate API specification
npm run api:generate

# Validate API specification
npm run api:validate
\`\`\`

## 📞 Support

For questions, issues, or feature requests, please contact the development team or create an issue in the project repository.

---

*Generated on ${new Date().toISOString()}*
`;

  const readmePath = join(outputDir, 'README.md');
  writeFileSync(readmePath, content);
  console.log(`✅ Main README generated: ${readmePath}`);
}

function generateAuthenticationGuide(outputDir: string) {
  const content = `# Authentication Guide

## Overview

The TMS REST API uses HTTP Basic Authentication for securing endpoints. All endpoints except the health check require authentication.

## Basic Authentication

### Setup

1. **Obtain Credentials**: Get your username and password from the system administrator
2. **Encode Credentials**: Base64 encode your \`username:password\`
3. **Include in Requests**: Add the \`Authorization\` header to all requests

### Example

\`\`\`bash
# Step 1: Encode credentials (replace with your actual credentials)
echo -n "your-username:your-password" | base64
# Output: eW91ci11c2VybmFtZTp5b3VyLXBhc3N3b3Jk

# Step 2: Use in request
curl -H "Authorization: Basic eW91ci11c2VybmFtZTp5b3VyLXBhc3N3b3Jk" \\
     -H "X-Correlation-ID: $(uuidgen)" \\
     https://api.example.com/quiz/f2f/paperless-marking-worked-solutions
\`\`\`

## Correlation ID

### Purpose
The \`X-Correlation-ID\` header is required for request tracing and debugging.

### Format
- Must be a valid UUID (v4 recommended)
- Example: \`123e4567-e89b-12d3-a456-************\`

### Generation

**JavaScript:**
\`\`\`javascript
function generateCorrelationId() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}
\`\`\`

**Python:**
\`\`\`python
import uuid

correlation_id = str(uuid.uuid4())
\`\`\`

**cURL:**
\`\`\`bash
# macOS/Linux
curl -H "X-Correlation-ID: $(uuidgen)" ...

# Or use a fixed UUID for testing
curl -H "X-Correlation-ID: 123e4567-e89b-12d3-a456-************" ...
\`\`\`

## Error Responses

### 401 Unauthorized
\`\`\`json
{
  "statusCode": 401,
  "message": "Unauthorized",
  "correlationId": "123e4567-e89b-12d3-a456-************"
}
\`\`\`

**Causes:**
- Missing \`Authorization\` header
- Invalid credentials
- Malformed Basic Auth token

### 400 Bad Request (Missing Correlation ID)
\`\`\`json
{
  "statusCode": 400,
  "message": "X-Correlation-ID header is required",
  "correlationId": null
}
\`\`\`

## Security Best Practices

1. **Use HTTPS**: Always use HTTPS in production
2. **Secure Storage**: Store credentials securely (environment variables, key vaults)
3. **Rotate Credentials**: Regularly rotate authentication credentials
4. **Monitor Access**: Track API usage with correlation IDs
5. **Validate Responses**: Always check response status codes

## Integration Examples

### JavaScript (Node.js)
\`\`\`javascript
const axios = require('axios');
const { v4: uuidv4 } = require('uuid');

const client = axios.create({
  baseURL: 'https://api.example.com',
  headers: {
    'Authorization': 'Basic ' + Buffer.from('username:password').toString('base64'),
    'X-Correlation-ID': uuidv4(),
  },
});

// Use the client
const response = await client.get('/quiz/f2f/paperless-marking-worked-solutions');
\`\`\`

### Python
\`\`\`python
import requests
import uuid
import base64

# Setup authentication
credentials = base64.b64encode(b'username:password').decode('ascii')
headers = {
    'Authorization': f'Basic {credentials}',
    'X-Correlation-ID': str(uuid.uuid4()),
}

# Make request
response = requests.get(
    'https://api.example.com/quiz/f2f/paperless-marking-worked-solutions',
    headers=headers
)
\`\`\`

### cURL
\`\`\`bash
#!/bin/bash

# Set credentials (replace with your actual credentials)
USERNAME="your-username"
PASSWORD="your-password"
BASE_URL="https://api.example.com"

# Encode credentials
AUTH_HEADER="Authorization: Basic $(echo -n "$USERNAME:$PASSWORD" | base64)"
CORRELATION_ID="X-Correlation-ID: $(uuidgen)"

# Make request
curl -H "$AUTH_HEADER" \\
     -H "$CORRELATION_ID" \\
     "$BASE_URL/quiz/f2f/paperless-marking-worked-solutions"
\`\`\`
`;

  const authPath = join(outputDir, 'authentication.md');
  writeFileSync(authPath, content);
  console.log(`✅ Authentication guide generated: ${authPath}`);
}

function generateUsageExamples(outputDir: string) {
  const content = `# Usage Examples

This document provides practical examples for using the TMS REST API.

## Prerequisites

- Valid authentication credentials
- API base URL
- HTTP client (curl, Postman, or programming language HTTP library)

## Common Headers

All examples use these headers:
\`\`\`
Authorization: Basic eW91ci11c2VybmFtZTp5b3VyLXBhc3N3b3Jk
X-Correlation-ID: 123e4567-e89b-12d3-a456-************
Content-Type: application/json (for JSON requests)
\`\`\`

## Health Check

### Check API Status
\`\`\`bash
curl -X GET "https://api.example.com/health"
\`\`\`

**Response:**
\`\`\`json
{
  "status": "ok",
  "timestamp": "2025-01-26T10:30:00.000Z",
  "uptime": 3600,
  "environment": "production"
}
\`\`\`

## Quiz Management

### 1. Retrieve Quiz Materials

\`\`\`bash
curl -X GET "https://api.example.com/quiz/f2f/paperless-marking-worked-solutions" \\
  -H "Authorization: Basic eW91ci11c2VybmFtZTp5b3VyLXBhc3N3b3Jk" \\
  -H "X-Correlation-ID: 123e4567-e89b-12d3-a456-************" \\
  -G \\
  -d "grade=12" \\
  -d "subject=Math" \\
  -d "course=3U" \\
  -d "classLevel=A1" \\
  -d "color=R" \\
  -d "year=2025" \\
  -d "term=2" \\
  -d "week=4" \\
  -d "weekType=normal"
\`\`\`

**Response:**
\`\`\`json
{
  "message": "Quiz materials retrieved successfully",
  "data": [
    {
      "id": "550e8400-e29b-41d4-a716-************",
      "grade": 12,
      "subject": "Math",
      "course": "3U",
      "classLevel": "A1",
      "color": "R",
      "year": 2025,
      "term": 2,
      "week": 4,
      "weekType": "normal",
      "gifUrls": [
        {
          "id": "1137",
          "url": "https://storage.example.com/quiz-assets/1137.gif?signature=..."
        }
      ],
      "uploadTimestamp": "2025-01-26T10:00:00.000Z"
    }
  ]
}
\`\`\`

### 2. Upload Quiz Materials

\`\`\`bash
curl -X POST "https://api.example.com/quiz/f2f/paperless-marking-worked-solutions" \\
  -H "Authorization: Basic dG1zLXVzZXJuYW1lOnRtcy1wYXNzd29yZA==" \\
  -H "X-Correlation-ID: 123e4567-e89b-12d3-a456-************" \\
  -F "file=@quiz-materials.zip" \\
  -F "year=2025" \\
  -F "term=2" \\
  -F "week=4" \\
  -F "weekType=normal" \\
  -F "teachingProgram=St George Girls"
\`\`\`

**Response:**
\`\`\`json
{
  "message": "Quiz uploaded and processed successfully",
  "data": {
    "quizId": 123,
    "metadataPath": "quiz-metadata/2025/term-2/week-4/metadata.json",
    "uploadedGifs": [
      {
        "id": "1137",
        "url": "https://storage.example.com/quiz-assets/1137.gif?signature=..."
      },
      {
        "id": "2013",
        "url": "https://storage.example.com/quiz-assets/2013.gif?signature=..."
      }
    ],
    "gifCount": 2,
    "extractedMetadata": {
      "grade": 12,
      "subject": "Math",
      "course": "3U",
      "classLevel": "A1",
      "color": "R",
      "topic": "Trigonometry"
    },
    "uploadTimestamp": "2025-01-26T10:30:00.000Z"
  }
}
\`\`\`

### 3. Update Quiz Materials

\`\`\`bash
curl -X PUT "https://api.example.com/quiz/550e8400-e29b-41d4-a716-************" \\
  -H "Authorization: Basic dG1zLXVzZXJuYW1lOnRtcy1wYXNzd29yZA==" \\
  -H "X-Correlation-ID: 123e4567-e89b-12d3-a456-************" \\
  -F "file=@updated-quiz-materials.zip" \\
  -F "teachingProgram=Updated Program"
\`\`\`

### 4. Delete Quiz Materials

\`\`\`bash
curl -X DELETE "https://api.example.com/quiz/550e8400-e29b-41d4-a716-************" \\
  -H "Authorization: Basic dG1zLXVzZXJuYW1lOnRtcy1wYXNzd29yZA==" \\
  -H "X-Correlation-ID: 123e4567-e89b-12d3-a456-************"
\`\`\`

**Response:**
\`\`\`json
{
  "message": "Quiz deleted successfully",
  "data": {
    "deletedQuizId": "550e8400-e29b-41d4-a716-************",
    "deletedAssets": 2,
    "deletionTimestamp": "2025-01-26T11:00:00.000Z"
  }
}
\`\`\`

## Error Handling Examples

### Validation Error (400)
\`\`\`json
{
  "statusCode": 400,
  "message": "Validation failed",
  "details": [
    {
      "field": "grade",
      "value": "invalid",
      "constraints": {
        "isNumber": "grade must be a number",
        "min": "grade must not be less than 1",
        "max": "grade must not be greater than 12"
      }
    }
  ],
  "correlationId": "123e4567-e89b-12d3-a456-************"
}
\`\`\`

### Not Found Error (404)
\`\`\`json
{
  "statusCode": 404,
  "message": "Quiz not found",
  "details": "No quiz found with ID: 550e8400-e29b-41d4-a716-************",
  "correlationId": "123e4567-e89b-12d3-a456-************"
}
\`\`\`

### File Too Large Error (413)
\`\`\`json
{
  "statusCode": 413,
  "message": "File too large",
  "details": "File size exceeds maximum limit of 50MB",
  "correlationId": "123e4567-e89b-12d3-a456-************"
}
\`\`\`

## Programming Language Examples

### JavaScript (Node.js with Axios)
\`\`\`javascript
const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');

const client = axios.create({
  baseURL: 'https://api.example.com',
  headers: {
    'Authorization': 'Basic ' + Buffer.from('username:password').toString('base64'),
  },
});

// Upload quiz
async function uploadQuiz(filePath) {
  const form = new FormData();
  form.append('file', fs.createReadStream(filePath));
  form.append('year', '2025');
  form.append('term', '2');
  form.append('week', '4');
  form.append('weekType', 'normal');

  const response = await client.post('/quiz/f2f/paperless-marking-worked-solutions', form, {
    headers: {
      ...form.getHeaders(),
      'X-Correlation-ID': uuidv4(),
    },
  });

  return response.data;
}
\`\`\`

### Python with Requests
\`\`\`python
import requests
import uuid
import base64

class TMSClient:
    def __init__(self, base_url, username, password):
        self.base_url = base_url
        credentials = base64.b64encode(f'{username}:{password}'.encode()).decode()
        self.headers = {
            'Authorization': f'Basic {credentials}',
        }

    def upload_quiz(self, file_path, year, term, week, week_type):
        headers = {
            **self.headers,
            'X-Correlation-ID': str(uuid.uuid4()),
        }

        with open(file_path, 'rb') as f:
            files = {'file': f}
            data = {
                'year': year,
                'term': term,
                'week': week,
                'weekType': week_type,
            }

            response = requests.post(
                f'{self.base_url}/quiz/f2f/paperless-marking-worked-solutions',
                headers=headers,
                files=files,
                data=data
            )

        return response.json()

# Usage
client = TMSClient('https://api.example.com', 'username', 'password')
result = client.upload_quiz('quiz.zip', 2025, 2, 4, 'normal')
\`\`\`
`;

  const examplesPath = join(outputDir, 'examples.md');
  writeFileSync(examplesPath, content);
  console.log(`✅ Usage examples generated: ${examplesPath}`);
}

function generateIntegrationGuide(outputDir: string) {
  const content = `# Integration Guide

This guide provides step-by-step instructions for integrating with the TMS REST API.

## Integration Steps

### 1. Environment Setup

#### Development Environment
- **Base URL**: \`http://localhost:3000\`
- **Interactive Docs**: \`http://localhost:3000/api/docs\`
- **Credentials**: Contact your system administrator for development credentials

#### Production Environment
- **Base URL**: \`https://api.tms.example.com\`
- **Credentials**: Provided by system administrator

### 2. Authentication Setup

1. **Obtain Credentials**: Contact your system administrator
2. **Test Authentication**: Use the health endpoint to verify connectivity
3. **Implement Basic Auth**: Add authentication headers to all requests

### 3. Client Implementation

#### HTTP Client Configuration
\`\`\`javascript
// Example configuration
const config = {
  baseURL: process.env.TMS_API_URL,
  timeout: 30000, // 30 seconds
  headers: {
    'Authorization': \`Basic \${Buffer.from(\`\${username}:\${password}\`).toString('base64')}\`,
    'User-Agent': 'MyApp/1.0.0',
  },
};
\`\`\`

#### Error Handling
\`\`\`javascript
try {
  const response = await client.get('/quiz/f2f/paperless-marking-worked-solutions', {
    headers: { 'X-Correlation-ID': generateUUID() },
    params: queryParams,
  });
  return response.data;
} catch (error) {
  if (error.response) {
    // API returned an error response
    console.error('API Error:', error.response.data);
    throw new APIError(error.response.data);
  } else if (error.request) {
    // Network error
    console.error('Network Error:', error.message);
    throw new NetworkError(error.message);
  } else {
    // Other error
    console.error('Error:', error.message);
    throw error;
  }
}
\`\`\`

### 4. File Upload Implementation

#### Multipart Form Data
\`\`\`javascript
const FormData = require('form-data');

async function uploadQuiz(filePath, metadata) {
  const form = new FormData();
  form.append('file', fs.createReadStream(filePath));

  // Add metadata fields
  Object.entries(metadata).forEach(([key, value]) => {
    form.append(key, value);
  });

  const response = await client.post('/quiz/f2f/paperless-marking-worked-solutions', form, {
    headers: {
      ...form.getHeaders(),
      'X-Correlation-ID': generateUUID(),
    },
    maxContentLength: 50 * 1024 * 1024, // 50MB limit
  });

  return response.data;
}
\`\`\`

### 5. Response Processing

#### Success Response Handling
\`\`\`javascript
function processQuizResponse(response) {
  const { message, data } = response;

  console.log('Success:', message);

  if (data.uploadedGifs) {
    console.log(\`Uploaded \${data.gifCount} GIF files\`);
    data.uploadedGifs.forEach(gif => {
      console.log(\`- GIF \${gif.id}: \${gif.url}\`);
    });
  }

  return data;
}
\`\`\`

#### Error Response Handling
\`\`\`javascript
function handleAPIError(error) {
  const { statusCode, message, details, correlationId } = error;

  console.error(\`API Error [\${correlationId}]: \${message}\`);

  switch (statusCode) {
    case 400:
      // Validation error
      if (Array.isArray(details)) {
        details.forEach(detail => {
          console.error(\`- \${detail.field}: \${Object.values(detail.constraints).join(', ')}\`);
        });
      }
      break;
    case 401:
      // Authentication error
      console.error('Check your credentials');
      break;
    case 404:
      // Not found
      console.error('Resource not found');
      break;
    case 413:
      // File too large
      console.error('File size exceeds limit');
      break;
    default:
      console.error('Unexpected error');
  }
}
\`\`\`

## Best Practices

### 1. Request Management

- **Correlation IDs**: Always include unique correlation IDs for request tracing
- **Timeouts**: Set appropriate timeouts (30-60 seconds for file uploads)
- **Retries**: Implement exponential backoff for transient failures
- **Rate Limiting**: Respect API rate limits (if applicable)

### 2. File Handling

- **Validation**: Validate file types and sizes before upload
- **Streaming**: Use streaming for large file uploads
- **Progress**: Implement upload progress tracking
- **Cleanup**: Clean up temporary files after processing

### 3. Security

- **HTTPS**: Always use HTTPS in production
- **Credential Storage**: Store credentials securely (environment variables, key vaults)
- **Logging**: Avoid logging sensitive information
- **Validation**: Validate all API responses

### 4. Monitoring

- **Logging**: Log all API interactions with correlation IDs
- **Metrics**: Track success rates, response times, and error rates
- **Alerting**: Set up alerts for API failures
- **Health Checks**: Regularly check API health

## Testing

### Unit Tests
\`\`\`javascript
describe('TMS API Client', () => {
  it('should upload quiz successfully', async () => {
    const mockResponse = {
      message: 'Quiz uploaded successfully',
      data: { quizId: 123, gifCount: 2 }
    };

    nock('https://api.example.com')
      .post('/quiz/f2f/paperless-marking-worked-solutions')
      .reply(200, mockResponse);

    const result = await client.uploadQuiz('test.zip', metadata);
    expect(result.data.quizId).toBe(123);
  });
});
\`\`\`

### Integration Tests
\`\`\`javascript
describe('TMS API Integration', () => {
  it('should handle complete quiz workflow', async () => {
    // Upload quiz
    const uploadResult = await client.uploadQuiz('test.zip', metadata);
    const quizId = uploadResult.data.quizId;

    // Retrieve quiz
    const retrieveResult = await client.getQuiz(queryParams);
    expect(retrieveResult.data).toHaveLength(1);

    // Update quiz
    const updateResult = await client.updateQuiz(quizId, updateData);
    expect(updateResult.message).toContain('updated');

    // Delete quiz
    const deleteResult = await client.deleteQuiz(quizId);
    expect(deleteResult.message).toContain('deleted');
  });
});
\`\`\`

## Troubleshooting

### Common Issues

1. **401 Unauthorized**
   - Check credentials encoding
   - Verify username/password
   - Ensure Authorization header format

2. **400 Bad Request**
   - Validate request parameters
   - Check required fields
   - Verify data types

3. **413 Payload Too Large**
   - Check file size (max 50MB)
   - Compress files if possible
   - Split large uploads

4. **500 Internal Server Error**
   - Check correlation ID in logs
   - Contact support with correlation ID
   - Retry after delay

### Debug Mode
\`\`\`javascript
// Enable request/response logging
const client = axios.create({
  // ... config
});

client.interceptors.request.use(request => {
  console.log('Request:', request);
  return request;
});

client.interceptors.response.use(
  response => {
    console.log('Response:', response);
    return response;
  },
  error => {
    console.error('Error:', error.response || error);
    return Promise.reject(error);
  }
);
\`\`\`

## Support

For integration support:
1. Check this documentation
2. Review API specification
3. Test with interactive documentation
4. Contact development team with correlation IDs
`;

  const integrationPath = join(outputDir, 'integration-guide.md');
  writeFileSync(integrationPath, content);
  console.log(`✅ Integration guide generated: ${integrationPath}`);
}

// Run the generator
if (require.main === module) {
  void generateDeploymentDocumentation();
}

export { generateDeploymentDocumentation };
