#!/usr/bin/env node

/**
 * Services Startup Script for Tests
 *
 * This script ensures that both PostgreSQL and MinIO services are running before executing tests.
 * It checks if the containers are running and starts them if necessary.
 *
 * Usage: node scripts/ensure-db-running.ts
 */

import { execSync } from 'child_process';

// Configuration
const DB_CONTAINER_NAME = 'tms-postgres-dev-container';
const MINIO_CONTAINER_NAME = 'tms-minio-dev-container';
const DB_SERVICE_NAME = 'postgres';
const MINIO_SERVICE_NAME = 'minio';

interface ServiceConfig {
  name: string;
  container: string;
  displayName: string;
}

const SERVICES: ServiceConfig[] = [
  { name: DB_SERVICE_NAME, container: DB_CONTAINER_NAME, displayName: 'PostgreSQL' },
  { name: MINIO_SERVICE_NAME, container: MINIO_CONTAINER_NAME, displayName: 'MinIO' },
];
const MAX_WAIT_TIME = 30000; // 30 seconds
const CHECK_INTERVAL = 1000; // 1 second

interface CommandResult {
  success: boolean;
  output: string;
  error?: string;
}

interface ExecuteOptions {
  silent?: boolean;
  encoding?: BufferEncoding;
  stdio?: 'pipe' | 'inherit';
}

/**
 * Execute a shell command and return the output
 */
function executeCommand(
  command: string,
  options: ExecuteOptions = {},
): CommandResult {
  try {
    const result = execSync(command, {
      encoding: 'utf8',
      stdio: options.silent ? 'pipe' : 'inherit',
      ...options,
    });
    return { success: true, output: result ? result.trim() : '' };
  } catch (error: unknown) {
    const execError = error as {
      message: string;
      stdout?: Buffer | string;
    };
    return {
      success: false,
      error: execError.message,
      output: execError.stdout ? execError.stdout.toString().trim() : '',
    };
  }
}

/**
 * Check if Docker is available
 */
function isDockerAvailable(): boolean {
  const result = executeCommand('docker --version', { silent: true });
  return result.success;
}

/**
 * Check if a container is running
 */
function isContainerRunning(containerName: string, displayName: string): boolean {
  const result = executeCommand(
    `docker ps --filter "name=${containerName}" --filter "status=running" --format "{{.Names}}"`,
    { silent: true },
  );

  if (result.success && result.output.includes(containerName)) {
    console.log(`✅ ${displayName} container '${containerName}' is running`);
    return true;
  }

  console.log(`❌ ${displayName} container '${containerName}' is not running`);
  return false;
}

/**
 * Check if a container exists (but may be stopped)
 */
function isContainerExists(containerName: string): boolean {
  const result = executeCommand(
    `docker ps -a --filter "name=${containerName}" --format "{{.Names}}"`,
    { silent: true },
  );

  return result.success && result.output.includes(containerName);
}

/**
 * Start services using docker-compose
 */
function startServices(serviceNames: string[]): boolean {
  const serviceList = serviceNames.join(' ');
  console.log(`🚀 Starting services: ${serviceList}...`);

  const result = executeCommand(`docker-compose up -d ${serviceList}`, {
    silent: false,
  });

  if (!result.success) {
    throw new Error(
      `Failed to start services: ${result.error ?? 'Unknown error'}`,
    );
  }

  console.log(`✅ Services started successfully: ${serviceList}`);
  return true;
}

/**
 * Wait for the database to be ready for connections
 */
async function waitForDatabaseReady(): Promise<boolean> {
  console.log('⏳ Waiting for PostgreSQL to be ready for connections...');

  const startTime = Date.now();

  while (Date.now() - startTime < MAX_WAIT_TIME) {
    try {
      // Try to connect to the database using docker exec
      // Use environment variables or development defaults for testing
      const dbUser = process.env.DB_USERNAME || 'dev-user';
      const dbName = process.env.DB_DATABASE || 'tms-dev-postgres-database';
      const result = executeCommand(
        `docker exec ${DB_CONTAINER_NAME} pg_isready -U ${dbUser} -d ${dbName}`,
        { silent: true },
      );

      if (result.success && result.output.includes('accepting connections')) {
        console.log('✅ PostgreSQL is ready for connections');
        return true;
      }
    } catch {
      // Continue waiting
    }

    // Wait before next check
    await new Promise((resolve) => setTimeout(resolve, CHECK_INTERVAL));
    process.stdout.write('.');
  }

  console.log('\n❌ Timeout waiting for PostgreSQL to be ready');
  return false;
}

/**
 * Wait for MinIO to be ready for connections
 */
async function waitForMinIOReady(): Promise<boolean> {
  console.log('⏳ Waiting for MinIO to be ready for connections...');

  const startTime = Date.now();

  while (Date.now() - startTime < MAX_WAIT_TIME) {
    try {
      // Try to check MinIO health endpoint
      const result = executeCommand(
        `docker exec ${MINIO_CONTAINER_NAME} curl -f http://localhost:9000/minio/health/live`,
        { silent: true },
      );

      if (result.success) {
        console.log('✅ MinIO is ready for connections');
        return true;
      }
    } catch {
      // Continue waiting
    }

    // Wait before next check
    await new Promise((resolve) => setTimeout(resolve, CHECK_INTERVAL));
    process.stdout.write('.');
  }

  console.log('\n❌ Timeout waiting for MinIO to be ready');
  return false;
}

/**
 * Main function to ensure all required services are running
 */
async function ensureDatabaseRunning(): Promise<boolean> {
  try {
    console.log('🔍 Ensuring PostgreSQL and MinIO services are running for tests...\n');

    // Check if Docker is available
    if (!isDockerAvailable()) {
      throw new Error(
        'Docker is not available. Please install Docker and try again.',
      );
    }

    // Check which services are running
    const runningServices: ServiceConfig[] = [];
    const stoppedServices: ServiceConfig[] = [];

    for (const service of SERVICES) {
      if (isContainerRunning(service.container, service.displayName)) {
        runningServices.push(service);
      } else {
        stoppedServices.push(service);
      }
    }

    // If all services are running, we're done
    if (stoppedServices.length === 0) {
      console.log('✅ All services are already running and ready for tests\n');
      return true;
    }

    // Start any stopped services
    const servicesToStart: string[] = [];
    for (const service of stoppedServices) {
      if (isContainerExists(service.container)) {
        console.log(
          `📦 ${service.displayName} container exists but is stopped. Will start it...`,
        );
      } else {
        console.log(
          `📦 ${service.displayName} container does not exist. Will create and start it...`,
        );
      }
      servicesToStart.push(service.name);
    }

    // Start all required services
    startServices(servicesToStart);

    // Wait for services to be ready
    const readinessChecks: Promise<boolean>[] = [];

    if (servicesToStart.includes(DB_SERVICE_NAME)) {
      readinessChecks.push(waitForDatabaseReady());
    }

    if (servicesToStart.includes(MINIO_SERVICE_NAME)) {
      readinessChecks.push(waitForMinIOReady());
    }

    const results = await Promise.all(readinessChecks);

    if (results.some(result => !result)) {
      throw new Error(
        'One or more services failed to become ready within the timeout period',
      );
    }

    console.log('✅ All services are now running and ready for tests\n');
    return true;
  } catch (error: unknown) {
    const errorMessage =
      error instanceof Error ? error.message : 'Unknown error';
    console.error('❌ Failed to ensure services are running:', errorMessage);
    process.exit(1);
  }
}

// Run the script if called directly
if (require.main === module) {
  void ensureDatabaseRunning();
}

export { ensureDatabaseRunning };
