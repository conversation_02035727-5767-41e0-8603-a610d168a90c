{"openapi": "3.0.0", "paths": {"/": {"get": {"operationId": "AppController_get<PERSON><PERSON>", "parameters": [{"name": "X-Correlation-ID", "in": "header", "description": "Correlation ID for request tracing (UUID format)", "required": true, "schema": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}}], "responses": {"200": {"description": "Returns hello message"}}, "summary": "Get hello message", "tags": ["General"]}}, "/protected": {"get": {"operationId": "AppController_getProtected", "parameters": [{"name": "X-Correlation-ID", "in": "header", "description": "Correlation ID for request tracing (UUID format)", "required": true, "schema": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}}], "responses": {"200": {"description": "Successfully authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "This is a protected endpoint that requires Basic Auth!"}, "correlationId": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}, "timestamp": {"type": "string", "format": "date-time", "example": "2025-05-26T21:45:23.926Z"}}}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"type": "object", "properties": {"statusCode": {"type": "number", "example": 401}, "message": {"type": "string", "example": "Unauthorized"}, "correlationId": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}, "timestamp": {"type": "string", "format": "date-time", "example": "2025-05-26T21:45:23.926Z"}, "path": {"type": "string", "example": "/protected"}}}}}}}, "security": [{"basic": []}], "summary": "Protected endpoint requiring Basic Auth", "tags": ["General"]}}, "/correlation-test": {"get": {"operationId": "AppController_getCorrelationTest", "parameters": [{"name": "X-Correlation-ID", "in": "header", "description": "Correlation ID for request tracing (UUID format)", "required": true, "schema": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}}], "responses": {"200": {"description": "Returns correlation ID test response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Correlation ID test endpoint"}, "correlationId": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}, "timestamp": {"type": "string", "format": "date-time", "example": "2025-05-26T21:45:23.926Z"}}}}}}}, "summary": "Test correlation ID functionality", "tags": ["General"]}}, "/health": {"get": {"operationId": "HealthController_getHealth", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["Health"]}}, "/quiz/f2f/paperless-marking-worked-solutions": {"get": {"description": "Retrieves worked solutions for one paperless marking quiz. Returns the same data contained in a QzF2f.zip file from Dr<PERSON> <PERSON>, with the solution GIFs translated into pre-signed URLs from MinIO.", "operationId": "QuizController_getQuizWorkedSolutions", "parameters": [{"name": "X-Correlation-ID", "in": "header", "description": "Correlation ID for request tracing (UUID format)", "required": true, "schema": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}}, {"name": "grade", "required": true, "in": "query", "description": "Grade level (1-12)", "schema": {"minimum": 1, "maximum": 12, "example": 12, "type": "integer"}}, {"name": "subject", "required": true, "in": "query", "description": "Subject name (e.g., Math, Phys, Chem, Engl)", "schema": {"minLength": 1, "maxLength": 100, "example": "Math", "type": "string"}}, {"name": "course", "required": true, "in": "query", "description": "Course code (e.g., 3U). For Y9 and Y10 where there is no course, input as empty string", "schema": {"maxLength": 100, "example": "3U", "type": "string"}}, {"name": "classLevel", "required": true, "in": "query", "description": "Class level identifier", "schema": {"minLength": 1, "maxLength": 50, "example": "A1", "type": "string"}}, {"name": "color", "required": true, "in": "query", "description": "Quiz color code (R for red quiz). If there is only one quiz version, it will be R", "schema": {"minLength": 1, "maxLength": 10, "example": "R", "type": "string"}}, {"name": "year", "required": true, "in": "query", "description": "Academic year", "schema": {"minimum": 2000, "maximum": 2100, "example": 2025, "type": "integer"}}, {"name": "term", "required": true, "in": "query", "description": "Academic term (1-4)", "schema": {"minimum": 1, "maximum": 4, "example": 2, "type": "integer"}}, {"name": "week", "required": true, "in": "query", "description": "Week number (1-52)", "schema": {"minimum": 1, "maximum": 52, "example": 4, "type": "integer"}}, {"name": "weekType", "required": true, "in": "query", "description": "Week type", "schema": {"enum": ["normal", "holiday"], "type": "string"}}, {"name": "teachingProgram", "required": false, "in": "query", "description": "Teaching program name (optional)", "schema": {"maxLength": 255, "example": "St George Girls", "type": "string"}}, {"name": "lessonName", "required": false, "in": "query", "description": "Lesson name/topic for reference and logging (optional)", "schema": {"maxLength": 255, "example": "Formulae", "type": "string"}}], "responses": {"200": {"description": "Quiz worked solutions retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/QuizResponseDto"}}}}, "400": {"description": "Bad Request - Validation failed or invalid input", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponseSchema"}}}}, "401": {"description": "Unauthorized - Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthenticationErrorResponseSchema"}}}}, "404": {"description": "Not Found - Resource not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundErrorResponseSchema"}}}}, "500": {"description": "Internal Server Error - Unexpected server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InternalServerErrorResponseSchema"}}}}}, "security": [{"basic": []}], "summary": "Retrieve worked solutions for paperless marking quiz", "tags": ["Quiz"]}, "post": {"description": "Uploads a new F2F paperless marking worked solution quiz. Processes ZIP file containing LessonMetadata.json, QzF2f.json, and solution GIF files.", "operationId": "QuizController_uploadQuizWorkedSolutions", "parameters": [{"name": "X-Correlation-ID", "in": "header", "description": "Correlation ID for request tracing (UUID format)", "required": true, "schema": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}}, {"name": "year", "required": true, "in": "query", "description": "Academic year", "schema": {"minimum": 2000, "maximum": 2100, "example": 2025, "type": "integer"}}, {"name": "term", "required": true, "in": "query", "description": "Academic term (1-4)", "schema": {"minimum": 1, "maximum": 4, "example": 2, "type": "integer"}}, {"name": "week", "required": true, "in": "query", "description": "Week number (1-52)", "schema": {"minimum": 1, "maximum": 52, "example": 4, "type": "integer"}}, {"name": "weekType", "required": true, "in": "query", "description": "Week type", "schema": {"enum": ["normal", "holiday"], "type": "string"}}, {"name": "teachingProgram", "required": false, "in": "query", "description": "Teaching program name (optional)", "schema": {"maxLength": 255, "example": "St George Girls", "type": "string"}}], "responses": {"201": {"description": "Quiz worked solutions uploaded and processed successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/QuizUploadResponseDto"}}}}, "400": {"description": "Bad Request - Validation failed or invalid input", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponseSchema"}}}}, "401": {"description": "Unauthorized - Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthenticationErrorResponseSchema"}}}}, "413": {"description": "Payload Too Large - File size exceeds limit", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FileTooLargeErrorResponseSchema"}}}}, "500": {"description": "Internal Server Error - Unexpected server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InternalServerErrorResponseSchema"}}}}}, "security": [{"basic": []}], "summary": "Upload worked solutions for paperless marking quiz", "tags": ["Quiz"]}}, "/quiz/{id}": {"put": {"description": "Updates an existing quiz with new metadata and optionally replaces the ZIP file. Supports partial updates - only provided fields will be updated.", "operationId": "QuizController_updateQuiz", "parameters": [{"name": "X-Correlation-ID", "in": "header", "description": "Correlation ID for request tracing (UUID format)", "required": true, "schema": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}}, {"name": "id", "required": true, "in": "path", "description": "Quiz UUID to update", "schema": {"example": "123e4567-e89b-12d3-a456-************", "type": "string"}}, {"name": "year", "required": false, "in": "query", "description": "Academic year (optional)", "schema": {"minimum": 2000, "maximum": 2100, "example": 2025, "type": "integer"}}, {"name": "term", "required": false, "in": "query", "description": "Academic term 1-4 (optional)", "schema": {"minimum": 1, "maximum": 4, "example": 2, "type": "integer"}}, {"name": "week", "required": false, "in": "query", "description": "Week number 1-52 (optional)", "schema": {"minimum": 1, "maximum": 52, "example": 4, "type": "integer"}}, {"name": "weekType", "required": false, "in": "query", "description": "Week type (optional)", "schema": {"enum": ["normal", "holiday"], "type": "string"}}, {"name": "teachingProgram", "required": false, "in": "query", "description": "Teaching program name (optional)", "schema": {"maxLength": 255, "example": "St George Girls", "type": "string"}}, {"name": "subject", "required": false, "in": "query", "description": "Subject name (optional)", "schema": {"maxLength": 100, "example": "Math", "type": "string"}}, {"name": "grade", "required": false, "in": "query", "description": "Grade level 1-12 (optional)", "schema": {"minimum": 1, "maximum": 12, "example": 12, "type": "integer"}}, {"name": "course", "required": false, "in": "query", "description": "Course code (optional)", "schema": {"maxLength": 50, "example": "3U", "type": "string"}}, {"name": "classLevel", "required": false, "in": "query", "description": "Class level identifier (optional)", "schema": {"minLength": 1, "maxLength": 50, "example": "A1", "type": "string"}}, {"name": "color", "required": false, "in": "query", "description": "Quiz color code (optional)", "schema": {"minLength": 1, "maxLength": 10, "example": "R", "type": "string"}}, {"name": "lessonName", "required": false, "in": "query", "description": "Lesson name/topic (optional)", "schema": {"maxLength": 255, "example": "Formulae", "type": "string"}}], "responses": {"200": {"description": "Quiz updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/QuizResponseDto"}}}}, "400": {"description": "Bad Request - Validation failed or invalid input", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponseSchema"}}}}, "401": {"description": "Unauthorized - Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthenticationErrorResponseSchema"}}}}, "404": {"description": "Not Found - Resource not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundErrorResponseSchema"}}}}, "413": {"description": "Payload Too Large - File size exceeds limit", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FileTooLargeErrorResponseSchema"}}}}, "500": {"description": "Internal Server Error - Unexpected server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InternalServerErrorResponseSchema"}}}}}, "security": [{"basic": []}], "summary": "Update existing quiz", "tags": ["Quiz"]}, "delete": {"description": "Deletes an existing quiz and all associated assets. Removes quiz from database and cleans up associated files from MinIO storage.", "operationId": "QuizController_deleteQuiz", "parameters": [{"name": "X-Correlation-ID", "in": "header", "description": "Correlation ID for request tracing (UUID format)", "required": true, "schema": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}}, {"name": "id", "required": true, "in": "path", "description": "Quiz UUID to delete", "schema": {"example": "123e4567-e89b-12d3-a456-************", "type": "string"}}], "responses": {"200": {"description": "Quiz deleted successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Quiz with ID 123e4567-e89b-12d3-a456-************ has been successfully deleted"}}}}}}, "400": {"description": "Bad Request - Validation failed or invalid input", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponseSchema"}}}}, "401": {"description": "Unauthorized - Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthenticationErrorResponseSchema"}}}}, "404": {"description": "Not Found - Resource not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundErrorResponseSchema"}}}}, "500": {"description": "Internal Server Error - Unexpected server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InternalServerErrorResponseSchema"}}}}}, "security": [{"basic": []}], "summary": "Delete quiz", "tags": ["Quiz"]}}}, "info": {"title": "Teaching Material System (TMS) REST API", "description": "A centralized service for storing, retrieving, updating, and deleting F2F paperless marking worked solution quiz materials", "version": "0.0.1", "contact": {}}, "tags": [], "servers": [], "components": {"securitySchemes": {"basic": {"type": "http", "scheme": "basic", "description": "Basic Authentication using username and password"}, "correlation-id": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "X-Correlation-ID", "description": "Correlation ID for request tracing (UUID format)"}}, "schemas": {"RetrievedMetadataDto": {"type": "object", "properties": {}}, "QuizResponseDto": {"type": "object", "properties": {"id": {"type": "number", "description": "Unique ID of the quiz", "minimum": 1, "example": 123}, "retrievedMetadata": {"description": "Retrieved metadata combining file content and query parameters", "allOf": [{"$ref": "#/components/schemas/RetrievedMetadataDto"}]}, "gifUrls": {"description": "Array of GIF URLs with their corresponding question IDs", "items": {"type": "array"}, "type": "array"}}, "required": ["id", "retrievedMetadata", "gifUrls"]}, "ValidationErrorResponseSchema": {"type": "object", "properties": {"statusCode": {"type": "number", "description": "HTTP status code", "example": 400}, "message": {"type": "string", "description": "Validation error message", "example": "Validation failed"}, "details": {"type": "array", "description": "Detailed validation error information", "items": {"$ref": "#/components/schemas/ValidationErrorDetailSchema"}}, "correlationId": {"type": "string", "description": "Correlation ID for request tracing (UUID format)", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "timestamp": {"type": "string", "description": "Timestamp when the error occurred", "example": "2025-05-26T21:45:23.926Z", "format": "date-time"}, "path": {"type": "string", "description": "API endpoint path where the error occurred", "example": "/quiz/f2f/paperless-marking-worked-solutions"}}, "required": ["statusCode", "message", "details", "timestamp", "path"]}, "AuthenticationErrorResponseSchema": {"type": "object", "properties": {"statusCode": {"type": "number", "description": "HTTP status code", "example": 401}, "message": {"type": "string", "description": "Authentication error message", "example": "Unauthorized"}, "correlationId": {"type": "string", "description": "Correlation ID for request tracing (UUID format)", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "timestamp": {"type": "string", "description": "Timestamp when the error occurred", "example": "2025-05-26T21:45:23.926Z", "format": "date-time"}, "path": {"type": "string", "description": "API endpoint path where the error occurred", "example": "/protected"}}, "required": ["statusCode", "message", "timestamp", "path"]}, "NotFoundErrorResponseSchema": {"type": "object", "properties": {"statusCode": {"type": "number", "description": "HTTP status code", "example": 404}, "message": {"type": "string", "description": "Not found error message", "example": "No quiz found matching the provided criteria"}, "correlationId": {"type": "string", "description": "Correlation ID for request tracing (UUID format)", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "timestamp": {"type": "string", "description": "Timestamp when the error occurred", "example": "2025-05-26T21:45:23.926Z", "format": "date-time"}, "path": {"type": "string", "description": "API endpoint path where the error occurred", "example": "/quiz/f2f/paperless-marking-worked-solutions"}}, "required": ["statusCode", "message", "timestamp", "path"]}, "InternalServerErrorResponseSchema": {"type": "object", "properties": {"statusCode": {"type": "number", "description": "HTTP status code", "example": 500}, "message": {"type": "string", "description": "Internal server error message", "example": "Internal server error"}, "details": {"type": "string", "description": "Error details for debugging", "example": "Database connection failed"}, "correlationId": {"type": "string", "description": "Correlation ID for request tracing (UUID format)", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "timestamp": {"type": "string", "description": "Timestamp when the error occurred", "example": "2025-05-26T21:45:23.926Z", "format": "date-time"}, "path": {"type": "string", "description": "API endpoint path where the error occurred", "example": "/quiz/f2f/paperless-marking-worked-solutions"}}, "required": ["statusCode", "message", "details", "timestamp", "path"]}, "QuizUploadDataDto": {"type": "object", "properties": {}}, "QuizUploadResponseDto": {"type": "object", "properties": {"message": {"type": "string", "description": "Success message", "minLength": 1, "maxLength": 500, "example": "Quiz uploaded and processed successfully"}, "data": {"description": "Upload data containing metadata path, uploaded GIFs, and count", "allOf": [{"$ref": "#/components/schemas/QuizUploadDataDto"}]}}, "required": ["message", "data"]}, "FileTooLargeErrorResponseSchema": {"type": "object", "properties": {"statusCode": {"type": "number", "description": "HTTP status code", "example": 413}, "message": {"type": "string", "description": "File size error message", "example": "File too large. Maximum size is 10MB."}, "correlationId": {"type": "string", "description": "Correlation ID for request tracing (UUID format)", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "timestamp": {"type": "string", "description": "Timestamp when the error occurred", "example": "2025-05-26T21:45:23.926Z", "format": "date-time"}, "path": {"type": "string", "description": "API endpoint path where the error occurred", "example": "/quiz/f2f/paperless-marking-worked-solutions"}}, "required": ["statusCode", "message", "timestamp", "path"]}, "ErrorResponseSchema": {"type": "object", "properties": {"statusCode": {"type": "number", "description": "HTTP status code", "example": 400}, "message": {"type": "string", "description": "Error message describing what went wrong", "example": "Validation failed"}, "details": {"description": "Additional error details (optional)", "example": "Invalid file type. Expected ZIP file.", "oneOf": [{"type": "string"}, {"type": "object"}, {"type": "array"}]}, "correlationId": {"type": "string", "description": "Correlation ID for request tracing (UUID format)", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "timestamp": {"type": "string", "description": "Timestamp when the error occurred", "example": "2025-05-26T21:45:23.926Z", "format": "date-time"}, "path": {"type": "string", "description": "API endpoint path where the error occurred", "example": "/quiz/f2f/paperless-marking-worked-solutions"}}, "required": ["statusCode", "message", "timestamp", "path"]}, "ValidationErrorDetailSchema": {"type": "object", "properties": {"property": {"type": "string", "description": "Property name that failed validation", "example": "grade"}, "value": {"type": "object", "description": "Invalid value that was provided", "example": "invalid"}, "constraints": {"type": "array", "description": "Array of constraint violation messages", "example": ["Grade must be an integer"], "items": {"type": "string"}}}, "required": ["property", "value", "constraints"]}}}}