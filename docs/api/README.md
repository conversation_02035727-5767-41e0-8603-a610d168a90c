# Teaching Material System (TMS) REST API

**Version:** 0.0.1 | **OpenAPI:** 3.0.0

A centralized service for storing, retrieving, updating, and deleting F2F paperless marking worked solution quiz materials.

## 📋 Overview

The TMS REST API provides a comprehensive solution for managing educational quiz materials with the following key features:

- **Quiz Management**: Upload, retrieve, update, and delete quiz materials
- **File Storage**: Secure storage of quiz assets (GIF files) with MinIO
- **Metadata Extraction**: Automatic extraction of quiz metadata from uploaded ZIP files
- **Authentication**: Basic HTTP authentication for secure access
- **Request Tracing**: Correlation ID support for request tracking
- **Comprehensive Validation**: Input validation with detailed error messages


## 📊 API Statistics

- **Endpoints**: 6
- **Data Models**: 11
- **Security Schemes**: 2
- **Last Updated**: 5/28/2025, 2:24:28 PM


## 🚀 Quick Start

### 1. Authentication

All API endpoints (except health check) require Basic HTTP authentication:

```bash
# Base64 encode your credentials
echo -n "username:password" | base64

# Use in requests
curl -H "Authorization: Basic <base64-credentials>" \
     -H "X-Correlation-ID: $(uuidgen)" \
     https://api.example.com/quiz/f2f/paperless-marking-worked-solutions
```

### 2. Required Headers

- **Authorization**: `Basic <base64-credentials>`
- **X-Correlation-ID**: UUID for request tracing

### 3. Base URL

```
Production: https://api.tms.example.com
Development: http://localhost:3000
```

## 📖 Documentation

- **[Authentication Guide](./authentication.md)** - Detailed authentication setup
- **[Usage Examples](./examples.md)** - Code examples and use cases
- **[Integration Guide](./integration-guide.md)** - Step-by-step integration
- **[OpenAPI Specification](./api-spec.json)** - Complete API specification
- **[Interactive Documentation](http://localhost:3000/api/docs)** - Swagger UI (development)

## 🔗 Available Endpoints

### Quiz Management

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/quiz/f2f/paperless-marking-worked-solutions` | Retrieve quiz materials |
| POST | `/quiz/f2f/paperless-marking-worked-solutions` | Upload new quiz materials |
| PUT | `/quiz/{id}` | Update existing quiz |
| DELETE | `/quiz/{id}` | Delete quiz materials |

### System

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/health` | Health check (no auth required) |

## 📝 Request/Response Format

### Successful Response
```json
{
  "message": "Success message",
  "data": {
    // Response data
  }
}
```

### Error Response
```json
{
  "statusCode": 400,
  "message": "Error description",
  "details": "Additional error details",
  "correlationId": "123e4567-e89b-12d3-a456-426614174000"
}
```

## 🛠️ Development

### Local Setup

1. Clone the repository
2. Install dependencies: `npm install`
3. Start services: `npm run docker:up`
4. Run the API: `npm run start:dev`
5. Access documentation: http://localhost:3000/api/docs

### Testing

```bash
# Run all tests
npm run test:all

# Run API documentation tests
npm run test:api-docs

# Generate API specification
npm run api:generate

# Validate API specification
npm run api:validate
```

## 📞 Support

For questions, issues, or feature requests, please contact the development team or create an issue in the project repository.

---

*Generated on 2025-05-28T04:24:37.672Z*
