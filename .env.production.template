# Production Environment Configuration Template for TMS REST API
# Copy this file to .env.production and replace all placeholder values
# SECURITY: Generate strong, unique credentials for each environment

# Application
NODE_ENV=production
PORT=3000

# Database Configuration
# CRITICAL: DB_SYNC=false prevents data loss on restart
DB_HOST=your-database-host
DB_PORT=5432
DB_USERNAME=your-secure-db-username-32chars-min
DB_PASSWORD=your-secure-db-password-32chars-min
DB_DATABASE=your-production-database-name
DB_SYNC=false
DB_LOGGING=false

# MinIO Object Storage Configuration
# CRITICAL: MINIO_USE_SSL=true for production security
MINIO_ENDPOINT=your-minio-host
MINIO_PORT=9000
MINIO_USE_SSL=true
MINIO_ACCESS_KEY=your-secure-minio-access-key-32chars-min
MINIO_SECRET_KEY=your-secure-minio-secret-key-32chars-min
MINIO_DEFAULT_BUCKET=your-production-bucket-name

# API Authentication
# CRITICAL: Strong credentials for API access
AUTH_USERNAME=your-secure-api-username-32chars-min
AUTH_PASSWORD=your-secure-api-password-32chars-min

# Security Configuration
CORS_ORIGIN=https://your-domain.com
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=1000
SESSION_SECRET=your-secure-session-secret-64chars-min

# Environment-based Rate Limiting
# Test/Development Environment (higher limits for comprehensive testing)
RATE_LIMIT_AUTH_TEST=500
RATE_LIMIT_UPLOAD_TEST=500
# Production Environment (secure limits for protection)
RATE_LIMIT_AUTH_PROD=100
RATE_LIMIT_UPLOAD_PROD=100

# Logging Configuration
LOG_LEVEL=warn
LOG_FORMAT=json
LOG_FILE_PATH=/var/log/tms-api/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# Health Check Configuration
HEALTH_CHECK_TIMEOUT=5000
HEALTH_CHECK_INTERVAL=30000

# SSL/TLS Configuration
TRUST_PROXY=true
SECURE_COOKIES=true

# Instructions for generating secure credentials:
# 1. Use a password manager or secure random generator
# 2. Minimum 32 characters for passwords
# 3. Include uppercase, lowercase, numbers, and symbols
# 4. Never reuse credentials across environments
# 5. Rotate credentials regularly (quarterly recommended)
