
> tms-api@0.0.1 test:e2e
> jest --config ./test/jest-e2e.json --forceExit --verbose --no-coverage

Running global setup...
Global setup completed
PASS test/utils/database-cleanup.e2e-spec.ts
  Database Cleanup Utilities
    cleanupDatabaseData
      ✓ should clean database data without closing connections (22 ms)
      ✓ should handle null dataSource gracefully (1 ms)
      ✓ should handle undefined dataSource gracefully
    cleanupMinIOBucket
      ✓ should clean all files from MinIO bucket (24 ms)
      ✓ should handle null minioService gracefully (3 ms)
      ✓ should handle undefined minioService gracefully (2 ms)
    cleanupTestData
      ✓ should clean both database and MinIO data (26 ms)
      ✓ should handle null parameters gracefully (1 ms)
      ✓ should handle undefined parameters gracefully (2 ms)
    backward compatibility
      ✓ should preserve existing cleanupDatabase function behavior

  console.log
    == PERFORMANCE TEST SETUP ==

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:95:13)

  console.log
    Performance testing environment initialized

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:96:13)

  console.log
    Test data directory: /Users/<USER>/tms/test-data/quiz-zip-files

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:97:13)

  console.log
    Real database and MinIO connections established

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:98:13)

  console.log
    ================================

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:99:13)

  console.log
    == BATCH UPLOAD PERFORMANCE TEST ==

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:340:15)

  console.log
    Testing batch upload of 20 files

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:341:15)

  console.log
    Expected: >80% success rate, <10s average response time

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:342:15)

  console.log
    =========================================

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:343:15)

  console.log
    Processing file 1/20: Dr Du_Math_2U_V6 (2024)_Y11_TF(II)_A_for_Y11_2U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:353:17)

PASS test/app.e2e-spec.ts
  AppController (e2e)
    ✓ / (GET) (12 ms)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:95:15)

  console.log
    Testing complete upload workflow with real ZIP file

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:96:15)

  console.log
    ZIP file size: 543352 bytes

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:97:15)

  console.log
    ZIP file path: /Users/<USER>/tms/test-data/quiz-zip-files/Dr Du_Math__V2_Y09_Trigonometry_A1.R_for_Y09__F2F QZ.zip

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:98:15)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:99:15)

PASS test/health.e2e-spec.ts
  Health Endpoint (e2e)
    ✓ /health (GET) should return health status with database information (22 ms)
    ✓ should return the correct content type (8 ms)
    ✓ should respond quickly (under 500ms) (3 ms)

  console.log
    ✅ Dr Du_Math_2U_V6 (2024)_Y11_TF(II)_A_for_Y11_2U_F2F QZ.zip - 56ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:361:19)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:173:15)

  console.log
    Upload successful - verifying response data

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:174:15)

  console.log
    Quiz ID created: 282027827

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:175:15)

  console.log
    GIF count: 4

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:176:15)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:177:15)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:188:15)

  console.log
    Response verification completed successfully

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:189:15)

  console.log
    Database verification skipped due to test cleanup timing

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:190:15)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:191:15)

  console.log
    Processing file 2/20: Dr Du_Math_2U_V6 (2024)_Y12_TE(I)_A_for_Y12_2U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:353:17)

  console.log
    ✅ Dr Du_Math_2U_V6 (2024)_Y12_TE(I)_A_for_Y12_2U_F2F QZ.zip - 46ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:361:19)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:198:15)

  console.log
    Testing quiz upload endpoint validation

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:199:15)

  console.log
    Note: Authentication is tested separately in auth.integration.spec.ts

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:200:15)

  console.log
    This test verifies that the quiz upload endpoint properly validates parameters

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:203:15)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:206:15)

[31m[Nest] 15108  - [39m05/26/2025, 11:52:46 PM [31m  ERROR[39m [38;5;3m[QuizService] [39m[31mError processing quiz upload for correlation ID: 123e4567-e89b-12d3-a456-426614174000[39m
PASS test/utils/test-module-factory.e2e-spec.ts
  Enhanced Test Module Factory
    createTestModule with cleanup options
      ✓ should create module without cleanup options (backward compatibility) (86 ms)
      ✓ should create module with MinIO when includeMinIO is true (43 ms)
      ✓ should not include MinIO when includeMinIO is false (45 ms)
    createTestApp with cleanup utilities
      ✓ should create test app with cleanup utilities when enableDataCleanup is true (149 ms)
      ✓ should work with custom cleanup function (37 ms)
    createE2ETestApp backward compatibility
      ✓ should maintain backward compatibility when no cleanup options provided (34 ms)
      ✓ should include cleanup utilities when enableDataCleanup is true (32 ms)
      ✓ should include cleanup utilities when withDataCleanup is true (TODOS.md specification) (33 ms)
    createCleanupHooks helper
      ✓ should create hooks for test app with cleanup utilities (104 ms)
      ✓ should create fallback hooks for backward compatibility (31 ms)

[31m[Nest] 15108  - [39m05/26/2025, 11:52:46 PM [31m  ERROR[39m [38;5;3m[QuizService] [39mQueryFailedError: invalid input value for enum quizzes_weektype_enum: "invalid"
    at PostgresQueryRunner.query [90m(/Users/<USER>/tms/tms-api/[39msrc/driver/postgres/PostgresQueryRunner.ts:325:19[90m)[39m
[90m    at processTicksAndRejections (node:internal/process/task_queues:105:5)[39m
    at InsertQueryBuilder.execute [90m(/Users/<USER>/tms/tms-api/[39msrc/query-builder/InsertQueryBuilder.ts:163:33[90m)[39m
    at SubjectExecutor.executeInsertOperations [90m(/Users/<USER>/tms/tms-api/[39msrc/persistence/SubjectExecutor.ts:435:42[90m)[39m
    at SubjectExecutor.execute [90m(/Users/<USER>/tms/tms-api/[39msrc/persistence/SubjectExecutor.ts:137:9[90m)[39m
    at EntityPersistExecutor.execute [90m(/Users/<USER>/tms/tms-api/[39msrc/persistence/EntityPersistExecutor.ts:182:21[90m)[39m
    at QuizService.uploadQuizWorkedSolutions [90m(/Users/<USER>/tms/tms-api/[39msrc/quiz/quiz.service.ts:309:25[90m)[39m
    at QuizController.uploadQuizWorkedSolutions [90m(/Users/<USER>/tms/tms-api/[39msrc/quiz/quiz.controller.ts:145:30[90m)[39m {
  query: [32m'INSERT INTO "quizzes"("id", "created_at", "updated_at", "deleted_at", "created_by", "updated_by", "subject", "grade", "classLevel", "lessonName", "year", "term", "week", "weekType", "course", "teachingProgram", "category", "versionType", "original_filename", "upload_timestamp", "internalMetadata", "quizType", "color", "total_marks") VALUES (DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, DEFAULT) RETURNING "id", "created_at", "updated_at", "deleted_at", "weekType", "quizType"'[39m,
  parameters: [
    [32m'Math'[39m,
    [33m9[39m,
    [32m'A1'[39m,
    [32m'Trigonometry'[39m,
    [32m'1999'[39m,
    [32m'5'[39m,
    [32m'60'[39m,
    [32m'invalid'[39m,
    [32m''[39m,
    [32m''[39m,
    [32m'quizzes'[39m,
    [32m'worked-solutions'[39m,
    [32m'test.zip'[39m,
    [35m2025-05-26T13:52:46.125Z[39m,
    [32m'[{"questionId":"2013","questionNumber":"1","smilFile":"Q2013.smil","marksJson":"[{\\"index\\": \\"0\\", \\"mark\\": 0}]"},{"questionId":"2013","questionNumber":"2(a)","smilFile":"Q2013.smil","marksJson":"[{\\"index\\": \\"0\\", \\"mark\\": 65}]"},{"questionId":"2013","questionNumber":"2(b)","smilFile":"Q2013.smil","marksJson":"[{\\"index\\": \\"0\\", \\"mark\\": 5}]"},{"questionId":"1137","questionNumber":"2(c)","smilFile":"Q1137.smil","marksJson":"[{\\"index\\": \\"0.0\\", \\"mark\\": 2}, {\\"index\\": \\"0.1\\", \\"mark\\": 3}]"},{"questionId":"2013","questionNumber":"3(a)","smilFile":"Q2013.smil","marksJson":"[{\\"index\\": \\"0\\", \\"mark\\": 5}]"},{"questionId":"2","questionNumber":"3(b)","smilFile":"Q2.smil","marksJson":"[{\\"index\\": \\"0.0\\", \\"mark\\": 1}, {\\"index\\": \\"0.1.0\\", \\"mark\\": 2}, {\\"index\\": \\"0.1.1\\", \\"mark\\": 3}, {\\"index\\": \\"*******\\", \\"mark\\": 4}]"},{"questionId":"2018","questionNumber":"3(c)","smilFile":"Q2018.smil","marksJson":"[{\\"index\\": \\"0.0.0\\", \\"mark\\": 0}, {\\"index\\": \\"0.0.1\\", \\"mark\\": 0}, {\\"index\\": \\"0.1.0\\", \\"mark\\": 0}, {\\"index\\": \\"0.1.1.0\\", \\"mark\\": 0}, {\\"index\\": \\"0.1.1.1\\", \\"mark\\": 0}, {\\"index\\": \\"0.1.2\\", \\"mark\\": 0}, {\\"index\\": \\"0.1.3\\", \\"mark\\": 0}, {\\"index\\": \\"0.1.4\\", \\"mark\\": 0}]"},{"questionId":"2","questionNumber":"3(d)(i)","smilFile":"Q2.smil","marksJson":"[{\\"index\\": \\"0.0\\", \\"mark\\": 4}, {\\"index\\": \\"0.1.0\\", \\"mark\\": 3}, {\\"index\\": \\"0.1.1\\", \\"mark\\": 2}, {\\"index\\": \\"*******\\", \\"mark\\": 1}]"},{"questionId":"2013","questionNumber":"4","smilFile":"Q2013.smil","marksJson":"[{\\"index\\": \\"0\\", \\"mark\\": 10}]"}]'[39m,
    [32m'f2f'[39m,
    [32m'R'[39m
  ],
  driverError: error: invalid input value for enum quizzes_weektype_enum: "invalid"
      at [90m/Users/<USER>/tms/tms-api/[39mnode_modules/[4mpg[24m/lib/client.js:545:17
  [90m    at processTicksAndRejections (node:internal/process/task_queues:105:5)[39m
      at PostgresQueryRunner.query [90m(/Users/<USER>/tms/tms-api/[39msrc/driver/postgres/PostgresQueryRunner.ts:254:25[90m)[39m
      at InsertQueryBuilder.execute [90m(/Users/<USER>/tms/tms-api/[39msrc/query-builder/InsertQueryBuilder.ts:163:33[90m)[39m
      at SubjectExecutor.executeInsertOperations [90m(/Users/<USER>/tms/tms-api/[39msrc/persistence/SubjectExecutor.ts:435:42[90m)[39m
      at SubjectExecutor.execute [90m(/Users/<USER>/tms/tms-api/[39msrc/persistence/SubjectExecutor.ts:137:9[90m)[39m
      at EntityPersistExecutor.execute [90m(/Users/<USER>/tms/tms-api/[39msrc/persistence/EntityPersistExecutor.ts:182:21[90m)[39m
      at QuizService.uploadQuizWorkedSolutions [90m(/Users/<USER>/tms/tms-api/[39msrc/quiz/quiz.service.ts:309:25[90m)[39m
      at QuizController.uploadQuizWorkedSolutions [90m(/Users/<USER>/tms/tms-api/[39msrc/quiz/quiz.controller.ts:145:30[90m)[39m {
    length: [33m148[39m,
    severity: [32m'ERROR'[39m,
    code: [32m'22P02'[39m,
    detail: [90mundefined[39m,
    hint: [90mundefined[39m,
    position: [90mundefined[39m,
    internalPosition: [90mundefined[39m,
    internalQuery: [90mundefined[39m,
    where: [32m"unnamed portal parameter $8 = '...'"[39m,
    schema: [90mundefined[39m,
    table: [90mundefined[39m,
    column: [90mundefined[39m,
    dataType: [90mundefined[39m,
    constraint: [90mundefined[39m,
    file: [32m'enum.c'[39m,
    line: [32m'129'[39m,
    routine: [32m'enum_in'[39m
  },
  length: [33m148[39m,
  severity: [32m'ERROR'[39m,
  code: [32m'22P02'[39m,
  detail: [90mundefined[39m,
  hint: [90mundefined[39m,
  position: [90mundefined[39m,
  internalPosition: [90mundefined[39m,
  internalQuery: [90mundefined[39m,
  where: [32m"unnamed portal parameter $8 = '...'"[39m,
  schema: [90mundefined[39m,
  table: [90mundefined[39m,
  column: [90mundefined[39m,
  dataType: [90mundefined[39m,
  constraint: [90mundefined[39m,
  file: [32m'enum.c'[39m,
  line: [32m'129'[39m,
  routine: [32m'enum_in'[39m
}
[31m[Nest] 15108  - [39m05/26/2025, 11:52:46 PM [31m  ERROR[39m [38;5;3m[QuizController] [39m[31mError processing quiz upload for correlation ID: 123e4567-e89b-12d3-a456-426614174000[39m
[31m[Nest] 15108  - [39m05/26/2025, 11:52:46 PM [31m  ERROR[39m [38;5;3m[QuizController] [39mInternalServerErrorException: An error occurred while processing the quiz upload
    at QuizService.uploadQuizWorkedSolutions [90m(/Users/<USER>/tms/tms-api/[39msrc/quiz/quiz.service.ts:368:13[90m)[39m
[90m    at processTicksAndRejections (node:internal/process/task_queues:105:5)[39m
    at QuizController.uploadQuizWorkedSolutions [90m(/Users/<USER>/tms/tms-api/[39msrc/quiz/quiz.controller.ts:145:30[90m)[39m {
  response: {
    message: [32m'An error occurred while processing the quiz upload'[39m,
    error: [32m'Internal Server Error'[39m,
    statusCode: [33m500[39m
  },
  status: [33m500[39m,
  options: {}
}
  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:230:17)

  console.log
    Expected 400 but got 500 - validation may be happening at database level

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:231:17)

  console.log
    This indicates the validation pipe may not be properly applied to query parameters

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:234:17)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:237:17)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:240:15)

  console.log
    Quiz upload validation test completed successfully

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:241:15)

  console.log
    Response status: 500

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:242:15)

  console.log
    Validation is working correctly

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:243:15)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:244:15)

  console.log
    Processing file 3/20: Dr Du_Math_3U_V6 (2023)_Y11_Binomial Expansion_B.R_for_Y11_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:353:17)

  console.log
    ✅ Dr Du_Math_3U_V6 (2023)_Y11_Binomial Expansion_B.R_for_Y11_3U_F2F QZ.zip - 29ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:361:19)

  console.log
    Processing file 4/20: Dr Du_Math_3U_V6 (2023)_Y11_Binomial Expansion_B.Y_for_Y11_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:353:17)

  console.log
    ✅ Dr Du_Math_3U_V6 (2023)_Y11_Binomial Expansion_B.Y_for_Y11_3U_F2F QZ.zip - 36ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:361:19)

  console.log
    Processing file 5/20: Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A1.R_for_Y11_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:353:17)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:323:15)

  console.log
    Testing invalid ZIP structure handling

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:324:15)

  console.log
    Expected: 400 Bad Request with proper error message

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:325:15)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:326:15)

[31m[Nest] 15108  - [39m05/26/2025, 11:52:46 PM [31m  ERROR[39m [38;5;3m[QuizService] [39m[31mError opening ZIP file for correlation ID: 123e4567-e89b-12d3-a456-426614174000[39m
[31m[Nest] 15108  - [39m05/26/2025, 11:52:46 PM [31m  ERROR[39m [38;5;3m[QuizService] [39mError: End of central directory record signature not found. Either not a zip file, or file is truncated.
    at [90m/Users/<USER>/tms/tms-api/[39mnode_modules/[4myauzl[24m/index.js:190:14
    at [90m/Users/<USER>/tms/tms-api/[39mnode_modules/[4myauzl[24m/index.js:772:5
    at Immediate._onImmediate [90m(/Users/<USER>/tms/tms-api/[39mnode_modules/[4myauzl[24m/fd-slicer.js:220:5[90m)[39m
[90m    at processImmediate (node:internal/timers:491:21)[39m
[31m[Nest] 15108  - [39m05/26/2025, 11:52:46 PM [31m  ERROR[39m [38;5;3m[QuizService] [39m[31mError processing quiz upload for correlation ID: 123e4567-e89b-12d3-a456-426614174000[39m
[31m[Nest] 15108  - [39m05/26/2025, 11:52:46 PM [31m  ERROR[39m [38;5;3m[QuizService] [39mBadRequestException: Invalid ZIP file format
    at [90m/Users/<USER>/tms/tms-api/[39msrc/quiz/quiz.service.ts:404:27
    at [90m/Users/<USER>/tms/tms-api/[39mnode_modules/[4myauzl[24m/index.js:190:5
    at [90m/Users/<USER>/tms/tms-api/[39mnode_modules/[4myauzl[24m/index.js:772:5
    at Immediate.<anonymous> [90m(/Users/<USER>/tms/tms-api/[39mnode_modules/[4myauzl[24m/fd-slicer.js:220:5[90m)[39m
[90m    at processImmediate (node:internal/timers:491:21)[39m {
  response: {
    message: [32m'Invalid ZIP file format'[39m,
    error: [32m'Bad Request'[39m,
    statusCode: [33m400[39m
  },
  status: [33m400[39m,
  options: {}
}
[31m[Nest] 15108  - [39m05/26/2025, 11:52:46 PM [31m  ERROR[39m [38;5;3m[QuizController] [39m[31mError processing quiz upload for correlation ID: 123e4567-e89b-12d3-a456-426614174000[39m
[31m[Nest] 15108  - [39m05/26/2025, 11:52:46 PM [31m  ERROR[39m [38;5;3m[QuizController] [39mBadRequestException: Invalid ZIP file format
    at [90m/Users/<USER>/tms/tms-api/[39msrc/quiz/quiz.service.ts:404:27
    at [90m/Users/<USER>/tms/tms-api/[39mnode_modules/[4myauzl[24m/index.js:190:5
    at [90m/Users/<USER>/tms/tms-api/[39mnode_modules/[4myauzl[24m/index.js:772:5
    at Immediate.<anonymous> [90m(/Users/<USER>/tms/tms-api/[39mnode_modules/[4myauzl[24m/fd-slicer.js:220:5[90m)[39m
[90m    at processImmediate (node:internal/timers:491:21)[39m {
  response: {
    message: [32m'Invalid ZIP file format'[39m,
    error: [32m'Bad Request'[39m,
    statusCode: [33m400[39m
  },
  status: [33m400[39m,
  options: {}
}
  console.log
    ✅ Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A1.R_for_Y11_3U_F2F QZ.zip - 42ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:361:19)

  console.log
    Processing file 6/20: Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A1.Y_for_Y11_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:353:17)

[31m[Nest] 15108  - [39m05/26/2025, 11:52:46 PM [31m  ERROR[39m [38;5;3m[QuizService] [39m[31mError opening ZIP file for correlation ID: 123e4567-e89b-12d3-a456-426614174000[39m
[31m[Nest] 15108  - [39m05/26/2025, 11:52:46 PM [31m  ERROR[39m [38;5;3m[QuizService] [39mError: End of central directory record signature not found. Either not a zip file, or file is truncated.
    at [90m/Users/<USER>/tms/tms-api/[39mnode_modules/[4myauzl[24m/index.js:190:14
    at [90m/Users/<USER>/tms/tms-api/[39mnode_modules/[4myauzl[24m/index.js:772:5
    at Immediate._onImmediate [90m(/Users/<USER>/tms/tms-api/[39mnode_modules/[4myauzl[24m/fd-slicer.js:220:5[90m)[39m
[90m    at processImmediate (node:internal/timers:491:21)[39m
[31m[Nest] 15108  - [39m05/26/2025, 11:52:46 PM [31m  ERROR[39m [38;5;3m[QuizService] [39m[31mError processing quiz upload for correlation ID: 123e4567-e89b-12d3-a456-426614174000[39m
[31m[Nest] 15108  - [39m05/26/2025, 11:52:46 PM [31m  ERROR[39m [38;5;3m[QuizService] [39mBadRequestException: Invalid ZIP file format
    at [90m/Users/<USER>/tms/tms-api/[39msrc/quiz/quiz.service.ts:404:27
    at [90m/Users/<USER>/tms/tms-api/[39mnode_modules/[4myauzl[24m/index.js:190:5
    at [90m/Users/<USER>/tms/tms-api/[39mnode_modules/[4myauzl[24m/index.js:772:5
    at Immediate.<anonymous> [90m(/Users/<USER>/tms/tms-api/[39mnode_modules/[4myauzl[24m/fd-slicer.js:220:5[90m)[39m
[90m    at processImmediate (node:internal/timers:491:21)[39m {
  response: {
    message: [32m'Invalid ZIP file format'[39m,
    error: [32m'Bad Request'[39m,
    statusCode: [33m400[39m
  },
  status: [33m400[39m,
  options: {}
}
[31m[Nest] 15108  - [39m05/26/2025, 11:52:46 PM [31m  ERROR[39m [38;5;3m[QuizController] [39m[31mError processing quiz upload for correlation ID: 123e4567-e89b-12d3-a456-426614174000[39m
[31m[Nest] 15108  - [39m05/26/2025, 11:52:46 PM [31m  ERROR[39m [38;5;3m[QuizController] [39mBadRequestException: Invalid ZIP file format
    at [90m/Users/<USER>/tms/tms-api/[39msrc/quiz/quiz.service.ts:404:27
    at [90m/Users/<USER>/tms/tms-api/[39mnode_modules/[4myauzl[24m/index.js:190:5
    at [90m/Users/<USER>/tms/tms-api/[39mnode_modules/[4myauzl[24m/index.js:772:5
    at Immediate.<anonymous> [90m(/Users/<USER>/tms/tms-api/[39mnode_modules/[4myauzl[24m/fd-slicer.js:220:5[90m)[39m
[90m    at processImmediate (node:internal/timers:491:21)[39m {
  response: {
    message: [32m'Invalid ZIP file format'[39m,
    error: [32m'Bad Request'[39m,
    statusCode: [33m400[39m
  },
  status: [33m400[39m,
  options: {}
}
  console.log
    ✅ Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A1.Y_for_Y11_3U_F2F QZ.zip - 37ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:361:19)

  console.log
    Processing file 7/20: Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A2.R_for_Y11_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:353:17)

[31m[Nest] 15108  - [39m05/26/2025, 11:52:46 PM [31m  ERROR[39m [38;5;3m[QuizController] [39m[31mError processing quiz upload for correlation ID: 123e4567-e89b-12d3-a456-426614174000[39m
[31m[Nest] 15108  - [39m05/26/2025, 11:52:46 PM [31m  ERROR[39m [38;5;3m[QuizController] [39mHttpException: Invalid file type. Only ZIP files are allowed.
    at QuizController.uploadQuizWorkedSolutions [90m(/Users/<USER>/tms/tms-api/[39msrc/quiz/quiz.controller.ts:134:15[90m)[39m
    at [90m/Users/<USER>/tms/tms-api/[39mnode_modules/[4m@nestjs[24m/core/router/router-execution-context.js:38:29
[90m    at processTicksAndRejections (node:internal/process/task_queues:105:5)[39m {
  response: [32m'Invalid file type. Only ZIP files are allowed.'[39m,
  status: [33m400[39m,
  options: [90mundefined[39m
}
  console.log
    ✅ Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A2.R_for_Y11_3U_F2F QZ.zip - 44ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:361:19)

  console.log
    Processing file 8/20: Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A2.Y_for_Y11_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:353:17)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:406:15)

  console.log
    Testing transaction rollback on failures

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:407:15)

  console.log
    Initial quiz count: 0

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:408:15)

  console.log
    Initial asset count: 0

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:409:15)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:410:15)

[31m[Nest] 15108  - [39m05/26/2025, 11:52:46 PM [31m  ERROR[39m [38;5;3m[QuizService] [39m[31mError opening ZIP file for correlation ID: 123e4567-e89b-12d3-a456-426614174000[39m
[31m[Nest] 15108  - [39m05/26/2025, 11:52:46 PM [31m  ERROR[39m [38;5;3m[QuizService] [39mError: End of central directory record signature not found. Either not a zip file, or file is truncated.
    at [90m/Users/<USER>/tms/tms-api/[39mnode_modules/[4myauzl[24m/index.js:190:14
    at [90m/Users/<USER>/tms/tms-api/[39mnode_modules/[4myauzl[24m/index.js:772:5
    at Immediate._onImmediate [90m(/Users/<USER>/tms/tms-api/[39mnode_modules/[4myauzl[24m/fd-slicer.js:220:5[90m)[39m
[90m    at processImmediate (node:internal/timers:491:21)[39m
[31m[Nest] 15108  - [39m05/26/2025, 11:52:46 PM [31m  ERROR[39m [38;5;3m[QuizService] [39m[31mError processing quiz upload for correlation ID: 123e4567-e89b-12d3-a456-426614174000[39m
[31m[Nest] 15108  - [39m05/26/2025, 11:52:46 PM [31m  ERROR[39m [38;5;3m[QuizService] [39mBadRequestException: Invalid ZIP file format
    at [90m/Users/<USER>/tms/tms-api/[39msrc/quiz/quiz.service.ts:404:27
    at [90m/Users/<USER>/tms/tms-api/[39mnode_modules/[4myauzl[24m/index.js:190:5
    at [90m/Users/<USER>/tms/tms-api/[39mnode_modules/[4myauzl[24m/index.js:772:5
    at Immediate.<anonymous> [90m(/Users/<USER>/tms/tms-api/[39mnode_modules/[4myauzl[24m/fd-slicer.js:220:5[90m)[39m
[90m    at processImmediate (node:internal/timers:491:21)[39m {
  response: {
    message: [32m'Invalid ZIP file format'[39m,
    error: [32m'Bad Request'[39m,
    statusCode: [33m400[39m
  },
  status: [33m400[39m,
  options: {}
}
[31m[Nest] 15108  - [39m05/26/2025, 11:52:46 PM [31m  ERROR[39m [38;5;3m[QuizController] [39m[31mError processing quiz upload for correlation ID: 123e4567-e89b-12d3-a456-426614174000[39m
[31m[Nest] 15108  - [39m05/26/2025, 11:52:46 PM [31m  ERROR[39m [38;5;3m[QuizController] [39mBadRequestException: Invalid ZIP file format
    at [90m/Users/<USER>/tms/tms-api/[39msrc/quiz/quiz.service.ts:404:27
    at [90m/Users/<USER>/tms/tms-api/[39mnode_modules/[4myauzl[24m/index.js:190:5
    at [90m/Users/<USER>/tms/tms-api/[39mnode_modules/[4myauzl[24m/index.js:772:5
    at Immediate.<anonymous> [90m(/Users/<USER>/tms/tms-api/[39mnode_modules/[4myauzl[24m/fd-slicer.js:220:5[90m)[39m
[90m    at processImmediate (node:internal/timers:491:21)[39m {
  response: {
    message: [32m'Invalid ZIP file format'[39m,
    error: [32m'Bad Request'[39m,
    statusCode: [33m400[39m
  },
  status: [33m400[39m,
  options: {}
}
  console.log
    ✅ Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A2.Y_for_Y11_3U_F2F QZ.zip - 41ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:361:19)

  console.log
    Processing file 9/20: Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A3.R_for_Y11_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:353:17)

  console.log
    ✅ Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A3.R_for_Y11_3U_F2F QZ.zip - 33ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:361:19)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:475:19)

  console.log
    MinIO test completed successfully

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:476:19)

  console.log
    Quiz ID: 72937980, GIFs: 4

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:477:19)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:480:19)

  console.log
    Processing file 10/20: Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A3.Y_for_Y11_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:353:17)

  console.log
    ✅ Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A3.Y_for_Y11_3U_F2F QZ.zip - 29ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:361:19)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:793:17)

  console.log
    Testing Basic Structure - 2U Course: Standard 2U course structure with all fields populated

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:794:17)

  console.log
    File: Dr Du_Math_2U_V6 (2024)_Y11_TF(II)_A_for_Y11_2U_F2F QZ.zip

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:795:17)

  console.log
    Expected metadata: {
      grade: 11,
      subject: 'Math',
      course: '2U',
      classLevel: 'A',
      color: 'R',
      topic: 'Trigonometric Functions (II)'
    }

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:796:17)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:797:17)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:919:17)

  console.log
    Basic Structure - 2U Course test completed successfully

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:920:17)

  console.log
    Quiz ID: 880417171, GIF count: 3

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:921:17)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:924:17)

  console.log
    Processing file 11/20: Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_A1.R_for_Y12_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:353:17)

  console.log
    ✅ Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_A1.R_for_Y12_3U_F2F QZ.zip - 32ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:361:19)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:793:17)

  console.log
    Testing Basic Structure - 3U Course: Standard 3U course structure with A1 class level

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:794:17)

  console.log
    File: Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A1.R_for_Y11_3U_F2F QZ.zip

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:795:17)

  console.log
    Expected metadata: {
      grade: 11,
      subject: 'Math',
      course: '3U',
      classLevel: 'A1',
      color: 'R',
      topic: 'Polynomial Functions (II)'
    }

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:796:17)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:797:17)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:919:17)

  console.log
    Basic Structure - 3U Course test completed successfully

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:920:17)

  console.log
    Quiz ID: 802506505, GIF count: 4

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:921:17)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:924:17)

  console.log
    Processing file 12/20: Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_A1.Y_for_Y12_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:353:17)

  console.log
    ✅ Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_A1.Y_for_Y12_3U_F2F QZ.zip - 30ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:361:19)

  console.log
    Processing file 13/20: Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_A2.R_for_Y12_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:353:17)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:793:17)

  console.log
    Testing Basic Structure - 4U Course: 4U course level (highest math level)

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:794:17)

  console.log
    File: Dr Du_Math_4U_V6 (2024)_Y12_TE(I)_A1.R_for_Y12_4U_F2F QZ.zip

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:795:17)

  console.log
    Expected metadata: {
      grade: 12,
      subject: 'Math',
      course: '4U',
      classLevel: 'A1',
      color: 'R',
      topic: 'Topic Enhancement (I)'
    }

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:796:17)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:797:17)

  console.log
    ✅ Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_A2.R_for_Y12_3U_F2F QZ.zip - 33ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:361:19)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:919:17)

  console.log
    Basic Structure - 4U Course test completed successfully

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:920:17)

  console.log
    Quiz ID: 1826403961, GIF count: 4

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:921:17)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:924:17)

  console.log
    Processing file 14/20: Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_A2.Y_for_Y12_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:353:17)

  console.log
    ✅ Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_A2.Y_for_Y12_3U_F2F QZ.zip - 24ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:361:19)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:793:17)

  console.log
    Testing Empty Course - Grade 9: Empty course field in Grade 9 metadata

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:794:17)

  console.log
    File: Dr Du_Math__V6_Y09_Plane Geometry (IV)_A1.R_for_Y09__F2F QZ.zip

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:795:17)

  console.log
    Expected metadata: {
      grade: 9,
      subject: 'Math',
      course: '',
      classLevel: 'A1',
      color: 'R',
      topic: 'Plane Geometry (IV)'
    }

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:796:17)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:797:17)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:919:17)

  console.log
    Empty Course - Grade 9 test completed successfully

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:920:17)

  console.log
    Quiz ID: 1245651896, GIF count: 4

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:921:17)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:924:17)

  console.log
    Processing file 15/20: Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_A3.R_for_Y12_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:353:17)

  console.log
    ✅ Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_A3.R_for_Y12_3U_F2F QZ.zip - 26ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:361:19)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:793:17)

  console.log
    Testing Empty Course - Grade 10: Empty course field in Grade 10 metadata

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:794:17)

  console.log
    File: Dr Du_Math__V6_Y10_PF(II)_A1.R_for_Y10__F2F QZ.zip

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:795:17)

  console.log
    Expected metadata: {
      grade: 10,
      subject: 'Math',
      course: '',
      classLevel: 'A1',
      color: 'R',
      topic: 'Polynomial Function (II)'
    }

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:796:17)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:797:17)

  console.log
    Processing file 16/20: Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_A3.Y_for_Y12_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:353:17)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:919:17)

  console.log
    Empty Course - Grade 10 test completed successfully

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:920:17)

  console.log
    Quiz ID: 1485258300, GIF count: 7

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:921:17)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:924:17)

  console.log
    ✅ Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_A3.Y_for_Y12_3U_F2F QZ.zip - 26ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:361:19)

  console.log
    Processing file 17/20: Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_B.R_for_Y12_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:353:17)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:793:17)

  console.log
    Testing Empty Course - A3 Class Level: Empty course field with A3 class level

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:794:17)

  console.log
    File: Dr Du_Math__V6_Y09_Plane Geometry (IV)_A3.R_for_Y09__F2F QZ.zip

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:795:17)

  console.log
    Expected metadata: {
      grade: 9,
      subject: 'Math',
      course: '',
      classLevel: 'A3',
      color: 'R',
      topic: 'Plane Geometry (IV)'
    }

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:796:17)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:797:17)

  console.log
    ✅ Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_B.R_for_Y12_3U_F2F QZ.zip - 72ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:361:19)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:919:17)

  console.log
    Empty Course - A3 Class Level test completed successfully

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:920:17)

  console.log
    Quiz ID: 1673644423, GIF count: 4

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:921:17)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:924:17)

  console.log
    Processing file 18/20: Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_B.Y_for_Y12_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:353:17)

  console.log
    ✅ Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_B.Y_for_Y12_3U_F2F QZ.zip - 28ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:361:19)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:793:17)

  console.log
    Testing Complex Numbering - Yellow Version: Complex question numbering like 1(a), 1(b), 2(a) - Yellow version

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:794:17)

  console.log
    File: Dr Du_Math_3U_V6 (2023)_Y11_Binomial Expansion_B.Y_for_Y11_3U_F2F QZ.zip

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:795:17)

  console.log
    Expected metadata: {
      grade: 11,
      subject: 'Math',
      course: '3U',
      classLevel: 'B',
      color: 'Y',
      topic: 'Binomial Expansion'
    }

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:796:17)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:797:17)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:919:17)

  console.log
    Complex Numbering - Yellow Version test completed successfully

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:920:17)

  console.log
    Quiz ID: 852949834, GIF count: 6

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:921:17)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:924:17)

  console.log
    Processing file 19/20: Dr Du_Math_4U_V6 (2024)_Y12_TE(I)_A1.R_for_Y12_4U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:353:17)

  console.log
    ✅ Dr Du_Math_4U_V6 (2024)_Y12_TE(I)_A1.R_for_Y12_4U_F2F QZ.zip - 34ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:361:19)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:793:17)

  console.log
    Testing Complex Numbering - Red Version: Complex question numbering like 1(a), 1(b), 2(a) - Red version

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:794:17)

  console.log
    File: Dr Du_Math_3U_V6 (2023)_Y11_Binomial Expansion_B.R_for_Y11_3U_F2F QZ.zip

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:795:17)

  console.log
    Expected metadata: {
      grade: 11,
      subject: 'Math',
      course: '3U',
      classLevel: 'B',
      color: 'R',
      topic: 'Binomial Expansion'
    }

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:796:17)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:797:17)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:919:17)

  console.log
    Complex Numbering - Red Version test completed successfully

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:920:17)

  console.log
    Quiz ID: 186883761, GIF count: 6

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:921:17)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:924:17)

  console.log
    Processing file 20/20: Dr Du_Math_4U_V6 (2024)_Y12_TE(I)_A1.Y_for_Y12_4U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:353:17)

  console.log
    ✅ Dr Du_Math_4U_V6 (2024)_Y12_TE(I)_A1.Y_for_Y12_4U_F2F QZ.zip - 30ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:361:19)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:793:17)

  console.log
    Testing Color Codes - Red vs Yellow A1: Yellow version of A1 class level quiz

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:794:17)

  console.log
    File: Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A1.Y_for_Y11_3U_F2F QZ.zip

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:795:17)

  console.log
    Expected metadata: {
      grade: 11,
      subject: 'Math',
      course: '3U',
      classLevel: 'A1',
      color: 'Y',
      topic: 'Polynomial Functions (II)'
    }

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:796:17)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:797:17)

  console.log
    == BATCH UPLOAD RESULTS ==

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:375:15)

  console.log
    Total files processed: 20

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:376:15)

  console.log
    Successful uploads: 20

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:377:15)

  console.log
    Failed uploads: 0

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:378:15)

  console.log
    Success rate: 100.0%

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:379:15)

  console.log
    Total time: 2.77s

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:380:15)

  console.log
    Average response time: 36ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:381:15)

  console.log
    Min response time: 24ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:384:15)

  console.log
    Max response time: 72ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:385:15)

  console.log
    Throughput: 7.23 files/second

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:386:15)

  console.log
    == CONSTRAINT VIOLATION ANALYSIS ==

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:391:15)

  console.log
    Constraint violations detected: 0

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:392:15)

  console.log
    Constraint violation rate: 0.0%

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:395:15)

  console.log
    Successful retries: 0

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:398:15)

  console.log
    Total retry attempts: 0

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:401:15)

  console.log
    ============================

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:411:15)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:919:17)

  console.log
    Color Codes - Red vs Yellow A1 test completed successfully

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:920:17)

  console.log
    Quiz ID: 1054959333, GIF count: 4

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:921:17)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:924:17)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:793:17)

  console.log
    Testing Color Codes - Red vs Yellow A2: Yellow version of A2 class level quiz

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:794:17)

  console.log
    File: Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A2.Y_for_Y11_3U_F2F QZ.zip

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:795:17)

  console.log
    Expected metadata: {
      grade: 11,
      subject: 'Math',
      course: '3U',
      classLevel: 'A2',
      color: 'Y',
      topic: 'Polynomial Functions (II)'
    }

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:796:17)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:797:17)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:919:17)

  console.log
    Color Codes - Red vs Yellow A2 test completed successfully

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:920:17)

  console.log
    Quiz ID: 512968349, GIF count: 4

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:921:17)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:924:17)

  console.log
    == CONCURRENT UPLOAD PERFORMANCE TEST ==

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:428:15)

  console.log
    Testing 5 simultaneous uploads

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:429:15)

  console.log
    Expected: >70% success rate, system stability maintained

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:430:15)

  console.log
    ==========================================

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:431:15)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:793:17)

  console.log
    Testing Class Levels - A2 Level: A2 class level testing

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:794:17)

  console.log
    File: Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A2.R_for_Y11_3U_F2F QZ.zip

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:795:17)

  console.log
    Expected metadata: {
      grade: 11,
      subject: 'Math',
      course: '3U',
      classLevel: 'A2',
      color: 'R',
      topic: 'Polynomial Functions (II)'
    }

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:796:17)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:797:17)

  console.log
    ✅ Dr Du_Math_2U_V6 (2024)_Y11_TF(II)_A_for_Y11_2U_F2F QZ.zip - 69ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:501:21)

  console.log
    ✅ Dr Du_Math_2U_V6 (2024)_Y12_TE(I)_A_for_Y12_2U_F2F QZ.zip - 69ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:501:21)

  console.log
    ✅ Dr Du_Math_3U_V6 (2023)_Y11_Binomial Expansion_B.R_for_Y11_3U_F2F QZ.zip - 57ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:501:21)

  console.log
    ✅ Dr Du_Math_3U_V6 (2023)_Y11_Binomial Expansion_B.Y_for_Y11_3U_F2F QZ.zip - 58ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:501:21)

  console.log
    ✅ Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A1.R_for_Y11_3U_F2F QZ.zip - 54ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:501:21)

  console.log
    == CONCURRENT UPLOAD RESULTS ==

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:526:15)

  console.log
    Concurrent uploads: 5

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:527:15)

  console.log
    Successful uploads: 5

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:528:15)

  console.log
    Failed uploads: 0

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:529:15)

  console.log
    Success rate: 100.0%

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:530:15)

  console.log
    Total concurrent time: 0.07s

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:531:15)

  console.log
    Average response time: 61ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:534:15)

  console.log
    Max response time: 69ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:537:15)

  console.log
    =================================

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:538:15)

  console.log
    Database verification: 5 quizzes found for 5 successful uploads

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:552:17)

  console.log
    Persistence rate: 100.0%

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:564:17)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:919:17)

  console.log
    Class Levels - A2 Level test completed successfully

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:920:17)

  console.log
    Quiz ID: 151577188, GIF count: 4

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:921:17)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:924:17)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:793:17)

  console.log
    Testing Class Levels - A3 Level: A3 class level testing

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:794:17)

  console.log
    File: Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A3.R_for_Y11_3U_F2F QZ.zip

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:795:17)

  console.log
    Expected metadata: {
      grade: 11,
      subject: 'Math',
      course: '3U',
      classLevel: 'A3',
      color: 'R',
      topic: 'Polynomial Functions (II)'
    }

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:796:17)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:797:17)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:919:17)

  console.log
    Class Levels - A3 Level test completed successfully

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:920:17)

  console.log
    Quiz ID: 1660427843, GIF count: 3

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:921:17)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:924:17)

  console.log
    == RAPID SEQUENTIAL UPLOAD TEST ==

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:592:15)

  console.log
    Testing 10 rapid sequential uploads

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:593:15)

  console.log
    Expected: System stability, graceful handling of load

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:594:15)

  console.log
    ====================================

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:595:15)

  console.log
    Rapid upload 1/10: Dr Du_Math_2U_V6 (2024)_Y11_TF(II)_A_for_Y11_2U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:605:17)

  console.log
    ✅ Dr Du_Math_2U_V6 (2024)_Y11_TF(II)_A_for_Y11_2U_F2F QZ.zip - 26ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:613:19)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:793:17)

  console.log
    Testing Class Levels - B Level: B class level testing

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:794:17)

  console.log
    File: Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_B.R_for_Y12_3U_F2F QZ.zip

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:795:17)

  console.log
    Expected metadata: {
      grade: 12,
      subject: 'Math',
      course: '3U',
      classLevel: 'B',
      color: 'R',
      topic: 'Topic Enhancement (I)'
    }

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:796:17)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:797:17)

  console.log
    Rapid upload 2/10: Dr Du_Math_2U_V6 (2024)_Y12_TE(I)_A_for_Y12_2U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:605:17)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:919:17)

  console.log
    Class Levels - B Level test completed successfully

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:920:17)

  console.log
    Quiz ID: 1789980903, GIF count: 3

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:921:17)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:924:17)

  console.log
    ✅ Dr Du_Math_2U_V6 (2024)_Y12_TE(I)_A_for_Y12_2U_F2F QZ.zip - 40ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:613:19)

  console.log
    Rapid upload 3/10: Dr Du_Math_3U_V6 (2023)_Y11_Binomial Expansion_B.R_for_Y11_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:605:17)

  console.log
    ✅ Dr Du_Math_3U_V6 (2023)_Y11_Binomial Expansion_B.R_for_Y11_3U_F2F QZ.zip - 33ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:613:19)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:793:17)

  console.log
    Testing Curriculum Versions - V2: Version 2 curriculum testing

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:794:17)

  console.log
    File: Dr Du_Math__V2_Y09_Trigonometry_A1.R_for_Y09__F2F QZ.zip

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:795:17)

  console.log
    Expected metadata: {
      grade: 9,
      subject: 'Math',
      course: '',
      classLevel: 'A1',
      color: 'R',
      topic: 'Trigonometry'
    }

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:796:17)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:797:17)

  console.log
    Rapid upload 4/10: Dr Du_Math_3U_V6 (2023)_Y11_Binomial Expansion_B.Y_for_Y11_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:605:17)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:919:17)

  console.log
    Curriculum Versions - V2 test completed successfully

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:920:17)

  console.log
    Quiz ID: 1279846293, GIF count: 4

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:921:17)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:924:17)

  console.log
    ✅ Dr Du_Math_3U_V6 (2023)_Y11_Binomial Expansion_B.Y_for_Y11_3U_F2F QZ.zip - 36ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:613:19)

  console.log
    Rapid upload 5/10: Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A1.R_for_Y11_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:605:17)

  console.log
    ✅ Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A1.R_for_Y11_3U_F2F QZ.zip - 31ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:613:19)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:793:17)

  console.log
    Testing Curriculum Versions - V3: Version 3 curriculum testing

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:794:17)

  console.log
    File: Dr Du_Math__V3_Y09_Plane Geometry (IV)_B_for_Y09__F2F QZ.zip

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:795:17)

  console.log
    Expected metadata: {
      grade: 9,
      subject: 'Math',
      course: '',
      classLevel: 'B',
      color: 'R',
      topic: 'Plane Geometry (IV)'
    }

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:796:17)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:797:17)

  console.log
    Rapid upload 6/10: Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A1.Y_for_Y11_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:605:17)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:919:17)

  console.log
    Curriculum Versions - V3 test completed successfully

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:920:17)

  console.log
    Quiz ID: 1242076428, GIF count: 4

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:921:17)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:924:17)

  console.log
    ✅ Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A1.Y_for_Y11_3U_F2F QZ.zip - 30ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:613:19)

  console.log
    Rapid upload 7/10: Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A2.R_for_Y11_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:605:17)

  console.log
    ✅ Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A2.R_for_Y11_3U_F2F QZ.zip - 29ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:613:19)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:793:17)

  console.log
    Testing Grade Levels - Year 12: Year 12 grade level testing

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:794:17)

  console.log
    File: Dr Du_Math_2U_V6 (2024)_Y12_TE(I)_A_for_Y12_2U_F2F QZ.zip

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:795:17)

  console.log
    Expected metadata: {
      grade: 12,
      subject: 'Math',
      course: '2U',
      classLevel: 'A',
      color: 'R',
      topic: 'Topic Enhancement (I)'
    }

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:796:17)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:797:17)

  console.log
    Rapid upload 8/10: Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A2.Y_for_Y11_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:605:17)

  console.log
    ✅ Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A2.Y_for_Y11_3U_F2F QZ.zip - 28ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:613:19)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:919:17)

  console.log
    Grade Levels - Year 12 test completed successfully

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:920:17)

  console.log
    Quiz ID: 581955629, GIF count: 9

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:921:17)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:924:17)

  console.log
    Rapid upload 9/10: Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A3.R_for_Y11_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:605:17)

  console.log
    ✅ Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A3.R_for_Y11_3U_F2F QZ.zip - 23ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:613:19)

  console.log
    Rapid upload 10/10: Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A3.Y_for_Y11_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:605:17)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:793:17)

  console.log
    Testing Special Topics - HE(I): Special topic name with abbreviation

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:794:17)

  console.log
    File: Dr Du_Math__V3_Y10_HE(I)_B_for_Y10__F2F QZ.zip

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:795:17)

  console.log
    Expected metadata: {
      grade: 10,
      subject: 'Math',
      course: '',
      classLevel: 'B',
      color: 'R',
      topic: 'Half-Yearly Enhancement (I)'
    }

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:796:17)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:797:17)

  console.log
    ✅ Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A3.Y_for_Y11_3U_F2F QZ.zip - 27ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:613:19)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:919:17)

  console.log
    Special Topics - HE(I) test completed successfully

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:920:17)

  console.log
    Quiz ID: 57918564, GIF count: 8

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:921:17)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:924:17)

  console.log
    == RAPID SEQUENTIAL RESULTS ==

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:626:15)

  console.log
    Rapid uploads: 10

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:627:15)

  console.log
    Successful uploads: 10

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:628:15)

  console.log
    Failed uploads: 0

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:629:15)

  console.log
    Success rate: 100.0%

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:630:15)

  console.log
    Total time: 0.82s

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:631:15)

  console.log
    Throughput: 12.21 files/second

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:632:15)

  console.log
    Average response time: 30ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:635:15)

  console.log
    ================================

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:638:15)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:934:15)

  console.log
    Found 33 ZIP files for comprehensive testing

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:935:15)

  console.log
    Testing 10 additional files beyond the parameterized tests

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:938:15)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:939:15)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:947:15)

  console.log
    Additional files to test: 10

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:948:15)

  console.log
    Files: Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A3.Y_for_Y11_3U_F2F QZ.zip, Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_A1.R_for_Y12_3U_F2F QZ.zip, Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_A1.Y_for_Y12_3U_F2F QZ.zip, Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_A2.R_for_Y12_3U_F2F QZ.zip, Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_A2.Y_for_Y12_3U_F2F QZ.zip, Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_A3.R_for_Y12_3U_F2F QZ.zip, Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_A3.Y_for_Y12_3U_F2F QZ.zip, Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_B.Y_for_Y12_3U_F2F QZ.zip, Dr Du_Math_4U_V6 (2024)_Y12_TE(I)_A1.Y_for_Y12_4U_F2F QZ.zip, Dr Du_Math_4U_V6 (2024)_Y12_TE(I)_A2.R_for_Y12_4U_F2F QZ.zip

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:949:15)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:950:15)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:960:19)

  console.log
    Testing additional file 1/10: Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A3.Y_for_Y11_3U_F2F QZ.zip

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:961:19)

  console.log
    File size: 271865 bytes

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:964:19)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:965:19)

  console.log
    ✅ Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A3.Y_for_Y11_3U_F2F QZ.zip processed successfully

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:982:21)

  console.log
    == MEMORY AND RESOURCE MONITORING TEST ==

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:663:15)

  console.log
    Testing 15 uploads for memory usage

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:664:15)

  console.log
    Expected: Stable memory usage, no significant leaks

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:665:15)

  console.log
    ==========================================

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:666:15)

  console.log
    Initial memory usage: { rss: '260.06 MB', heapUsed: '96.02 MB', heapTotal: '134.34 MB' }

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:670:15)

  console.log
    Memory test upload 1/15: Dr Du_Math_2U_V6 (2024)_Y11_TF(II)_A_for_Y11_2U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:692:17)

  console.log
    Memory test upload 2/15: Dr Du_Math_2U_V6 (2024)_Y12_TE(I)_A_for_Y12_2U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:692:17)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:960:19)

  console.log
    Testing additional file 2/10: Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_A1.R_for_Y12_3U_F2F QZ.zip

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:961:19)

  console.log
    File size: 390894 bytes

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:964:19)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:965:19)

  console.log
    ✅ Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_A1.R_for_Y12_3U_F2F QZ.zip processed successfully

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:982:21)

  console.log
    Memory test upload 3/15: Dr Du_Math_3U_V6 (2023)_Y11_Binomial Expansion_B.R_for_Y11_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:692:17)

  console.log
    Memory test upload 4/15: Dr Du_Math_3U_V6 (2023)_Y11_Binomial Expansion_B.Y_for_Y11_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:692:17)

  console.log
    Memory test upload 5/15: Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A1.R_for_Y11_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:692:17)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:960:19)

  console.log
    Testing additional file 3/10: Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_A1.Y_for_Y12_3U_F2F QZ.zip

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:961:19)

  console.log
    File size: 353955 bytes

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:964:19)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:965:19)

  console.log
    ✅ Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_A1.Y_for_Y12_3U_F2F QZ.zip processed successfully

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:982:21)

  console.log
    Memory after 5 uploads: { rss: '262.06 MB', heapUsed: '104.44 MB' }

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:707:19)

  console.log
    Memory test upload 6/15: Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A1.Y_for_Y11_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:692:17)

  console.log
    Memory test upload 7/15: Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A2.R_for_Y11_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:692:17)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:960:19)

  console.log
    Testing additional file 4/10: Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_A2.R_for_Y12_3U_F2F QZ.zip

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:961:19)

  console.log
    File size: 299404 bytes

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:964:19)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:965:19)

  console.log
    ✅ Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_A2.R_for_Y12_3U_F2F QZ.zip processed successfully

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:982:21)

  console.log
    Memory test upload 8/15: Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A2.Y_for_Y11_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:692:17)

  console.log
    Memory test upload 9/15: Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A3.R_for_Y11_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:692:17)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:960:19)

  console.log
    Testing additional file 5/10: Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_A2.Y_for_Y12_3U_F2F QZ.zip

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:961:19)

  console.log
    File size: 278150 bytes

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:964:19)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:965:19)

  console.log
    Memory test upload 10/15: Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A3.Y_for_Y11_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:692:17)

  console.log
    ✅ Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_A2.Y_for_Y12_3U_F2F QZ.zip processed successfully

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:982:21)

  console.log
    Memory after 10 uploads: { rss: '265.19 MB', heapUsed: '100.39 MB' }

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:707:19)

  console.log
    Memory test upload 11/15: Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_A1.R_for_Y12_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:692:17)

  console.log
    Memory test upload 12/15: Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_A1.Y_for_Y12_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:692:17)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:960:19)

  console.log
    Testing additional file 6/10: Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_A3.R_for_Y12_3U_F2F QZ.zip

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:961:19)

  console.log
    File size: 218447 bytes

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:964:19)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:965:19)

  console.log
    ✅ Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_A3.R_for_Y12_3U_F2F QZ.zip processed successfully

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:982:21)

  console.log
    Memory test upload 13/15: Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_A2.R_for_Y12_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:692:17)

  console.log
    Memory test upload 14/15: Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_A2.Y_for_Y12_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:692:17)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:960:19)

  console.log
    Testing additional file 7/10: Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_A3.Y_for_Y12_3U_F2F QZ.zip

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:961:19)

  console.log
    File size: 252402 bytes

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:964:19)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:965:19)

  console.log
    Memory test upload 15/15: Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_A3.R_for_Y12_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:692:17)

  console.log
    ✅ Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_A3.Y_for_Y12_3U_F2F QZ.zip processed successfully

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:982:21)

  console.log
    Memory after 15 uploads: { rss: '265.89 MB', heapUsed: '95.49 MB' }

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:707:19)

  console.log
    == MEMORY ANALYSIS RESULTS ==

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:740:15)

  console.log
    Files processed: 15

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:741:15)

  console.log
    Success rate: 100.0%

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:742:15)

  console.log
    Memory increase: { rss: '5.83 MB', heapUsed: '-0.46 MB', heapTotal: '-1.75 MB' }

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:743:15)

  console.log
    Final memory usage: { rss: '265.89 MB', heapUsed: '95.56 MB' }

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:748:15)

  console.log
    ==============================

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:752:15)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:960:19)

  console.log
    Testing additional file 8/10: Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_B.Y_for_Y12_3U_F2F QZ.zip

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:961:19)

  console.log
    File size: 252401 bytes

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:964:19)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:965:19)

  console.log
    ✅ Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_B.Y_for_Y12_3U_F2F QZ.zip processed successfully

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:982:21)

  console.log
    == SUSTAINED LOAD STRESS TEST ==

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:780:15)

  console.log
    Testing sustained load with 30 files

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:781:15)

  console.log
    Expected: System handles extended load gracefully

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:782:15)

  console.log
    ======================================

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:783:15)

  console.log
    Stress test upload 1/30: Dr Du_Math_2U_V6 (2024)_Y11_TF(II)_A_for_Y11_2U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:795:17)

  console.log
    ✅ Dr Du_Math_2U_V6 (2024)_Y11_TF(II)_A_for_Y11_2U_F2F QZ.zip - 36ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:813:19)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:960:19)

  console.log
    Testing additional file 9/10: Dr Du_Math_4U_V6 (2024)_Y12_TE(I)_A1.Y_for_Y12_4U_F2F QZ.zip

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:961:19)

  console.log
    File size: 453931 bytes

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:964:19)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:965:19)

  console.log
    Stress test upload 2/30: Dr Du_Math_2U_V6 (2024)_Y12_TE(I)_A_for_Y12_2U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:795:17)

  console.log
    ✅ Dr Du_Math_4U_V6 (2024)_Y12_TE(I)_A1.Y_for_Y12_4U_F2F QZ.zip processed successfully

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:982:21)

  console.log
    ✅ Dr Du_Math_2U_V6 (2024)_Y12_TE(I)_A_for_Y12_2U_F2F QZ.zip - 47ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:813:19)

  console.log
    Stress test upload 3/30: Dr Du_Math_3U_V6 (2023)_Y11_Binomial Expansion_B.R_for_Y11_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:795:17)

  console.log
    ✅ Dr Du_Math_3U_V6 (2023)_Y11_Binomial Expansion_B.R_for_Y11_3U_F2F QZ.zip - 48ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:813:19)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:960:19)

  console.log
    Testing additional file 10/10: Dr Du_Math_4U_V6 (2024)_Y12_TE(I)_A2.R_for_Y12_4U_F2F QZ.zip

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:961:19)

  console.log
    File size: 402703 bytes

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:964:19)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:965:19)

  console.log
    ✅ Dr Du_Math_4U_V6 (2024)_Y12_TE(I)_A2.R_for_Y12_4U_F2F QZ.zip processed successfully

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:982:21)

  console.log
    Stress test upload 4/30: Dr Du_Math_3U_V6 (2023)_Y11_Binomial Expansion_B.Y_for_Y11_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:795:17)

  console.log
    ✅ Dr Du_Math_3U_V6 (2023)_Y11_Binomial Expansion_B.Y_for_Y11_3U_F2F QZ.zip - 48ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:813:19)

  console.log
    Stress test upload 5/30: Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A1.R_for_Y11_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:795:17)

  console.log
    ✅ Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A1.R_for_Y11_3U_F2F QZ.zip - 44ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:813:19)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:1021:15)

  console.log
    Additional files test results: 10/10 files processed successfully

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:1022:15)

  console.log
    Success rate: 100.0%

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:1025:15)

  console.log
    Total coverage: 17 parameterized + 10 additional = 27 files tested

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:1028:15)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:1031:15)

  console.log
    Stress test upload 6/30: Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A1.Y_for_Y11_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:795:17)

  console.log
    ✅ Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A1.Y_for_Y11_3U_F2F QZ.zip - 27ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:813:19)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:1044:15)

  console.log
    Testing 3 concurrent uploads

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:1045:15)

  console.log
    Expected: All uploads should succeed or fail gracefully

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:1046:15)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:1047:15)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:1099:15)

  console.log
    Concurrent upload results: 3 succeeded, 0 failed

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:1100:15)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:1103:15)

  console.log
    Stress test upload 7/30: Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A2.R_for_Y11_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:795:17)

  console.log
    ✅ Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A2.R_for_Y11_3U_F2F QZ.zip - 27ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:813:19)

  console.log
    Stress test upload 8/30: Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A2.Y_for_Y11_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:795:17)

  console.log
    ✅ Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A2.Y_for_Y11_3U_F2F QZ.zip - 33ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:813:19)

  console.log
    Stress test upload 9/30: Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A3.R_for_Y11_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:795:17)

  console.log
    ✅ Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A3.R_for_Y11_3U_F2F QZ.zip - 45ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:813:19)

  console.log
    Stress test upload 10/30: Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A3.Y_for_Y11_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:795:17)

  console.log
    ✅ Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A3.Y_for_Y11_3U_F2F QZ.zip - 26ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:813:19)

  console.log
    Progress: 10/30 - Success rate: 100.0%

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:836:19)

  console.log
    Stress test upload 11/30: Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_A1.R_for_Y12_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:795:17)

  console.log
    == DEBUGGING OUTPUT START ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:1170:15)

  console.log
    Rapid upload results: 3/3 succeeded

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:1171:15)

  console.log
    Failed uploads: 0

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:1174:15)

  console.log
    System stability test completed

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:1175:15)

  console.log
    == DEBUGGING OUTPUT END ==

      at Object.<anonymous> (quiz-upload.e2e-spec.ts:1176:15)

  console.log
    ✅ Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_A1.R_for_Y12_3U_F2F QZ.zip - 33ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:813:19)

  console.log
    Stress test upload 12/30: Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_A1.Y_for_Y12_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:795:17)

  console.log
    ✅ Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_A1.Y_for_Y12_3U_F2F QZ.zip - 49ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:813:19)

  console.log
    Stress test upload 13/30: Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_A2.R_for_Y12_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:795:17)

  console.log
    ✅ Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_A2.R_for_Y12_3U_F2F QZ.zip - 49ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:813:19)

  console.log
    Stress test upload 14/30: Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_A2.Y_for_Y12_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:795:17)

  console.log
    ✅ Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_A2.Y_for_Y12_3U_F2F QZ.zip - 49ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:813:19)

PASS test/quiz-upload.e2e-spec.ts (10.318 s)
  Enhanced Quiz Upload E2E Tests
    Complete Upload Workflow Tests
      ✓ should successfully upload and process a real quiz ZIP file (230 ms)
      ✓ should handle authentication and validation in complete workflow (161 ms)
      ✓ should verify ZIP file extraction and validation with real file (178 ms)
    Error Scenario Tests
      ✓ should handle invalid ZIP structure gracefully (145 ms)
      ✓ should handle missing required files in ZIP (142 ms)
      ✓ should handle non-ZIP file uploads (143 ms)
    Transaction Rollback Tests
      ✓ should verify proper cleanup on database failures (145 ms)
      ✓ should handle MinIO upload failures gracefully (168 ms)
    Training Data Edge Case Tests
      ✓ should process Basic Structure - 2U Course files correctly: Dr Du_Math_2U_V6 (2024)_Y11_TF(II)_A_for_Y11_2U_F2F QZ.zip (165 ms)
      ✓ should process Basic Structure - 3U Course files correctly: Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A1.R_for_Y11_3U_F2F QZ.zip (191 ms)
      ✓ should process Basic Structure - 4U Course files correctly: Dr Du_Math_4U_V6 (2024)_Y12_TE(I)_A1.R_for_Y12_4U_F2F QZ.zip (175 ms)
      ✓ should process Empty Course - Grade 9 files correctly: Dr Du_Math__V6_Y09_Plane Geometry (IV)_A1.R_for_Y09__F2F QZ.zip (166 ms)
      ✓ should process Empty Course - Grade 10 files correctly: Dr Du_Math__V6_Y10_PF(II)_A1.R_for_Y10__F2F QZ.zip (189 ms)
      ✓ should process Empty Course - A3 Class Level files correctly: Dr Du_Math__V6_Y09_Plane Geometry (IV)_A3.R_for_Y09__F2F QZ.zip (185 ms)
      ✓ should process Complex Numbering - Yellow Version files correctly: Dr Du_Math_3U_V6 (2023)_Y11_Binomial Expansion_B.Y_for_Y11_3U_F2F QZ.zip (161 ms)
      ✓ should process Complex Numbering - Red Version files correctly: Dr Du_Math_3U_V6 (2023)_Y11_Binomial Expansion_B.R_for_Y11_3U_F2F QZ.zip (169 ms)
      ✓ should process Color Codes - Red vs Yellow A1 files correctly: Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A1.Y_for_Y11_3U_F2F QZ.zip (176 ms)
      ✓ should process Color Codes - Red vs Yellow A2 files correctly: Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A2.Y_for_Y11_3U_F2F QZ.zip (156 ms)
      ✓ should process Class Levels - A2 Level files correctly: Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A2.R_for_Y11_3U_F2F QZ.zip (183 ms)
      ✓ should process Class Levels - A3 Level files correctly: Dr Du_Math_3U_V6 (2023)_Y11_PF(II)_A3.R_for_Y11_3U_F2F QZ.zip (147 ms)
      ✓ should process Class Levels - B Level files correctly: Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_B.R_for_Y12_3U_F2F QZ.zip (171 ms)
      ✓ should process Curriculum Versions - V2 files correctly: Dr Du_Math__V2_Y09_Trigonometry_A1.R_for_Y09__F2F QZ.zip (176 ms)
      ✓ should process Curriculum Versions - V3 files correctly: Dr Du_Math__V3_Y09_Plane Geometry (IV)_B_for_Y09__F2F QZ.zip (162 ms)
      ✓ should process Grade Levels - Year 12 files correctly: Dr Du_Math_2U_V6 (2024)_Y12_TE(I)_A_for_Y12_2U_F2F QZ.zip (188 ms)
      ✓ should process Special Topics - HE(I) files correctly: Dr Du_Math__V3_Y10_HE(I)_B_for_Y10__F2F QZ.zip (170 ms)
      ✓ should handle expanded training files without errors (comprehensive test) (3569 ms)
    Concurrent Upload Tests
      ✓ should handle multiple simultaneous uploads correctly (172 ms)
      ✓ should maintain system stability under concurrent load (863 ms)

  console.log
    Stress test upload 15/30: Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_A3.R_for_Y12_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:795:17)

  console.log
    ✅ Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_A3.R_for_Y12_3U_F2F QZ.zip - 36ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:813:19)

  console.log
    Stress test upload 16/30: Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_A3.Y_for_Y12_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:795:17)

  console.log
    ✅ Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_A3.Y_for_Y12_3U_F2F QZ.zip - 30ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:813:19)

  console.log
    Stress test upload 17/30: Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_B.R_for_Y12_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:795:17)

  console.log
    ✅ Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_B.R_for_Y12_3U_F2F QZ.zip - 32ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:813:19)

  console.log
    Stress test upload 18/30: Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_B.Y_for_Y12_3U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:795:17)

  console.log
    ✅ Dr Du_Math_3U_V6 (2024)_Y12_TE(I)_B.Y_for_Y12_3U_F2F QZ.zip - 38ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:813:19)

  console.log
    Stress test upload 19/30: Dr Du_Math_4U_V6 (2024)_Y12_TE(I)_A1.R_for_Y12_4U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:795:17)

  console.log
    ✅ Dr Du_Math_4U_V6 (2024)_Y12_TE(I)_A1.R_for_Y12_4U_F2F QZ.zip - 46ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:813:19)

  console.log
    Stress test upload 20/30: Dr Du_Math_4U_V6 (2024)_Y12_TE(I)_A1.Y_for_Y12_4U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:795:17)

  console.log
    ✅ Dr Du_Math_4U_V6 (2024)_Y12_TE(I)_A1.Y_for_Y12_4U_F2F QZ.zip - 50ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:813:19)

  console.log
    Progress: 20/30 - Success rate: 100.0%

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:836:19)

  console.log
    Stress test upload 21/30: Dr Du_Math_4U_V6 (2024)_Y12_TE(I)_A2.R_for_Y12_4U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:795:17)

  console.log
    ✅ Dr Du_Math_4U_V6 (2024)_Y12_TE(I)_A2.R_for_Y12_4U_F2F QZ.zip - 41ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:813:19)

  console.log
    Stress test upload 22/30: Dr Du_Math_4U_V6 (2024)_Y12_TE(I)_A2.Y_for_Y12_4U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:795:17)

  console.log
    ✅ Dr Du_Math_4U_V6 (2024)_Y12_TE(I)_A2.Y_for_Y12_4U_F2F QZ.zip - 51ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:813:19)

  console.log
    Stress test upload 23/30: Dr Du_Math_4U_V6 (2024)_Y12_TE(I)_A3.R_for_Y12_4U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:795:17)

  console.log
    ✅ Dr Du_Math_4U_V6 (2024)_Y12_TE(I)_A3.R_for_Y12_4U_F2F QZ.zip - 42ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:813:19)

  console.log
    Stress test upload 24/30: Dr Du_Math_4U_V6 (2024)_Y12_TE(I)_A3.Y_for_Y12_4U_F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:795:17)

  console.log
    ✅ Dr Du_Math_4U_V6 (2024)_Y12_TE(I)_A3.Y_for_Y12_4U_F2F QZ.zip - 50ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:813:19)

  console.log
    Stress test upload 25/30: Dr Du_Math__V2_Y09_Trigonometry_A1.R_for_Y09__F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:795:17)

  console.log
    ✅ Dr Du_Math__V2_Y09_Trigonometry_A1.R_for_Y09__F2F QZ.zip - 60ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:813:19)

  console.log
    Stress test upload 26/30: Dr Du_Math__V3_Y09_Plane Geometry (IV)_B_for_Y09__F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:795:17)

  console.log
    ✅ Dr Du_Math__V3_Y09_Plane Geometry (IV)_B_for_Y09__F2F QZ.zip - 33ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:813:19)

  console.log
    Stress test upload 27/30: Dr Du_Math__V3_Y10_HE(I)_B_for_Y10__F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:795:17)

  console.log
    ✅ Dr Du_Math__V3_Y10_HE(I)_B_for_Y10__F2F QZ.zip - 46ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:813:19)

  console.log
    Stress test upload 28/30: Dr Du_Math__V6_Y09_Plane Geometry (IV)_A1.R_for_Y09__F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:795:17)

  console.log
    ✅ Dr Du_Math__V6_Y09_Plane Geometry (IV)_A1.R_for_Y09__F2F QZ.zip - 47ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:813:19)

  console.log
    Stress test upload 29/30: Dr Du_Math__V6_Y09_Plane Geometry (IV)_A3.R_for_Y09__F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:795:17)

  console.log
    ✅ Dr Du_Math__V6_Y09_Plane Geometry (IV)_A3.R_for_Y09__F2F QZ.zip - 44ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:813:19)

  console.log
    Stress test upload 30/30: Dr Du_Math__V6_Y10_PF(II)_A1.R_for_Y10__F2F QZ.zip

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:795:17)

  console.log
    ✅ Dr Du_Math__V6_Y10_PF(II)_A1.R_for_Y10__F2F QZ.zip - 51ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:813:19)

  console.log
    Progress: 30/30 - Success rate: 100.0%

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:836:19)

  console.log
    == STRESS TEST RESULTS ==

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:846:15)

  console.log
    Total files processed: 30

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:847:15)

  console.log
    Successful uploads: 30

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:848:15)

  console.log
    Failed uploads: 0

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:849:15)

  console.log
    Success rate: 100.0%

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:850:15)

  console.log
    Total time: 0.10 minutes

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:851:15)

  console.log
    Average response time: 42ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:854:15)

  console.log
    Throughput: 5.13 files/second

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:857:15)

  console.log
    Max consecutive failures: 0

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:860:15)

  console.log
    ========================

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:861:15)

  console.log
    Database verification: 19 quizzes found for 30 successful uploads

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:882:17)

  console.log
    Persistence rate: 63.3%

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:893:17)

  console.log
    
    🔬 GRADIENT STRESS TEST: Finding System Breaking Point

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:901:15)

  console.log
    ======================================================================

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:902:15)

  console.log
    Testing incremental loads to identify performance degradation patterns

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:903:15)

  console.log
    ======================================================================

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:906:15)

  console.log
    
    📊 LEVEL 1: Baseline (3 files)

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:937:17)

  console.log
    --------------------------------------------------

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:940:17)

  console.log
    Memory before: 97.07MB

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:949:17)

  console.log
      ✅ File 1/3: 24ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:982:23)

  console.log
    
    📈 LEVEL 1 RESULTS:

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1060:17)

  console.log
      Files processed: 3

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1061:17)

  console.log
      Success rate: 100.0%

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1062:17)

  console.log
      Avg response time: 39ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1063:17)

  console.log
      Throughput: 11.07 files/sec

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1066:17)

  console.log
      Max consecutive failures: 0

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1069:17)

  console.log
      Memory increase: -6.44MB

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1070:17)

  console.log
      System stable: ✅ YES

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1071:17)

  console.log
    
    📊 LEVEL 2: Light Load (5 files)

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:937:17)

  console.log
    --------------------------------------------------

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:940:17)

  console.log
    Memory before: 92.22MB

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:949:17)

  console.log
      ✅ File 1/5: 31ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:982:23)

  console.log
    
    📈 LEVEL 2 RESULTS:

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1060:17)

  console.log
      Files processed: 5

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1061:17)

  console.log
      Success rate: 100.0%

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1062:17)

  console.log
      Avg response time: 47ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1063:17)

  console.log
      Throughput: 10.22 files/sec

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1066:17)

  console.log
      Max consecutive failures: 0

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1069:17)

  console.log
      Memory increase: -0.41MB

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1070:17)

  console.log
      System stable: ✅ YES

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1071:17)

  console.log
    
    📊 LEVEL 3: Moderate Load (8 files)

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:937:17)

  console.log
    --------------------------------------------------

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:940:17)

  console.log
    Memory before: 93.99MB

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:949:17)

  console.log
      ✅ File 1/8: 25ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:982:23)

  console.log
      ✅ File 6/8: 50ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:982:23)

  console.log
    
    📈 LEVEL 3 RESULTS:

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1060:17)

  console.log
      Files processed: 8

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1061:17)

  console.log
      Success rate: 100.0%

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1062:17)

  console.log
      Avg response time: 38ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1063:17)

  console.log
      Throughput: 11.24 files/sec

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1066:17)

  console.log
      Max consecutive failures: 0

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1069:17)

  console.log
      Memory increase: -7.37MB

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1070:17)

  console.log
      System stable: ✅ YES

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1071:17)

  console.log
    
    📊 LEVEL 4: Medium Load (12 files)

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:937:17)

  console.log
    --------------------------------------------------

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:940:17)

  console.log
    Memory before: 89.56MB

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:949:17)

  console.log
      ✅ File 1/12: 22ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:982:23)

  console.log
      ✅ File 6/12: 40ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:982:23)

  console.log
      ✅ File 11/12: 35ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:982:23)

  console.log
    
    📈 LEVEL 4 RESULTS:

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1060:17)

  console.log
      Files processed: 12

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1061:17)

  console.log
      Success rate: 100.0%

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1062:17)

  console.log
      Avg response time: 38ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1063:17)

  console.log
      Throughput: 11.20 files/sec

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1066:17)

  console.log
      Max consecutive failures: 0

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1069:17)

  console.log
      Memory increase: -2.28MB

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1070:17)

  console.log
      System stable: ✅ YES

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1071:17)

  console.log
    
    📊 LEVEL 5: Heavy Load (15 files)

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:937:17)

  console.log
    --------------------------------------------------

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:940:17)

  console.log
    Memory before: 91.02MB

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:949:17)

  console.log
      ✅ File 1/15: 20ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:982:23)

  console.log
      ✅ File 6/15: 26ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:982:23)

  console.log
      ✅ File 11/15: 66ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:982:23)

  console.log
    
    📈 LEVEL 5 RESULTS:

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1060:17)

  console.log
      Files processed: 15

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1061:17)

  console.log
      Success rate: 100.0%

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1062:17)

  console.log
      Avg response time: 37ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1063:17)

  console.log
      Throughput: 11.32 files/sec

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1066:17)

  console.log
      Max consecutive failures: 0

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1069:17)

  console.log
      Memory increase: -4.08MB

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1070:17)

  console.log
      System stable: ✅ YES

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1071:17)

  console.log
      ⏳ Cooling down for 2 seconds...

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1100:19)

  console.log
    
    📊 LEVEL 6: High Load (20 files)

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:937:17)

  console.log
    --------------------------------------------------

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:940:17)

  console.log
    Memory before: 91.11MB

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:949:17)

  console.log
      ✅ File 1/20: 21ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:982:23)

  console.log
      ✅ File 6/20: 42ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:982:23)

  console.log
      ✅ File 11/20: 42ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:982:23)

  console.log
      ✅ File 16/20: 39ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:982:23)

  console.log
    
    📈 LEVEL 6 RESULTS:

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1060:17)

  console.log
      Files processed: 20

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1061:17)

  console.log
      Success rate: 100.0%

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1062:17)

  console.log
      Avg response time: 36ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1063:17)

  console.log
      Throughput: 11.34 files/sec

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1066:17)

  console.log
      Max consecutive failures: 0

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1069:17)

  console.log
      Memory increase: -5.96MB

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1070:17)

  console.log
      System stable: ✅ YES

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1071:17)

  console.log
      ⏳ Cooling down for 2 seconds...

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1100:19)

  console.log
    
    📊 LEVEL 7: Very High Load (25 files)

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:937:17)

  console.log
    --------------------------------------------------

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:940:17)

  console.log
    Memory before: 90.28MB

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:949:17)

  console.log
      ✅ File 1/25: 20ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:982:23)

  console.log
      ✅ File 6/25: 38ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:982:23)

  console.log
      ✅ File 11/25: 29ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:982:23)

  console.log
      ✅ File 16/25: 20ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:982:23)

  console.log
      ✅ File 21/25: 31ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:982:23)

  console.log
    
    📈 LEVEL 7 RESULTS:

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1060:17)

  console.log
      Files processed: 25

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1061:17)

  console.log
      Success rate: 100.0%

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1062:17)

  console.log
      Avg response time: 33ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1063:17)

  console.log
      Throughput: 11.81 files/sec

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1066:17)

  console.log
      Max consecutive failures: 0

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1069:17)

  console.log
      Memory increase: -3.91MB

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1070:17)

  console.log
      System stable: ✅ YES

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1071:17)

  console.log
      ⏳ Cooling down for 2 seconds...

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1100:19)

  console.log
    
    📊 LEVEL 8: Extreme Load (30 files)

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:937:17)

  console.log
    --------------------------------------------------

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:940:17)

  console.log
    Memory before: 92.79MB

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:949:17)

  console.log
      ✅ File 1/30: 20ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:982:23)

  console.log
      ✅ File 6/30: 32ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:982:23)

  console.log
      ✅ File 11/30: 24ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:982:23)

  console.log
      ✅ File 16/30: 24ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:982:23)

  console.log
      ✅ File 21/30: 58ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:982:23)

  console.log
      ✅ File 26/30: 38ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:982:23)

  console.log
    
    📈 LEVEL 8 RESULTS:

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1060:17)

  console.log
      Files processed: 30

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1061:17)

  console.log
      Success rate: 100.0%

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1062:17)

  console.log
      Avg response time: 36ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1063:17)

  console.log
      Throughput: 11.42 files/sec

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1066:17)

  console.log
      Max consecutive failures: 0

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1069:17)

  console.log
      Memory increase: -3.81MB

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1070:17)

  console.log
      System stable: ✅ YES

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1071:17)

  console.log
      ⏳ Cooling down for 2 seconds...

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1100:19)

  console.log
    
    📊 LEVEL 9: Maximum Load (all files)

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:937:17)

  console.log
    --------------------------------------------------

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:940:17)

  console.log
    Memory before: 96.88MB

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:949:17)

  console.log
      ✅ File 1/33: 19ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:982:23)

  console.log
      ✅ File 6/33: 128ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:982:23)

  console.log
      ✅ File 11/33: 33ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:982:23)

  console.log
      ✅ File 16/33: 28ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:982:23)

  console.log
      ✅ File 21/33: 54ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:982:23)

  console.log
      ✅ File 26/33: 22ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:982:23)

  console.log
      ✅ File 31/33: 36ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:982:23)

  console.log
    
    📈 LEVEL 9 RESULTS:

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1060:17)

  console.log
      Files processed: 33

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1061:17)

  console.log
      Success rate: 100.0%

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1062:17)

  console.log
      Avg response time: 33ms

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1063:17)

  console.log
      Throughput: 11.84 files/sec

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1066:17)

  console.log
      Max consecutive failures: 0

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1069:17)

  console.log
      Memory increase: -5.56MB

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1070:17)

  console.log
      System stable: ✅ YES

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1071:17)

  console.log
      ⏳ Cooling down for 2 seconds...

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1100:19)

  console.log
    
    🔍 COMPREHENSIVE GRADIENT ANALYSIS

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1106:15)

  console.log
    ======================================================================

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1107:15)

  console.log
    
    📊 PERFORMANCE BREAKDOWN BY LEVEL:

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1122:15)

  console.log
    Files | Success% | Avg Time | Throughput | Stable | Errors

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1123:15)

  console.log
    -----------------------------------------------------------------

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1124:15)

  console.log
        3 |   100.0% |       39ms |      11.07 |      ✅ | None

      at performance/quiz-upload-performance.e2e-spec.ts:1134:17
          at Array.forEach (<anonymous>)

  console.log
        5 |   100.0% |       47ms |      10.22 |      ✅ | None

      at performance/quiz-upload-performance.e2e-spec.ts:1134:17
          at Array.forEach (<anonymous>)

  console.log
        8 |   100.0% |       38ms |      11.24 |      ✅ | None

      at performance/quiz-upload-performance.e2e-spec.ts:1134:17
          at Array.forEach (<anonymous>)

  console.log
       12 |   100.0% |       38ms |      11.20 |      ✅ | None

      at performance/quiz-upload-performance.e2e-spec.ts:1134:17
          at Array.forEach (<anonymous>)

  console.log
       15 |   100.0% |       37ms |      11.32 |      ✅ | None

      at performance/quiz-upload-performance.e2e-spec.ts:1134:17
          at Array.forEach (<anonymous>)

  console.log
       20 |   100.0% |       36ms |      11.34 |      ✅ | None

      at performance/quiz-upload-performance.e2e-spec.ts:1134:17
          at Array.forEach (<anonymous>)

  console.log
       25 |   100.0% |       33ms |      11.81 |      ✅ | None

      at performance/quiz-upload-performance.e2e-spec.ts:1134:17
          at Array.forEach (<anonymous>)

  console.log
       30 |   100.0% |       36ms |      11.42 |      ✅ | None

      at performance/quiz-upload-performance.e2e-spec.ts:1134:17
          at Array.forEach (<anonymous>)

  console.log
       33 |   100.0% |       33ms |      11.84 |      ✅ | None

      at performance/quiz-upload-performance.e2e-spec.ts:1134:17
          at Array.forEach (<anonymous>)

  console.log
    
    🎯 KEY FINDINGS:

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1144:15)

  console.log
      • Last stable level: 33 files

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1145:15)

  console.log
      • First unstable level: N/A files

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1146:15)

  console.log
      • Best stable performance: 100.0% success, 33ms avg

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1153:19)

  console.log
    
    📉 PERFORMANCE DEGRADATION ANALYSIS:

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1175:15)

  console.log
    
    💾 MEMORY ANALYSIS:

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1204:15)

  console.log
      • Peak memory usage: 91.81MB

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1205:15)

  console.log
      • Memory efficient: ✅ YES

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1208:15)

  console.log
    
    💡 RECOMMENDATIONS:

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1253:15)

  console.log
      ✅ System handles high loads well (stable up to 33 files)

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1255:17)

  console.log
      ✅ Production ready for expected workloads

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1258:17)

  console.log
    ======================================================================

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1275:15)

  console.log
    
    🎉 VERDICT: System is production ready (stable up to 33 files)

      at Object.<anonymous> (performance/quiz-upload-performance.e2e-spec.ts:1283:17)

PASS test/performance/quiz-upload-performance.e2e-spec.ts (39.256 s)
  Quiz Upload Performance Tests
    Batch Upload Performance Tests
      ✓ should handle batch upload of 20 training files with acceptable performance (3010 ms)
    Concurrent Upload Performance Tests
      ✓ should handle 5 simultaneous uploads with acceptable performance (326 ms)
      ✓ should handle 10 rapid sequential uploads with system stability (1042 ms)
    Memory and Resource Monitoring Tests
      ✓ should handle large dataset uploads without memory leaks (2393 ms)
    Stress Testing
      ✓ should handle sustained load with 30 files over extended period (6197 ms)
      ✓ should find the exact breaking point through gradient load testing (24331 ms)

Test Suites: 6 passed, 6 total
Tests:       58 passed, 58 total
Snapshots:   0 total
Time:        39.486 s, estimated 42 s
Ran all test suites.
Running global teardown...
