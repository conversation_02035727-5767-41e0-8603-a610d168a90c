/**
 * Database Cleanup Utility
 *
 * This utility provides functions for consistent database connection cleanup
 * across test files. It helps ensure proper resource management and prevents
 * test timeouts due to unclosed connections.
 */

import { INestApplication } from '@nestjs/common';
import { TestingModule } from '@nestjs/testing';
import { DataSource } from 'typeorm';
import { resetDatabase } from '../database-test.utils';
import { MinioService } from '../../src/minio/minio.service';

// Type to represent either a NestJS application or a testing module
type AppOrModule = INestApplication | TestingModule | null | undefined;

/**
 * Safely closes database connections and NestJS application or testing module
 *
 * @param dataSource TypeORM DataSource instance
 * @param appOrModule NestJS application or testing module instance
 * @returns Promise that resolves when cleanup is complete
 */
export async function cleanupDatabase(
  dataSource: DataSource | null | undefined,
  appOrModule: AppOrModule,
): Promise<void> {
  try {
    // Close the database connection first
    if (dataSource && dataSource.isInitialized) {
      await dataSource.destroy();
    }

    // Then close the application or module
    if (appOrModule) {
      await appOrModule.close();
    }
  } catch (error) {
    console.error('Error during database cleanup:', error);
    // Don't rethrow the error to ensure cleanup continues
  }
}

/**
 * Safely closes database connections and NestJS application or testing module with callback support
 * For use with Jest's done() callback pattern
 *
 * @param dataSource TypeORM DataSource instance
 * @param appOrModule NestJS application or testing module instance
 * @param done Jest's done callback
 */
export function cleanupDatabaseWithCallback(
  dataSource: DataSource | null | undefined,
  appOrModule: AppOrModule,
  done: () => void,
): void {
  cleanupDatabase(dataSource, appOrModule)
    .then(() => {
      done();
    })
    .catch((error) => {
      console.error('Error during database cleanup with callback:', error);
      done();
    });
}

/**
 * Clean all data from the database while keeping connections open
 * This function clears all table data but preserves database connections
 *
 * @param dataSource TypeORM DataSource instance
 * @returns Promise that resolves when data cleanup is complete
 */
export async function cleanupDatabaseData(
  dataSource: DataSource | null | undefined,
): Promise<void> {
  try {
    if (dataSource && dataSource.isInitialized) {
      await resetDatabase(dataSource);
    }
  } catch (error) {
    console.error('Error during database data cleanup:', error);
    throw error; // Rethrow to ensure test failures are visible
  }
}

/**
 * Clean all objects from the MinIO bucket
 * This function removes all files from the default bucket
 *
 * @param minioService MinioService instance
 * @returns Promise that resolves when MinIO cleanup is complete
 */
export async function cleanupMinIOBucket(
  minioService: MinioService | null | undefined,
): Promise<void> {
  try {
    if (!minioService) {
      return;
    }

    const bucketName = minioService.getDefaultBucket();

    // List all objects in the bucket
    const objects = await minioService.listFiles(bucketName);

    // Delete all objects
    for (const objectName of objects) {
      if (objectName) {
        await minioService.deleteFile(bucketName, objectName);
      }
    }
  } catch (error) {
    console.error('Error during MinIO bucket cleanup:', error);
    throw error; // Rethrow to ensure test failures are visible
  }
}

/**
 * Comprehensive test data cleanup function
 * Cleans both database data and MinIO bucket contents
 *
 * @param dataSource TypeORM DataSource instance
 * @param minioService MinioService instance
 * @returns Promise that resolves when all cleanup is complete
 */
export async function cleanupTestData(
  dataSource: DataSource | null | undefined,
  minioService: MinioService | null | undefined,
): Promise<void> {
  const errors: Error[] = [];

  // Clean MinIO first (fail-fast approach)
  try {
    await cleanupMinIOBucket(minioService);
  } catch (error) {
    errors.push(error as Error);
  }

  // Then clean database data
  try {
    await cleanupDatabaseData(dataSource);
  } catch (error) {
    errors.push(error as Error);
  }

  // If any errors occurred, throw them
  if (errors.length > 0) {
    const errorMessage = errors.map((e) => e.message).join('; ');
    throw new Error(`Test data cleanup failed: ${errorMessage}`);
  }
}
