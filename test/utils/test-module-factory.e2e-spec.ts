/**
 * Test Module Factory Enhancement Tests
 *
 * These tests verify that the enhanced test module factory works correctly
 * with the new cleanup options and automatic MinIO integration.
 */

import { TestingModule } from '@nestjs/testing';
import { DataSource } from 'typeorm';
import { MinioService } from '../../src/minio/minio.service';
import {
  createTestModule,
  createTestApp,
  createE2ETestApp,
  createCleanupHooks,
} from './test-module-factory';
import { cleanupDatabase } from './database-cleanup';

describe('Enhanced Test Module Factory', () => {
  let module: TestingModule;
  let dataSource: DataSource;

  afterAll(async () => {
    await cleanupDatabase(dataSource, module);
  });

  describe('createTestModule with cleanup options', () => {
    it('should create module without cleanup options (backward compatibility)', async () => {
      module = await createTestModule({
        isGlobalConfig: true,
      });

      expect(module).toBeDefined();
      dataSource = module.get<DataSource>(DataSource);
      expect(dataSource).toBeDefined();
      expect(dataSource.isInitialized).toBe(true);
    });

    it('should create module with MinIO when includeMinIO is true', async () => {
      const testModule = await createTestModule({
        isGlobalConfig: true,
        cleanup: {
          includeMinIO: true,
        },
      });

      expect(testModule).toBeDefined();

      const minioService = testModule.get<MinioService>(MinioService);
      expect(minioService).toBeDefined();
      expect(minioService).toBeInstanceOf(MinioService);

      // Clean up this test module
      await cleanupDatabase(null, testModule);
    });

    it('should not include MinIO when includeMinIO is false', async () => {
      const testModule = await createTestModule({
        isGlobalConfig: true,
        cleanup: {
          includeMinIO: false,
        },
      });

      expect(testModule).toBeDefined();

      // Should throw when trying to get MinioService since it's not provided
      expect(() => testModule.get<MinioService>(MinioService)).toThrow();

      // Clean up this test module
      await cleanupDatabase(null, testModule);
    });
  });

  describe('createTestApp with cleanup utilities', () => {
    it('should create test app with cleanup utilities when enableDataCleanup is true', async () => {
      const result = await createTestApp({
        isGlobalConfig: true,
        cleanup: {
          enableDataCleanup: true,
          includeMinIO: true,
        },
      });

      expect(result).toBeDefined();
      expect(result.app).toBeDefined();
      expect(result.dataSource).toBeDefined();
      expect(result.minioService).toBeDefined();
      expect(result.cleanup).toBeDefined();

      // Test cleanup utilities
      expect(typeof result.cleanup.cleanupData).toBe('function');
      expect(typeof result.cleanup.cleanupMinIO).toBe('function');
      expect(typeof result.cleanup.cleanupAll).toBe('function');
      expect(typeof result.cleanup.cleanupConnections).toBe('function');
      expect(typeof result.cleanup.cleanupConnectionsWithCallback).toBe(
        'function',
      );

      // Test that cleanup functions work
      await expect(result.cleanup.cleanupData()).resolves.not.toThrow();
      await expect(result.cleanup.cleanupMinIO()).resolves.not.toThrow();
      await expect(result.cleanup.cleanupAll()).resolves.not.toThrow();

      // Clean up
      await result.cleanup.cleanupConnections();
    });

    it('should work with custom cleanup function', async () => {
      let customCleanupCalled = false;
      const customCleanup = (
        dataSource: DataSource,
        minioService?: MinioService,
      ) => {
        customCleanupCalled = true;
        expect(dataSource).toBeDefined();
        expect(minioService).toBeDefined();
        return Promise.resolve();
      };

      const result = await createTestApp({
        isGlobalConfig: true,
        cleanup: {
          enableDataCleanup: true,
          includeMinIO: true,
          customCleanup,
        },
      });

      // Test custom cleanup is called
      await result.cleanup.cleanupData();
      expect(customCleanupCalled).toBe(true);

      // Clean up
      await result.cleanup.cleanupConnections();
    });
  });

  describe('createE2ETestApp backward compatibility', () => {
    it('should maintain backward compatibility when no cleanup options provided', async () => {
      const result = await createE2ETestApp({
        isGlobalConfig: true,
      });

      expect(result).toBeDefined();
      expect(result.app).toBeDefined();
      expect(result.dataSource).toBeDefined();
      expect(result.cleanup).toBeUndefined(); // Should not include cleanup utilities
      expect(result.minioService).toBeUndefined(); // Should not include MinIO service

      // Clean up using traditional method
      await cleanupDatabase(result.dataSource, result.app);
    });

    it('should include cleanup utilities when enableDataCleanup is true', async () => {
      const result = await createE2ETestApp({
        isGlobalConfig: true,
        cleanup: {
          enableDataCleanup: true,
          includeMinIO: true,
        },
      });

      expect(result).toBeDefined();
      expect(result.app).toBeDefined();
      expect(result.dataSource).toBeDefined();
      expect(result.cleanup).toBeDefined(); // Should include cleanup utilities
      expect(result.minioService).toBeDefined(); // Should include MinIO service

      // Clean up using new method
      await result.cleanup!.cleanupConnections();
    });

    it('should include cleanup utilities when withDataCleanup is true (TODOS.md specification)', async () => {
      const result = await createE2ETestApp({
        isGlobalConfig: true,
        cleanup: {
          withDataCleanup: true,
          includeMinIO: true,
        },
      });

      expect(result).toBeDefined();
      expect(result.app).toBeDefined();
      expect(result.dataSource).toBeDefined();
      expect(result.cleanup).toBeDefined(); // Should include cleanup utilities
      expect(result.minioService).toBeDefined(); // Should include MinIO service

      // Clean up using new method
      await result.cleanup!.cleanupConnections();
    });
  });

  describe('createCleanupHooks helper', () => {
    it('should create hooks for test app with cleanup utilities', async () => {
      const result = await createTestApp({
        isGlobalConfig: true,
        cleanup: {
          enableDataCleanup: true,
          includeMinIO: true,
        },
      });

      const hooks = createCleanupHooks(result, {
        cleanupBeforeEach: true,
        cleanupAfterEach: true,
        cleanupAfterAll: true,
      });

      expect(hooks.beforeEach).toBeDefined();
      expect(hooks.afterEach).toBeDefined();
      expect(hooks.afterAll).toBeDefined();

      // Test that hooks work
      await expect(hooks.beforeEach!()).resolves.not.toThrow();
      await expect(hooks.afterEach!()).resolves.not.toThrow();

      // Clean up
      await result.cleanup.cleanupConnections();
    });

    it('should create fallback hooks for backward compatibility', async () => {
      const result = await createE2ETestApp({
        isGlobalConfig: true,
      });

      const hooks = createCleanupHooks(result, {
        cleanupAfterAll: true,
      });

      expect(hooks.beforeEach).toBeUndefined();
      expect(hooks.afterEach).toBeUndefined();
      expect(hooks.afterAll).toBeDefined();

      // Test fallback hook
      await expect(hooks.afterAll!()).resolves.not.toThrow();
    });
  });
});
