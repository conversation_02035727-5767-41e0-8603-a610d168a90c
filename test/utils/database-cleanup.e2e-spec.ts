/**
 * Database Cleanup Utility Tests
 *
 * These tests verify that our comprehensive data cleanup functions work correctly.
 * They use real database and MinIO connections to ensure proper cleanup behavior.
 */

import { TestingModule } from '@nestjs/testing';
import { DataSource } from 'typeorm';
import { MinioService } from '../../src/minio/minio.service';
import {
  cleanupDatabase,
  cleanupDatabaseData,
  cleanupMinIOBucket,
  cleanupTestData,
} from './database-cleanup';
import { createTestModule } from './test-module-factory';
import { ConfigModule } from '@nestjs/config';
import minioConfig from '../../src/config/minio.config';

describe('Database Cleanup Utilities', () => {
  let module: TestingModule;
  let dataSource: DataSource;
  let minioService: MinioService;

  beforeAll(async () => {
    module = await createTestModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          load: [minioConfig],
        }),
      ],
      providers: [MinioService],
      isGlobalConfig: true,
    });

    minioService = module.get<MinioService>(MinioService);

    // Initialize MinIO service
    minioService.onModuleInit();

    // Get DataSource directly from the module (it's already initialized by TypeORM)
    dataSource = module.get<DataSource>(DataSource);
  });

  afterAll(async () => {
    await cleanupDatabase(dataSource, module);
  });

  describe('cleanupDatabaseData', () => {
    it('should clean database data without closing connections', async () => {
      // Verify database is connected
      expect(dataSource.isInitialized).toBe(true);

      // Run cleanup
      await expect(cleanupDatabaseData(dataSource)).resolves.not.toThrow();

      // Verify connection is still open
      expect(dataSource.isInitialized).toBe(true);
    });

    it('should handle null dataSource gracefully', async () => {
      await expect(cleanupDatabaseData(null)).resolves.not.toThrow();
    });

    it('should handle undefined dataSource gracefully', async () => {
      await expect(cleanupDatabaseData(undefined)).resolves.not.toThrow();
    });
  });

  describe('cleanupMinIOBucket', () => {
    const testFileName = 'test-cleanup-file.txt';
    const testFileContent = Buffer.from(
      'Test content for cleanup verification',
    );

    beforeEach(async () => {
      // Clean up any existing test files
      try {
        await minioService.deleteFile(
          minioService.getDefaultBucket(),
          testFileName,
        );
      } catch {
        // Ignore if file doesn't exist
      }
    });

    it('should clean all files from MinIO bucket', async () => {
      const bucketName = minioService.getDefaultBucket();

      // Upload a test file
      await minioService.uploadFile(testFileContent, {
        objectName: testFileName,
        contentType: 'text/plain',
      });

      // Verify file exists
      const filesBeforeCleanup = await minioService.listFiles(bucketName);
      expect(filesBeforeCleanup).toContain(testFileName);

      // Run cleanup
      await expect(cleanupMinIOBucket(minioService)).resolves.not.toThrow();

      // Verify bucket is empty
      const filesAfterCleanup = await minioService.listFiles(bucketName);
      expect(filesAfterCleanup).toHaveLength(0);
    });

    it('should handle null minioService gracefully', async () => {
      await expect(cleanupMinIOBucket(null)).resolves.not.toThrow();
    });

    it('should handle undefined minioService gracefully', async () => {
      await expect(cleanupMinIOBucket(undefined)).resolves.not.toThrow();
    });
  });

  describe('cleanupTestData', () => {
    const testFileName = 'test-comprehensive-cleanup.txt';
    const testFileContent = Buffer.from(
      'Test content for comprehensive cleanup',
    );

    beforeEach(async () => {
      // Clean up any existing test files
      try {
        await minioService.deleteFile(
          minioService.getDefaultBucket(),
          testFileName,
        );
      } catch {
        // Ignore if file doesn't exist
      }
    });

    it('should clean both database and MinIO data', async () => {
      const bucketName = minioService.getDefaultBucket();

      // Upload a test file to MinIO
      await minioService.uploadFile(testFileContent, {
        objectName: testFileName,
        contentType: 'text/plain',
      });

      // Verify file exists
      const filesBeforeCleanup = await minioService.listFiles(bucketName);
      expect(filesBeforeCleanup).toContain(testFileName);

      // Verify database is connected
      expect(dataSource.isInitialized).toBe(true);

      // Run comprehensive cleanup
      await expect(
        cleanupTestData(dataSource, minioService),
      ).resolves.not.toThrow();

      // Verify MinIO bucket is empty
      const filesAfterCleanup = await minioService.listFiles(bucketName);
      expect(filesAfterCleanup).toHaveLength(0);

      // Verify database connection is still open
      expect(dataSource.isInitialized).toBe(true);
    });

    it('should handle null parameters gracefully', async () => {
      await expect(cleanupTestData(null, null)).resolves.not.toThrow();
    });

    it('should handle undefined parameters gracefully', async () => {
      await expect(
        cleanupTestData(undefined, undefined),
      ).resolves.not.toThrow();
    });
  });

  describe('backward compatibility', () => {
    it('should preserve existing cleanupDatabase function behavior', async () => {
      // This test ensures the original cleanupDatabase function still works
      // We can't test connection closing without breaking our test setup,
      // so we just verify it doesn't throw with null parameters
      await expect(cleanupDatabase(null, null)).resolves.not.toThrow();
    });
  });
});
