/**
 * API Specification Validation E2E Tests
 *
 * These tests ensure that the generated OpenAPI specification is valid
 * and meets quality standards. They are part of the test suite to ensure
 * the specification stays valid as the codebase evolves.
 */

import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { existsSync } from 'fs';
import { join } from 'path';
import { AppModule } from '../src/app.module';
import { generateApiSpecification } from '../scripts/generate-api-spec';
import { validateApiSpecification } from '../scripts/validate-api-spec';

describe('API Specification Validation (E2E)', () => {
  let app: INestApplication;
  let module: TestingModule;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = module.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    if (app) {
      await app.close();
    }
    if (module) {
      await module.close();
    }
  });

  describe('API Specification Generation', () => {
    it('should generate API specification files successfully', async () => {
      // Generate the API specification
      await generateApiSpecification();

      // Verify that files were created
      const outputDir = join(process.cwd(), 'docs', 'api');
      const jsonPath = join(outputDir, 'api-spec.json');
      const yamlPath = join(outputDir, 'api-spec.yaml');
      const summaryPath = join(outputDir, 'api-summary.json');

      expect(existsSync(jsonPath)).toBe(true);
      expect(existsSync(yamlPath)).toBe(true);
      expect(existsSync(summaryPath)).toBe(true);
    }, 30000); // Allow 30 seconds for generation

    it('should generate specification with expected content structure', () => {
      const jsonPath = join(process.cwd(), 'docs', 'api', 'api-spec.json');
      expect(existsSync(jsonPath)).toBe(true);

      // eslint-disable-next-line @typescript-eslint/no-require-imports
      const spec = require(jsonPath);

      // Verify basic OpenAPI structure
      expect(spec.openapi).toMatch(/^3\./);
      expect(spec.info).toBeDefined();
      expect(spec.info.title).toBe('Teaching Material System (TMS) REST API');
      expect(spec.info.version).toBe('0.0.1');
      expect(spec.paths).toBeDefined();
      expect(spec.components).toBeDefined();

      // Verify that we have the expected endpoints
      expect(spec.paths['/health']).toBeDefined();
      expect(
        spec.paths['/quiz/f2f/paperless-marking-worked-solutions'],
      ).toBeDefined();
      expect(spec.paths['/quiz/{id}']).toBeDefined();

      // Verify security schemes
      expect(spec.components.securitySchemes).toBeDefined();
      expect(spec.components.securitySchemes.basic).toBeDefined();

      // Verify schemas
      expect(spec.components.schemas).toBeDefined();
      expect(Object.keys(spec.components.schemas).length).toBeGreaterThan(0);
    });

    it('should generate API summary with correct statistics', () => {
      const summaryPath = join(
        process.cwd(),
        'docs',
        'api',
        'api-summary.json',
      );
      expect(existsSync(summaryPath)).toBe(true);

      // eslint-disable-next-line @typescript-eslint/no-require-imports
      const summary = require(summaryPath);

      expect(summary.title).toBe('Teaching Material System (TMS) REST API');
      expect(summary.version).toBe('0.0.1');
      expect(summary.openApiVersion).toMatch(/^3\./);
      expect(summary.endpoints).toBeGreaterThan(0);
      expect(summary.schemas).toBeGreaterThan(0);
      expect(summary.securitySchemes).toBeGreaterThan(0);
      expect(summary.generatedAt).toBeDefined();
      expect(new Date(summary.generatedAt)).toBeInstanceOf(Date);
    });
  });

  describe('API Specification Validation', () => {
    it('should validate the generated specification successfully', async () => {
      const result = await validateApiSpecification();

      expect(result).toBeDefined();
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);

      // Log warnings if any (they don't fail the test but are good to know)
      if (result.warnings.length > 0) {
        console.log('⚠️  Validation warnings:');
        result.warnings.forEach((warning) => console.log(`   - ${warning}`));
      }
    }, 15000); // Allow 15 seconds for validation

    it('should have comprehensive endpoint coverage', async () => {
      const result = await validateApiSpecification();

      expect(result.summary.endpoints).toBeGreaterThanOrEqual(3); // health + quiz endpoints
      expect(result.summary.schemas).toBeGreaterThanOrEqual(10); // DTOs + error schemas
      expect(result.summary.securitySchemes).toBeGreaterThanOrEqual(1); // basic auth
    });

    it('should have proper security coverage', async () => {
      const result = await validateApiSpecification();

      // Most operations should have authentication (except health check)
      expect(result.summary.operationsWithAuth).toBeGreaterThanOrEqual(4); // quiz CRUD operations
    });

    it('should meet quality standards', async () => {
      const result = await validateApiSpecification();

      // Should have minimal warnings for a production-ready API
      // Note: Some warnings are expected for test endpoints and optional schema properties
      expect(result.warnings.length).toBeLessThanOrEqual(15);

      // Should have no critical errors
      expect(result.errors.length).toBe(0);

      // Should have good schema coverage
      expect(result.summary.schemas).toBeGreaterThanOrEqual(10);
    });
  });

  describe('Specification Consistency', () => {
    it('should have consistent JSON and YAML formats', () => {
      const jsonPath = join(process.cwd(), 'docs', 'api', 'api-spec.json');
      const yamlPath = join(process.cwd(), 'docs', 'api', 'api-spec.yaml');

      expect(existsSync(jsonPath)).toBe(true);
      expect(existsSync(yamlPath)).toBe(true);

      // eslint-disable-next-line @typescript-eslint/no-require-imports
      const jsonSpec = require(jsonPath);

      // Basic consistency checks
      expect(jsonSpec.info.title).toBe(
        'Teaching Material System (TMS) REST API',
      );
      expect(jsonSpec.info.version).toBe('0.0.1');

      // Both files should exist and be non-empty
      // eslint-disable-next-line @typescript-eslint/no-require-imports
      const fs = require('fs');
      const jsonContent = fs.readFileSync(jsonPath, 'utf8') as string;
      const yamlContent = fs.readFileSync(yamlPath, 'utf8') as string;

      expect(jsonContent.length).toBeGreaterThan(1000); // Should be substantial
      expect(yamlContent.length).toBeGreaterThan(1000); // Should be substantial
      expect(yamlContent).toContain('Teaching Material System'); // Should contain our API title
    });

    it('should include all documented endpoints', () => {
      const jsonPath = join(process.cwd(), 'docs', 'api', 'api-spec.json');
      // eslint-disable-next-line @typescript-eslint/no-require-imports
      const spec = require(jsonPath);

      // Expected endpoints based on our controller
      const expectedPaths = [
        '/health',
        '/quiz/f2f/paperless-marking-worked-solutions',
        '/quiz/{id}',
      ];

      expectedPaths.forEach((path) => {
        expect(spec.paths[path]).toBeDefined();
      });
    });

    it('should include all error response schemas', () => {
      const jsonPath = join(process.cwd(), 'docs', 'api', 'api-spec.json');
      // eslint-disable-next-line @typescript-eslint/no-require-imports
      const spec = require(jsonPath);

      const expectedSchemas = [
        'ErrorResponseSchema',
        'ValidationErrorResponseSchema',
        'AuthenticationErrorResponseSchema',
        'NotFoundErrorResponseSchema',
        'FileTooLargeErrorResponseSchema',
        'InternalServerErrorResponseSchema',
      ];

      expectedSchemas.forEach((schema) => {
        expect(spec.components.schemas[schema]).toBeDefined();
      });
    });
  });

  describe('Integration with Test Suite', () => {
    it('should be able to regenerate specification during CI/CD', async () => {
      // This test ensures that specification generation works in automated environments
      const startTime = Date.now();

      await generateApiSpecification();
      const result = await validateApiSpecification();

      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(result.isValid).toBe(true);
      expect(duration).toBeLessThan(45000); // Should complete within 45 seconds
    }, 50000);

    it('should maintain specification validity across code changes', async () => {
      // This test ensures that the specification remains valid
      // even as the codebase evolves
      const result = await validateApiSpecification();

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);

      // Ensure we maintain good coverage
      expect(result.summary.endpoints).toBeGreaterThan(0);
      expect(result.summary.schemas).toBeGreaterThan(0);
      expect(result.summary.securitySchemes).toBeGreaterThan(0);
    });
  });
});
