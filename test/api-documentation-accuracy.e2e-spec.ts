/**
 * API Documentation Accuracy E2E Tests
 *
 * These tests verify that the actual API behavior matches the documented
 * OpenAPI specification. They test response schemas, status codes, and
 * error handling against the generated specification.
 */

import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { readFileSync, existsSync } from 'fs';
import { join } from 'path';
import { AppModule } from '../src/app.module';
import { ValidationPipe } from '../src/pipes/validation.pipe';
import { GlobalExceptionFilter } from '../src/filters/global-exception.filter';
import { LoggingInterceptor } from '../src/interceptors/logging.interceptor';
import { createTestModule } from './utils/test-module-factory';
import { TEST_CREDENTIALS, TEST_CORRELATION_ID } from './test-constants';

describe('API Documentation Accuracy (E2E)', () => {
  let app: INestApplication;
  let module: TestingModule;
  let apiSpec: any;

  beforeAll(async () => {
    // Load the generated API specification
    const specPath = join(process.cwd(), 'docs', 'api', 'api-spec.json');
    if (!existsSync(specPath)) {
      throw new Error(
        `API specification not found at ${specPath}. Run 'npm run api:generate' first.`,
      );
    }

    apiSpec = JSON.parse(readFileSync(specPath, 'utf8'));

    // Create test module
    module = await createTestModule({
      imports: [AppModule],
    });
    app = module.createNestApplication();

    // Apply the same configuration as main.ts
    app.useGlobalPipes(new ValidationPipe());
    app.useGlobalFilters(new GlobalExceptionFilter());
    app.useGlobalInterceptors(new LoggingInterceptor());

    await app.init();
  });

  afterAll(async () => {
    if (app) {
      await app.close();
    }
    if (module) {
      await module.close();
    }
  });

  describe('API Specification Structure', () => {
    it('should have valid OpenAPI 3.0 structure', () => {
      expect(apiSpec).toBeDefined();
      expect(apiSpec.openapi).toMatch(/^3\./);
      expect(apiSpec.info).toBeDefined();
      expect(apiSpec.info.title).toBe(
        'Teaching Material System (TMS) REST API',
      );
      expect(apiSpec.info.version).toBe('0.0.1');
      expect(apiSpec.paths).toBeDefined();
      expect(apiSpec.components).toBeDefined();
    });

    it('should have security schemes defined', () => {
      expect(apiSpec.components.securitySchemes).toBeDefined();
      expect(apiSpec.components.securitySchemes.basic).toBeDefined();
      expect(apiSpec.components.securitySchemes.basic.type).toBe('http');
      expect(apiSpec.components.securitySchemes.basic.scheme).toBe('basic');
    });

    it('should have error response schemas defined', () => {
      const schemas = apiSpec.components.schemas;
      expect(schemas.ErrorResponseSchema).toBeDefined();
      expect(schemas.ValidationErrorResponseSchema).toBeDefined();
      expect(schemas.AuthenticationErrorResponseSchema).toBeDefined();
      expect(schemas.NotFoundErrorResponseSchema).toBeDefined();
    });
  });

  describe('Health Endpoint Documentation', () => {
    it('should match documented response for GET /health', async () => {
      // Note: Health endpoint requires correlation ID header
      const response = await request(app.getHttpServer())
        .get('/health')
        .expect(400); // Expects 400 because correlation ID is missing

      // Check that the endpoint is documented in the spec
      expect(apiSpec.paths['/health']).toBeDefined();
      expect(apiSpec.paths['/health'].get).toBeDefined();
      expect(apiSpec.paths['/health'].get.responses['200']).toBeDefined();
    });
  });

  describe('Quiz Endpoints Documentation', () => {
    const validAuth = TEST_CREDENTIALS.BASIC_AUTH_HEADER;
    const correlationId = TEST_CORRELATION_ID;

    it('should document GET /quiz/f2f/paperless-marking-worked-solutions endpoint', () => {
      const path = '/quiz/f2f/paperless-marking-worked-solutions';
      expect(apiSpec.paths[path]).toBeDefined();
      expect(apiSpec.paths[path].get).toBeDefined();

      const operation = apiSpec.paths[path].get;
      expect(operation.summary).toBeDefined();
      expect(operation.description).toBeDefined();
      expect(operation.parameters).toBeDefined();
      expect(operation.responses).toBeDefined();
      expect(operation.security).toBeDefined();
    });

    it('should document POST /quiz/f2f/paperless-marking-worked-solutions endpoint', () => {
      const path = '/quiz/f2f/paperless-marking-worked-solutions';
      expect(apiSpec.paths[path]).toBeDefined();
      expect(apiSpec.paths[path].post).toBeDefined();

      const operation = apiSpec.paths[path].post;
      expect(operation.summary).toBeDefined();
      expect(operation.description).toBeDefined();
      expect(operation.parameters).toBeDefined(); // Uses form parameters instead of requestBody
      expect(operation.responses).toBeDefined();
      expect(operation.security).toBeDefined();
    });

    it('should document PUT /quiz/{id} endpoint', () => {
      const path = '/quiz/{id}';
      expect(apiSpec.paths[path]).toBeDefined();
      expect(apiSpec.paths[path].put).toBeDefined();

      const operation = apiSpec.paths[path].put;
      expect(operation.summary).toBeDefined();
      expect(operation.description).toBeDefined();
      expect(operation.parameters).toBeDefined();
      expect(operation.responses).toBeDefined();
      expect(operation.security).toBeDefined();
    });

    it('should document DELETE /quiz/{id} endpoint', () => {
      const path = '/quiz/{id}';
      expect(apiSpec.paths[path]).toBeDefined();
      expect(apiSpec.paths[path].delete).toBeDefined();

      const operation = apiSpec.paths[path].delete;
      expect(operation.summary).toBeDefined();
      expect(operation.description).toBeDefined();
      expect(operation.parameters).toBeDefined();
      expect(operation.responses).toBeDefined();
      expect(operation.security).toBeDefined();
    });

    it('should match documented error response for missing authentication', async () => {
      const response = await request(app.getHttpServer())
        .get('/quiz/f2f/paperless-marking-worked-solutions')
        .set('X-Correlation-ID', correlationId)
        .expect(401);

      // Verify error response structure matches documentation
      expect(response.body).toHaveProperty('statusCode', 401);
      expect(response.body).toHaveProperty('message');
      expect(response.body).toHaveProperty('correlationId', correlationId);

      // Check that 401 response is documented
      const path = '/quiz/f2f/paperless-marking-worked-solutions';
      expect(apiSpec.paths[path].get.responses['401']).toBeDefined();
    });

    it('should match documented error response for validation errors', async () => {
      const response = await request(app.getHttpServer())
        .get('/quiz/f2f/paperless-marking-worked-solutions')
        .set('Authorization', `Basic ${validAuth}`)
        .set('X-Correlation-ID', correlationId)
        .query({ grade: 'invalid' }) // Invalid grade should trigger validation error
        .expect(400);

      // Verify validation error response structure
      expect(response.body).toHaveProperty('statusCode', 400);
      expect(response.body).toHaveProperty('message');
      expect(response.body).toHaveProperty('details');
      expect(response.body).toHaveProperty('correlationId', correlationId);

      // Check that 400 response is documented
      const path = '/quiz/f2f/paperless-marking-worked-solutions';
      expect(apiSpec.paths[path].get.responses['400']).toBeDefined();
    });
  });

  describe('Schema Validation', () => {
    it('should have all DTO schemas properly defined', () => {
      const schemas = apiSpec.components.schemas;

      // Check that main DTOs are documented (based on actual generated schemas)
      expect(schemas.QuizResponseDto).toBeDefined();
      expect(schemas.QuizUploadResponseDto).toBeDefined();
      expect(schemas.RetrievedMetadataDto).toBeDefined();
      expect(schemas.QuizUploadDataDto).toBeDefined();
    });

    it('should have proper validation constraints in schemas', () => {
      const quizResponseSchema = apiSpec.components.schemas.QuizResponseDto;
      expect(quizResponseSchema).toBeDefined();
      expect(quizResponseSchema.properties).toBeDefined();

      // Check that required fields are marked as required
      expect(quizResponseSchema.required).toContain('id');
      expect(quizResponseSchema.required).toContain('retrievedMetadata');
      expect(quizResponseSchema.required).toContain('gifUrls');

      // Check that validation constraints are documented
      const idProperty = quizResponseSchema.properties.id;
      expect(idProperty.type).toBe('number');
      expect(idProperty.minimum).toBeDefined();
    });
  });

  describe('Security Documentation', () => {
    it('should document all endpoints as requiring authentication', () => {
      const protectedPaths = [
        '/quiz/f2f/paperless-marking-worked-solutions',
        '/quiz/{id}',
      ];

      protectedPaths.forEach((path) => {
        const pathItem = apiSpec.paths[path];
        expect(pathItem).toBeDefined();

        // Check each HTTP method in the path
        Object.keys(pathItem).forEach((method) => {
          if (method !== 'parameters') {
            const operation = pathItem[method];
            expect(operation.security).toBeDefined();
            expect(operation.security.length).toBeGreaterThan(0);
          }
        });
      });
    });

    it('should document correlation ID header requirement', () => {
      const protectedPaths = [
        '/quiz/f2f/paperless-marking-worked-solutions',
        '/quiz/{id}',
      ];

      protectedPaths.forEach((path) => {
        const pathItem = apiSpec.paths[path];
        Object.keys(pathItem).forEach((method) => {
          if (method !== 'parameters') {
            const operation = pathItem[method];
            // Check if correlation ID is documented in parameters or globally
            const hasCorrelationId =
              operation.parameters?.some(
                (param: any) => param.name === 'X-Correlation-ID',
              ) ||
              operation.security?.some(
                (security: any) => security['correlation-id'] !== undefined,
              );
            expect(hasCorrelationId).toBeTruthy();
          }
        });
      });
    });
  });
});
