/**
 * Global setup for Jest
 *
 * This file is used to set up the test environment before tests run.
 * It configures timers to ensure they don't prevent test exit.
 */

interface NodeTimer {
  unref?: () => void;
}

type TimerHandler = string | ((...args: unknown[]) => void);

interface NodeError extends Error {
  code?: string;
}

export default (): void => {
  console.log('Running global setup...');

  // Ensure timers don't prevent test exit by calling unref() on them
  const originalSetTimeout = global.setTimeout;
  global.setTimeout = ((
    handler: TimerHandler,
    timeout?: number,
    ...args: unknown[]
  ): NodeJS.Timeout => {
    const timer = originalSetTimeout(handler, timeout, ...args) as NodeTimer;
    if (timer && typeof timer.unref === 'function') {
      timer.unref();
    }
    return timer as NodeJS.Timeout;
  }) as typeof global.setTimeout;

  const originalSetInterval = global.setInterval;
  global.setInterval = ((
    handler: Timer<PERSON><PERSON><PERSON>,
    timeout?: number,
    ...args: unknown[]
  ): NodeJS.Timeout => {
    const timer = originalSetInterval(handler, timeout, ...args) as NodeTimer;
    if (timer && typeof timer.unref === 'function') {
      timer.unref();
    }
    return timer as NodeJS.Timeout;
  }) as typeof global.setInterval;

  // Note: Removed process.nextTick patching as it was causing EPIPE errors during test teardown
  // Jest handles process cleanup automatically, so manual nextTick patching is not needed

  // Handle EPIPE errors on stdout/stderr to prevent test failures
  process.stdout.on('error', (error: NodeError) => {
    if (error.code === 'EPIPE') {
      // Silently ignore EPIPE errors on stdout during test teardown
      return;
    }
    console.error('stdout error:', error);
  });

  process.stderr.on('error', (error: NodeError) => {
    if (error.code === 'EPIPE') {
      // Silently ignore EPIPE errors on stderr during test teardown
      return;
    }
    console.error('stderr error:', error);
  });

  // We can't set Jest timeout here as jest is not in global scope
  // The timeout is set in jest.config.js instead

  console.log('Global setup completed');
};
