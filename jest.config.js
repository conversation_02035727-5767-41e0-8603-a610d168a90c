module.exports = {
  moduleFileExtensions: ['js', 'json', 'ts'],
  rootDir: 'src',
  testRegex: '.*\\.spec\\.ts$',
  transform: {
    '^.+\\.(t|j)s$': 'ts-jest',
  },
  collectCoverageFrom: ['**/*.(t|j)s'],
  coverageDirectory: '../coverage',
  testEnvironment: 'node',
  globalSetup: '../test/setup.ts',
  globalTeardown: '../test/teardown.ts',
  testTimeout: 60000, // Increase timeout to 60 seconds
  // Add additional Jest configuration for better handling of async operations
  verbose: true,
  // Properly handle promises in tests
  testRunner: 'jest-circus/runner',
  // Set a reasonable time for the test runner to wait for pending operations
  slowTestThreshold: 10, // Mark tests as slow if they take more than 10 seconds
  // Force exit after tests complete
  forceExit: true,
  // Detect open handles that might prevent Je<PERSON> from exiting
  detectOpenHandles: true,
};
