# Product Requirements Document: Teaching Material System (TMS) REST API

**Date:** May 22, 2025
**Author:** <PERSON>

**AUTHORITATIVE SOURCE**: This document is fully aligned with the Design Doc specification at `/Users/<USER>/tms/Design Doc for Teaching Material System (TMS) REST API.html` which serves as the single source of truth. All requirements in this PRD conform to the Design Doc.

**SCOPE**: F2F paperless marking worked solutions for quizzes only. The Design Doc specifies exactly 2 endpoints:
- GET `/quiz/f2f/paperless-marking-worked-solutions`
- POST `/quiz/f2f/paperless-marking-worked-solutions`

**ADDITIONAL SCOPE**: PUT and DELETE endpoints (not specified in Design Doc) are included per user requirements.

## 1. Introduction & Overview

This document outlines the requirements for the Teaching Material System (TMS) REST API. The TMS API will serve as a centralized, internal service for storing, retrieving, updating, and deleting quiz materials. The scope is focused specifically on F2F paperless marking worked solutions for quizzes.

The primary purpose of the TMS API is to provide a robust and standardized way for other internal services to programmatically access and manage quiz content, streamlining content workflows and ensuring consistency.

## 2. Goals & Objectives

### 2.1. Product Goals

* **Centralize Quiz Material Management:** Establish a single source of truth for F2F paperless marking worked solution quiz materials.
* **Enable Programmatic Access:** Allow other internal systems (e.g. learning platforms, content management tools) to efficiently access and utilize teaching materials via a well-defined API.
* **Improve Content Workflow Efficiency:** Reduce manual effort and time spent in managing, distributing, and updating teaching content.
* **Ensure Content Accuracy & Consistency:** Ensuring users access the correct and most current materials.

### 2.2. Measurable Objectives

* Achieve an API uptime of >99.9% with reliable responses.

## 3. Target Audience & Users

### 3.1. Primary Users (Client Systems)

* **Internal System:** Requires access to quiz worked solutions for distribution to markers.
* **Internal Content Manager:** Needs to push and pull quiz materials for display or packaging.

### 3.2. Indirect Beneficiaries (End Users)

* **Educators/Teachers:** Will benefit from easier access to materials, streamlined curriculum updates, and potentially less time spent on manual document management.
* **Students:** Will receive accurate and timely access to learning materials (student copies, and worked solutions where appropriate) via integrated platforms.
* **Content Administrators:** Will have a more efficient and less error-prone method for managing the lifecycle of teaching materials.

### 3.3. User Needs & Pain Points Addressed

* **Current State Pain Points:**
  * Difficulty in locating the latest versions of materials.
  * Time-consuming manual processes for distributing updates.
  * Lack of a centralized system leading to potential inconsistencies across different access points.
  * Inability for other systems to easily integrate and use teaching materials programmatically.
* **How TMS API Addresses Them:**
  * Provides a single, queryable source for all materials.
  * Allows for automated updates and retrieval by client systems.
  * Ensures consistency as all client systems pull from the same source.

## 4. Proposed Solution

The TMS API will be a stateless, HTTP-based RESTful service. It will handle CRUD (Create, Read, Update, Delete) operations for teaching materials. The application will be containerized using Docker to ensure consistency across development and production environments.

### 4.1. System Architecture

* **Application Framework:** Node.js with Nest.js
* **Containerization:** Docker
* **Database:** PostgreSQL for persistent storage of material metadata and references.
* **File Storage:** Minio (an S3-like object storage service) for storing the actual material files (e.g., PDFs, ZIPs containing JSON/images, GIFs). The API will store links/paths to these files.
* **Authentication:** Basic Authentication will be required for all protected endpoints.
* **Deployment:** The API will be deployed as a Docker container on an Ubuntu server. For development, Docker will be used on macOS (Macbook). PM2 or a similar process manager can be used *within* the Docker container if needed for managing the Node.js application.

### 4.2. Core Components

* **API Server:** Handles incoming HTTP requests, routes them, and returns appropriate HTTP responses.
* **Controller Layer:** Defines API endpoints, manages request validation, and orchestrates response generation.
* **Service Layer:** Encapsulates the business logic for operations on teaching materials (e.g., fetching metadata, interacting with file storage).
* **Repository Layer:** Manages data access and interaction with the PostgreSQL database.
* **Authentication Middleware:** Verifies credentials for protected endpoints using Basic Auth.

### 4.3. General API Conventions

* **Stateless:** Each request from a client will contain all information needed to understand the request.
* **HTTPS:** All API communication will be over HTTPS.
* **`X-Correlation-ID` Header:** Every API request **must** include an `X-Correlation-ID` header (UUID format, e.g., `123e4567-e89b-12d3-a456-************`). This ID will be logged for request tracing and debugging purposes.
* **Error Responses:** Errors will be returned as a JSON object containing a `statusCode` (reflecting HTTP status), a descriptive `message`, optional `details`, `correlationId`, `timestamp`, and `path` fields. This format exactly matches the Design Doc specification.
  ```json
  {
    "statusCode": 404,
    "message": "No quiz found matching the provided criteria",
    "details": "Additional error information (optional)",
    "correlationId": "123e4567-e89b-12d3-a456-************",
    "timestamp": "2025-05-24T21:45:23.926Z",
    "path": "/quiz/f2f/paperless-marking-worked-solutions"
  }
  ```
* **Input Validation:** All user-supplied input (query parameters, request bodies) will be rigorously validated and sanitized to prevent common vulnerabilities.
* **API Versioning:** No API versioning will be implemented in the initial release.

## 5. Features & Functionality (Endpoints)

The API supports CRUD operations for Quiz materials only, specifically F2F paperless marking worked solutions as specified in the Design Doc.

**Supported Material Categories:**

* `quizzes` (F2F paperless marking worked solutions only)

**Material Versions:**

* `worked-solutions` (only version currently supported)

### 5.1. Quizzes

#### 5.1.1. `GET /quiz/f2f/paperless-marking-worked-solutions`

* **Description:** Retrieves one or more F2F paperless marking worked solution quizzes based on specified filters.
  * **Note:** This endpoint path aligns with the Design Doc specification for paperless marking worked solutions.
* **Authentication:** Basic Auth required.
* **Query Parameters:**
  * `grade` (integer, Required, e.g., `12`)
  * `subject` (string, Required, e.g., `Math`)
  * `course` (string, Required, e.g., `3U`)
  * `classLevel` (string, Required, e.g., `A1`)
  * `color` (string, Required, e.g., `R`)
  * `year` (integer, Required, e.g., `2025`)
  * `term` (integer, Required, e.g., `2`)
  * `week` (integer, Required, e.g., `4`)
  * `weekType` (string, Required, e.g., `normal` or `holiday`)
  * `teachingProgram` (string, Optional, e.g., `St George Girls`)
  * `lessonName` (string, Optional, e.g., `Formulae`)
* **Request Example (worked-solutions):**
  ```
  GET /quiz/f2f/paperless-marking-worked-solutions?grade=12&subject=Math&course=3U&classLevel=A1&year=2025&term=2&week=4&weekType=normal&lessonName=Formulae
  ```
* **Success Response Example (200 OK for worked-solutions):**
  ```json
  {
    "id": 1, // Unique ID of the material entry
    "retrievedMetadata": {
      "subject": "Math",
      "grade": 12,
      "classLevel": "A1",
      "lessonName": "Formulae",
      "color": "R",
      "year": 2025,
      "term": 1,
      "week": 1,
      "weekType": "normal",
      "course": "",
      "teachingProgram": "",
      "internalMetadata": [
        {
          "questionId": "2013",
          "questionNumber": "1",
          "smilFile": "Q2013.smil",
          "marksJson": "[{\"index\": \"0\", \"mark\": 0}]"
        },
        {
          "questionId": "1137",
          "questionNumber": "2(c)",
          "smilFile": "Q1137.smil",
          "marksJson": "[{\"index\": \"0.0\", \"mark\": 2}, {\"index\": \"0.1\", \"mark\": 3}]"
        }
      ], // Content from QzF2f.json from .zip file as a JSON array
      "originalFilename": "Dr Du_Math__V6_Y09_Formulae_A1.R_for_Y09__F2F QZ.zip",
      "uploadTimestamp": "2025-05-02T01:38:23.926Z"
    },
    "gifUrls": [
      {
        "id": "3421",
        "url": "https://tms-minio:9000/questions-work-solutions/3421.gif?X-Amz-Algorithm=AWS4-HMAX-SHA..."
      },
      {
        "id": "1234",
        "url": "https://tms-minio:9000/questions-work-solutions/1234.gif?X-Amz-Algorithm=AWS4-HMAX-SHA..."
      }
    ]
  }
  ```

#### 5.1.2. `POST /quiz/f2f/paperless-marking-worked-solutions`

* **Description:** Uploads a new F2F paperless marking worked solution quiz. The request body contains a `.zip` file with `LessonMetadata.json`, `QzF2f.json`, and solution GIF files.
* **Authentication:** Basic Auth required.
* **Query Parameters:**
  * `year` (integer, Required, e.g., `2025`)
  * `term` (integer, Required, e.g., `2`)
  * `week` (integer, Required, e.g., `4`)
  * `weekType` (string, Required, e.g., `normal` or `holiday`)
  * `teachingProgram` (string, Optional, e.g., `St George Girls`)
* **Request Body:**
  * **Content-Type:** multipart/form-data
  * **Field:** `file` (required) - A `.zip` file containing:
    * `LessonMetadata.json` - Contains `{"grade": 9, "subject": "Math", "course": "", "classLevel": "A1", "color": "R", "topic": "Trigonometry"}`
    * `QzF2f.json` - Contains array of quiz questions with questionId, questionNumber, smilFile, and marksJson
    * `solution/` directory - Contains GIF files named by questionId (e.g., `1137.gif`, `2013.gif`)
* **Request Example:**
  ```
  POST /quiz/f2f/paperless-marking-worked-solutions?year=2025&term=2&week=4&weekType=normal
  Content-Type: multipart/form-data

  file: Dr Du_Math__V2_Y09_Trigonometry_A1.R_for_Y09__F2F QZ.zip
  ```
* **Success Response Example (201 Created):**
  ```json
  {
    "message": "Quiz worked solutions uploaded and processed successfully",
    "data": {
      "quizId": 42,
      "metadataPath": "metadata/Math/9/A1/2025/2/3/normal/metadata.json",
      "uploadedGifs": [
        {
          "questionId": "2013",
          "objectName": "gifs/6a2e4ff8-34a3-44cd-a587-d9baabf5544c.gif"
        },
        {
          "questionId": "1137",
          "objectName": "gifs/c6fa9f4c-6a6e-48eb-950b-38baf25375c5.gif"
        },
        {
          "questionId": "3421",
          "objectName": "gifs/44b6aa6b-fe6d-499a-b623-974b32e5f62b.gif"
        },
        {
          "questionId": "1234",
          "objectName": "gifs/741876d0-f7a5-42a9-bfd7-440912bed735.gif"
        }
      ],
      "gifCount": 4,
      "extractedMetadata": {
        "subject": "Math",
        "grade": 9,
        "classLevel": "A1",
        "color": "R",
        "course": "",
        "topic": "Trigonometry"
      },
      "uploadTimestamp": "2025-05-24T21:45:23.926Z",
      "originalFilename": "Dr Du_Math__V2_Y09_Trigonometry_A1.R_for_Y09__F2F QZ.zip"
    }
  }
  ```

#### 5.1.3. `PUT /quiz/{id}`
* **Description:** Updates an existing quiz specified by `{id}`. The request body would contain metadata to be updated and optionally a new file.

#### 5.1.4. `DELETE /quiz/{id}`
* **Description:** Deletes a quiz specified by `{id}`.



### 5.2. File Upload

* **Upload Process:**
  1. Client uploads a `.zip` file and temporal metadata (year, term, week, weekType, teachingProgram) to the POST endpoint `POST /quiz/f2f/paperless-marking-worked-solutions`.
  2. The API server receives the file and extracts:
     - `LessonMetadata.json` - Contains grade, subject, course, classLevel, color, topic
     - `QzF2f.json` - Contains quiz question structure and marking information
     - `solution/*.gif` files - Contains worked solution images
  3. The extracted `QzF2f.json` is parsed and stored as an array in the `internalMetadata` field in PostgreSQL.
  4. The GIF files are uploaded to Minio with generated UUIDs as filenames.
  5. Metadata from `LessonMetadata.json` combined with query parameters is stored in PostgreSQL.
  6. Pre-signed URLs for the GIF files are generated for API responses.

## 6. User Interaction & Design (API Client Perspective)

* **Authentication Flow:**
  * Client system obtains Basic Auth credentials.
  * For every request to a protected TMS API endpoint, the client includes the `Authorization` header with `Basic <credentials>`.
* **Typical Material Retrieval Flow (e.g., Quiz Worked Solution):**
  * Client needs a specific quiz worked solution.
  * Client constructs a `GET` request to `/quiz/f2f/paperless-marking-worked-solutions` with appropriate query parameters and the `X-Correlation-ID` header.
  * TMS authenticates the request, validates parameters, queries data, and returns the JSON response.
  * Client parses the JSON.
* **Material Upload Flow:**
  * Client initiates a `POST` request to `/quiz/f2f/paperless-marking-worked-solutions`.
  * Request includes temporal metadata as query parameters and the `.zip` file as multipart form data.
  * TMS authenticates, validates, processes the file (extracts `LessonMetadata.json` and `QzF2f.json`), stores GIF files to Minio, and saves combined metadata to PostgreSQL.
  * TMS returns a `201 Created` response with upload confirmation details.

## 7. Technical Considerations

* **Containerization & Orchestration:** Using Docker will simplify deployment and ensure environment consistency. A `Dockerfile` will define the application image. For more complex deployments in the future, Docker Compose or Kubernetes could be considered but are out of scope for V1.
* **Scalability:** Stateless design and containerization with Docker facilitate horizontal scaling.
* **Database Schema:** (to be determined)
* **Transaction Management:** Ensure atomic operations for database writes and file storage operations.
* **Logging:** Comprehensive logging with `X-Correlation-ID`.
* **Configuration Management:** Externalize configuration (DB connections, Minio details, auth credentials) via environment variables, suitable for Dockerized deployments.

## 8. Security Considerations

* **HTTPS:** Enforce HTTPS.
* **Authentication:** Basic Auth.
* **Input Validation & Sanitization.**
* **Principle of Least Privilege.**
* **Sensitive Data:** Avoid logging sensitive data.
* **Docker Security:** Use official base images, scan images for vulnerabilities, and run containers with non-root users where possible.

## 9. Future Considerations & Out of Scope

### 9.1. Future Improvements
- Rate Limiting: Implement to prevent abuse and ensure fair usage by client systems.
- IP Whitelisting: Restrict API access to known internal IP addresses/ranges for an additional layer of security.
- Advanced Logging & Monitoring: Integrate with a dedicated monitoring solution (e.g., Datadog, Splunk, Grafana Loki, New Relic) for better observability, alerting, and automated log analysis.
- More Granular Permissions/Roles: If usage expands, more sophisticated RBAC might be needed beyond shared Basic Auth.
- Webhook Notifications: For notifying client systems of material updates.
- Full-text Search: More advanced search capabilities across material metadata or content.


### 9.2. Out of Scope (Version 1.0)
* API Versioning.
* Complex Material Versioning (beyond `student-copy`/`worked-solutions`).
* Support for material types beyond Quizzes (Homework and Lecture Notes are out of scope).
* User Interface (UI) for direct interaction with this API.
* Advanced container orchestration (e.g., Kubernetes).

## 10. Release Criteria / Definition of Done

* **Dockerfile created and application successfully builds and runs in Docker locally (macOS) and on a target Ubuntu environment.**
* All defined CRUD endpoints for Quizzes are implemented and unit tested.
* Integration tests cover key API flows.
* Authentication (Basic Auth) is implemented and enforced.
* `X-Correlation-ID` handling and logging are implemented.
* Input validation is in place for all endpoints.
* File handling (upload to Minio, metadata extraction for `internalMetadata`) is functional.
* Deployment scripts/Docker commands for running on Ubuntu are prepared.
* API documentation (e.g., Swagger/OpenAPI specification) is available.
* Successful end-to-end testing with at least one primary client system.

## 11. Development Approach & Coding Guidelines

This section outlines the core principles and guidelines to be followed during the development of the TMS REST API, particularly when interacting with a coding agent or development team.

### 11.1. Development Methodology

* **Iterative and Incremental Development:**
  * The system shall be built one small, manageable component at a time.
  * Focus on completing and thoroughly testing a single component before initiating work on subsequent components. This ensures a solid foundation and allows for early feedback and adjustments.
* **Version Control & Continuous Integration:**
  * All code changes must be managed using Git, with frequent, incremental commits that represent logical units of work.
  * Automated tests (unit, integration) are mandatory and must be executed automatically upon each significant change or commit (e.g., via Git hooks or a CI pipeline if applicable). Development should not proceed if tests are failing.
* **Emphasis on Testing:**
  * Each component or functional unit must be accompanied by comprehensive automated tests.
  * No component is considered "done" until it is thoroughly tested and all tests pass.

### 11.2. Coding & Implementation Principles

* **Clarity over Premature Assumption:**
  * In instances of ambiguity or uncertainty regarding implementation details, requirements, or expected behavior, clarification **must** be sought before proceeding. Assumptions should not be made.
* **Code Cleanliness and Minimality:**
  * Strive for clean, readable, and maintainable code.
  * Adhere to the principle of minimalism:
    * **File Structure:** Maintain the minimum number of necessary files. Avoid creating superfluous files or overly complex directory structures.
    * **File Content:** Keep the content of each file concise and focused on its specific responsibility. Avoid boilerplate or unused code.
* **Testing Principles:**
  * **Realistic Testing:** Never mock components unless absolutely necessary. Prefer real implementations for more realistic and reliable testing.
  * **Test Database Isolation:** When testing database interactions, use a dedicated test database to ensure tests are reliable and don't affect development data.
  * **Test Coverage:** Aim for comprehensive test coverage, including unit tests, integration tests, and end-to-end tests.
* **Debugging Practices:**
  * When investigating bugs or unexpected behavior, utilize descriptive debugging statements.
  * All temporary debugging print statements **must** be clearly demarcated as follows to ensure they are easily identifiable and removable:
    ```
    == DEBUGGING OUTPUT START ==
    [Brief description of what is being debugged, e.g., "User ID in create_quiz function"]
    [Variable_name: value]
    [Other relevant debugging information]
    == DEBUGGING OUTPUT END ==
    ```
  * These debugging blocks can then be easily removed before finalizing a component or committing stable code.

### 11.3. Agent Interaction

* **Adherence to Guidelines:** The coding agent is expected to strictly adhere to all principles outlined in this section and the broader PRD.
* **Proactive Questioning:** The agent should proactively ask for clarification when requirements are unclear or if multiple implementation paths exist with different trade-offs.