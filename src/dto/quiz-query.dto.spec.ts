/**
 * Quiz Query DTO Tests
 *
 * These tests verify the validation rules for the QuizQueryDto class.
 * They test all validation scenarios including valid inputs, invalid inputs,
 * boundary conditions, and edge cases.
 */

import { validate, ValidationError } from 'class-validator';
import { plainToClass } from 'class-transformer';
import { QuizQueryDto } from './quiz-query.dto';
import { WeekType } from '../entities/material.entity';

interface TestQuizQueryData {
  grade: number;
  subject: string;
  course: string;
  classLevel: string;
  color: string;
  year: number;
  term: number;
  week: number;
  weekType: WeekType;
  teachingProgram?: string;
  lessonName?: string;
}

describe('QuizQueryDto', () => {
  /**
   * Helper function to validate a DTO and return validation errors
   */
  async function validateDto(dto: object): Promise<string[]> {
    const validationErrors: ValidationError[] = await validate(dto);
    return validationErrors.flatMap((error: ValidationError) =>
      error.constraints ? Object.values(error.constraints) : [],
    );
  }

  /**
   * Helper function to create a valid DTO with default values
   */
  function createValidDto(): TestQuizQueryData {
    return {
      grade: 12,
      subject: 'Math',
      course: '3U',
      classLevel: 'A1',
      color: 'R',
      year: 2025,
      term: 2,
      week: 4,
      weekType: WeekType.NORMAL,
      teachingProgram: 'St George Girls',
      lessonName: 'Formulae',
    };
  }

  describe('Valid inputs', () => {
    it('should pass validation with all required fields', async () => {
      const dto = plainToClass(QuizQueryDto, {
        grade: 12,
        subject: 'Math',
        course: '3U',
        classLevel: 'A1',
        color: 'R',
        year: 2025,
        term: 2,
        week: 4,
        weekType: WeekType.NORMAL,
      });

      const errors = await validateDto(dto);
      expect(errors).toHaveLength(0);
    });

    it('should pass validation with optional fields included', async () => {
      const dto = plainToClass(QuizQueryDto, createValidDto());

      const errors = await validateDto(dto);
      expect(errors).toHaveLength(0);
    });

    it('should pass validation with optional fields omitted', async () => {
      const validData = createValidDto();
      delete validData.teachingProgram;
      delete validData.lessonName;

      const dto = plainToClass(QuizQueryDto, validData);
      const errors = await validateDto(dto);
      expect(errors).toHaveLength(0);
    });

    it('should handle string numbers correctly with transformation', async () => {
      const dto = plainToClass(QuizQueryDto, {
        grade: '12',
        subject: 'Math',
        course: '3U',
        classLevel: 'A1',
        color: 'R',
        year: '2025',
        term: '2',
        week: '4',
        weekType: WeekType.NORMAL,
      });

      const errors = await validateDto(dto);
      expect(errors).toHaveLength(0);
      expect(dto.grade).toBe(12);
      expect(dto.year).toBe(2025);
      expect(dto.term).toBe(2);
      expect(dto.week).toBe(4);
    });

    it('should trim whitespace from string fields', async () => {
      const dto = plainToClass(QuizQueryDto, {
        grade: 12,
        subject: '  Math  ',
        course: '  3U  ',
        classLevel: '  A1  ',
        color: '  R  ',
        year: 2025,
        term: 2,
        week: 4,
        weekType: WeekType.NORMAL,
        teachingProgram: '  St George Girls  ',
        lessonName: '  Formulae  ',
      });

      const errors = await validateDto(dto);
      expect(errors).toHaveLength(0);
      expect(dto.subject).toBe('Math');
      expect(dto.course).toBe('3U');
      expect(dto.classLevel).toBe('A1');
      expect(dto.color).toBe('R');
      expect(dto.teachingProgram).toBe('St George Girls');
      expect(dto.lessonName).toBe('Formulae');
    });
  });

  describe('Grade validation', () => {
    it('should reject non-integer grade', async () => {
      const validData = createValidDto();
      (validData as unknown as Record<string, unknown>).grade = 'not-a-number';

      const dto = plainToClass(QuizQueryDto, validData);
      const errors = await validateDto(dto);
      expect(errors).toContain('Grade must be an integer');
    });

    it('should reject grade below minimum (1)', async () => {
      const validData = createValidDto();
      validData.grade = 0;

      const dto = plainToClass(QuizQueryDto, validData);
      const errors = await validateDto(dto);
      expect(errors).toContain('Grade must be at least 1');
    });

    it('should reject grade above maximum (12)', async () => {
      const validData = createValidDto();
      validData.grade = 13;

      const dto = plainToClass(QuizQueryDto, validData);
      const errors = await validateDto(dto);
      expect(errors).toContain('Grade must be at most 12');
    });

    it('should accept boundary values for grade', async () => {
      // Test minimum boundary
      const minDto = plainToClass(QuizQueryDto, {
        ...createValidDto(),
        grade: 1,
      });
      const minErrors = await validateDto(minDto);
      expect(minErrors.filter((e) => e.includes('Grade'))).toHaveLength(0);

      // Test maximum boundary
      const maxDto = plainToClass(QuizQueryDto, {
        ...createValidDto(),
        grade: 12,
      });
      const maxErrors = await validateDto(maxDto);
      expect(maxErrors.filter((e) => e.includes('Grade'))).toHaveLength(0);
    });
  });

  describe('Subject validation', () => {
    it('should reject empty subject', async () => {
      const validData = createValidDto();
      validData.subject = '';

      const dto = plainToClass(QuizQueryDto, validData);
      const errors = await validateDto(dto);
      expect(errors).toContain('Subject must be between 1 and 100 characters');
    });

    it('should reject subject longer than 100 characters', async () => {
      const validData = createValidDto();
      validData.subject = 'a'.repeat(101);

      const dto = plainToClass(QuizQueryDto, validData);
      const errors = await validateDto(dto);
      expect(errors).toContain('Subject must be between 1 and 100 characters');
    });

    it('should accept subject at boundary lengths', async () => {
      // Test minimum boundary (1 character)
      const minDto = plainToClass(QuizQueryDto, {
        ...createValidDto(),
        subject: 'M',
      });
      const minErrors = await validateDto(minDto);
      expect(minErrors.filter((e) => e.includes('Subject'))).toHaveLength(0);

      // Test maximum boundary (100 characters)
      const maxDto = plainToClass(QuizQueryDto, {
        ...createValidDto(),
        subject: 'a'.repeat(100),
      });
      const maxErrors = await validateDto(maxDto);
      expect(maxErrors.filter((e) => e.includes('Subject'))).toHaveLength(0);
    });
  });

  describe('Course validation', () => {
    it('should accept empty course', async () => {
      const validData = createValidDto();
      validData.course = '';

      const dto = plainToClass(QuizQueryDto, validData);
      const errors = await validateDto(dto);
      expect(errors.filter((e) => e.includes('Course'))).toHaveLength(0);
    });

    it('should reject course longer than 100 characters', async () => {
      const validData = createValidDto();
      validData.course = 'a'.repeat(101);

      const dto = plainToClass(QuizQueryDto, validData);
      const errors = await validateDto(dto);
      expect(errors).toContain('Course must be at most 100 characters');
    });
  });

  describe('Year validation', () => {
    it('should reject year below 2000', async () => {
      const validData = createValidDto();
      validData.year = 1999;

      const dto = plainToClass(QuizQueryDto, validData);
      const errors = await validateDto(dto);
      expect(errors).toContain('Year must be at least 2000');
    });

    it('should reject year above 2100', async () => {
      const validData = createValidDto();
      validData.year = 2101;

      const dto = plainToClass(QuizQueryDto, validData);
      const errors = await validateDto(dto);
      expect(errors).toContain('Year must be at most 2100');
    });
  });

  describe('Term validation', () => {
    it('should reject term below 1', async () => {
      const validData = createValidDto();
      validData.term = 0;

      const dto = plainToClass(QuizQueryDto, validData);
      const errors = await validateDto(dto);
      expect(errors).toContain('Term must be at least 1');
    });

    it('should reject term above 4', async () => {
      const validData = createValidDto();
      validData.term = 5;

      const dto = plainToClass(QuizQueryDto, validData);
      const errors = await validateDto(dto);
      expect(errors).toContain('Term must be at most 4');
    });
  });

  describe('Week validation', () => {
    it('should reject week below 1', async () => {
      const validData = createValidDto();
      validData.week = 0;

      const dto = plainToClass(QuizQueryDto, validData);
      const errors = await validateDto(dto);
      expect(errors).toContain('Week must be at least 1');
    });

    it('should reject week above 52', async () => {
      const validData = createValidDto();
      validData.week = 53;

      const dto = plainToClass(QuizQueryDto, validData);
      const errors = await validateDto(dto);
      expect(errors).toContain('Week must be at most 52');
    });
  });

  describe('WeekType validation', () => {
    it('should accept valid week types', async () => {
      const normalDto = plainToClass(QuizQueryDto, {
        ...createValidDto(),
        weekType: WeekType.NORMAL,
      });
      const normalErrors = await validateDto(normalDto);
      expect(normalErrors.filter((e) => e.includes('Week type'))).toHaveLength(
        0,
      );

      const holidayDto = plainToClass(QuizQueryDto, {
        ...createValidDto(),
        weekType: WeekType.HOLIDAY,
      });
      const holidayErrors = await validateDto(holidayDto);
      expect(holidayErrors.filter((e) => e.includes('Week type'))).toHaveLength(
        0,
      );
    });

    it('should reject invalid week type', async () => {
      const validData = createValidDto();
      (validData as unknown as Record<string, unknown>).weekType = 'invalid';

      const dto = plainToClass(QuizQueryDto, validData);
      const errors = await validateDto(dto);
      expect(errors).toContain(
        'Week type must be either "normal" or "holiday"',
      );
    });
  });

  describe('Optional fields validation', () => {
    it('should accept undefined optional fields', async () => {
      const dto = plainToClass(QuizQueryDto, {
        grade: 12,
        subject: 'Math',
        course: '3U',
        classLevel: 'A1',
        color: 'R',
        year: 2025,
        term: 2,
        week: 4,
        weekType: WeekType.NORMAL,
        // teachingProgram and lessonName omitted
      });

      const errors = await validateDto(dto);
      expect(errors).toHaveLength(0);
    });

    it('should reject teachingProgram longer than 255 characters', async () => {
      const validData = createValidDto();
      validData.teachingProgram = 'a'.repeat(256);

      const dto = plainToClass(QuizQueryDto, validData);
      const errors = await validateDto(dto);
      expect(errors).toContain(
        'Teaching program must be at most 255 characters',
      );
    });

    it('should reject lessonName longer than 255 characters', async () => {
      const validData = createValidDto();
      validData.lessonName = 'a'.repeat(256);

      const dto = plainToClass(QuizQueryDto, validData);
      const errors = await validateDto(dto);
      expect(errors).toContain('Lesson name must be at most 255 characters');
    });
  });
});
