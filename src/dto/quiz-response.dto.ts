/**
 * Quiz Response DTOs
 *
 * These DTOs define the structure of API responses for quiz endpoints.
 * Based on the PRD specification and design document examples.
 */

import {
  IsString,
  IsNumber,
  IsArray,
  IsObject,
  IsOptional,
  IsDateString,
  IsNotEmpty,
  ValidateNested,
  Min,
  Max,
  Length,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * GIF URL DTO - represents a single GIF asset with its ID and URL
 */
export class GifUrlDto {
  /**
   * Question ID that corresponds to the GIF file
   */
  @ApiProperty({
    description: 'Question ID that corresponds to the GIF file',
    type: 'string',
    minLength: 1,
    maxLength: 50,
    example: '2013',
  })
  @IsString()
  @Length(1, 50)
  id!: string;

  /**
   * Pre-signed URL for accessing the GIF file
   */
  @ApiProperty({
    description: 'Pre-signed URL for accessing the GIF file',
    type: 'string',
    minLength: 1,
    maxLength: 1000,
    example:
      'https://minio.example.com/bucket/gifs/uuid.gif?X-Amz-Expires=3600',
  })
  @IsString()
  @Length(1, 1000)
  url!: string;
}

/**
 * Internal Metadata DTO - represents the structure of QzF2f.json content
 */
export class InternalMetadataDto {
  /**
   * Unique identifier for the question
   */
  @ApiProperty({
    description: 'Unique identifier for the question',
    type: 'string',
    minLength: 1,
    maxLength: 50,
    example: '2013',
  })
  @IsString()
  @Length(1, 50)
  questionId!: string;

  /**
   * Question number (can include sub-parts like "2(a)", "3(d)(i)")
   */
  @ApiProperty({
    description:
      'Question number (can include sub-parts like "2(a)", "3(d)(i)")',
    type: 'string',
    minLength: 1,
    maxLength: 50,
    example: '2(a)',
  })
  @IsString()
  @Length(1, 50)
  questionNumber!: string;

  /**
   * SMIL file reference (if applicable)
   */
  @ApiPropertyOptional({
    description: 'SMIL file reference (if applicable)',
    type: 'string',
    minLength: 1,
    maxLength: 255,
    example: 'question_2013.smil',
  })
  @IsOptional()
  @IsString()
  @Length(1, 255)
  smilFile?: string;

  /**
   * Marks/scoring information as JSON string
   */
  @ApiPropertyOptional({
    description: 'Marks/scoring information as JSON string',
    type: 'string',
    minLength: 1,
    maxLength: 5000,
    example: '{"totalMarks": 5, "partMarks": [2, 3]}',
  })
  @IsOptional()
  @IsString()
  @Length(1, 5000)
  marksJson?: string;
}

/**
 * Lesson Metadata DTO - represents the structure of LessonMetadata.json content
 */
export class LessonMetadataDto {
  /**
   * Grade level (1-12)
   */
  @IsNumber()
  @Min(1)
  @Max(12)
  grade!: number;

  /**
   * Subject name
   */
  @IsString()
  @Length(1, 100)
  subject!: string;

  /**
   * Course code (can be empty string)
   */
  @IsString()
  @Length(0, 100)
  course!: string;

  /**
   * Class level
   */
  @IsString()
  @Length(1, 50)
  classLevel!: string;

  /**
   * Color code
   */
  @IsString()
  @Length(1, 10)
  color!: string;

  /**
   * Topic/lesson name
   */
  @IsString()
  @Length(1, 255)
  topic!: string;
}

/**
 * Retrieved Metadata DTO - represents the combined metadata returned in responses
 */
export class RetrievedMetadataDto {
  /**
   * Subject name
   */
  @IsString()
  @Length(1, 100)
  subject!: string;

  /**
   * Grade level
   */
  @IsNumber()
  @Min(1)
  @Max(12)
  grade!: number;

  /**
   * Class level
   */
  @IsString()
  @Length(1, 50)
  classLevel!: string;

  /**
   * Lesson name
   */
  @IsString()
  @Length(1, 255)
  lessonName!: string;

  /**
   * Color code
   */
  @IsString()
  @Length(1, 10)
  color!: string;

  /**
   * Year
   */
  @IsNumber()
  @Min(2000)
  @Max(2100)
  year!: number;

  /**
   * Term
   */
  @IsNumber()
  @Min(1)
  @Max(4)
  term!: number;

  /**
   * Week
   */
  @IsNumber()
  @Min(1)
  @Max(52)
  week!: number;

  /**
   * Week type
   */
  @IsString()
  @Length(1, 50)
  weekType!: string;

  /**
   * Course code
   */
  @IsString()
  @Length(0, 100)
  course!: string;

  /**
   * Teaching program
   */
  @IsOptional()
  @IsString()
  @Length(0, 255)
  teachingProgram?: string;

  /**
   * Internal metadata (QzF2f.json content)
   */
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => InternalMetadataDto)
  internalMetadata!: InternalMetadataDto[];

  /**
   * Original filename of the uploaded ZIP
   */
  @IsString()
  @Length(1, 500)
  originalFilename!: string;

  /**
   * Upload timestamp
   */
  @IsDateString()
  uploadTimestamp!: string;
}

/**
 * Quiz Response DTO - main response structure for GET requests
 */
export class QuizResponseDto {
  /**
   * Unique ID of the quiz
   */
  @ApiProperty({
    description: 'Unique ID of the quiz',
    type: 'number',
    minimum: 1,
    example: 123,
  })
  @IsNumber()
  id!: number;

  /**
   * Retrieved metadata combining file content and query parameters
   */
  @ApiProperty({
    description:
      'Retrieved metadata combining file content and query parameters',
    type: () => RetrievedMetadataDto,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => RetrievedMetadataDto)
  retrievedMetadata!: RetrievedMetadataDto;

  /**
   * Array of GIF URLs with their corresponding question IDs
   */
  @ApiProperty({
    description: 'Array of GIF URLs with their corresponding question IDs',
    type: [GifUrlDto],
    isArray: true,
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => GifUrlDto)
  gifUrls!: GifUrlDto[];
}

/**
 * Uploaded GIF DTO - represents information about an uploaded GIF file
 * Based on Design Doc response format: {"questionId": "2013", "objectName": "gifs/uuid.gif"}
 */
export class UploadedGifDto {
  /**
   * Question ID for the uploaded GIF file
   */
  @IsString()
  @Length(1, 100)
  questionId!: string;

  /**
   * Object name/path in MinIO storage
   */
  @IsString()
  @Length(1, 500)
  objectName!: string;
}

/**
 * Extracted Metadata DTO - represents metadata extracted from LessonMetadata.json
 */
export class ExtractedMetadataDto {
  /**
   * Subject
   */
  @IsString()
  @Length(1, 100)
  subject!: string;

  /**
   * Grade level
   */
  @IsNumber()
  @Min(1)
  @Max(12)
  grade!: number;

  /**
   * Class level
   */
  @IsString()
  @Length(1, 50)
  classLevel!: string;

  /**
   * Color
   */
  @IsString()
  @Length(1, 10)
  color!: string;

  /**
   * Course code
   */
  @IsString()
  @Length(0, 100)
  course!: string;

  /**
   * Topic
   */
  @IsString()
  @Length(1, 255)
  topic!: string;
}

/**
 * Quiz Upload Data DTO - data structure for POST response data field
 * Based on Design Doc specification
 */
export class QuizUploadDataDto {
  /**
   * Quiz ID
   */
  @IsNumber()
  @Min(1)
  quizId!: number;

  /**
   * Path to the stored metadata
   */
  @IsString()
  @Length(1, 1000)
  metadataPath!: string;

  /**
   * Array of uploaded GIF information
   */
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UploadedGifDto)
  uploadedGifs!: UploadedGifDto[];

  /**
   * Total count of uploaded GIF files
   */
  @IsNumber()
  @Min(0)
  @Max(100)
  gifCount!: number;

  /**
   * Extracted metadata from LessonMetadata.json
   */
  @IsObject()
  @ValidateNested()
  @Type(() => ExtractedMetadataDto)
  extractedMetadata!: ExtractedMetadataDto;

  /**
   * Upload timestamp
   */
  @IsDateString()
  uploadTimestamp!: string;

  /**
   * Original filename of the uploaded ZIP
   */
  @IsString()
  @Length(1, 500)
  originalFilename!: string;
}

/**
 * Quiz Upload Response DTO - response structure for POST requests
 * Based on PRD specification with message and data wrapper
 */
export class QuizUploadResponseDto {
  /**
   * Success message
   */
  @ApiProperty({
    description: 'Success message',
    type: 'string',
    minLength: 1,
    maxLength: 500,
    example: 'Quiz uploaded and processed successfully',
  })
  @IsString()
  @Length(1, 500)
  message!: string;

  /**
   * Upload data containing metadata path, uploaded GIFs, and count
   */
  @ApiProperty({
    description:
      'Upload data containing metadata path, uploaded GIFs, and count',
    type: () => QuizUploadDataDto,
  })
  @ValidateNested()
  @Type(() => QuizUploadDataDto)
  @IsNotEmpty({ message: 'Data field is required' })
  data!: QuizUploadDataDto;
}
