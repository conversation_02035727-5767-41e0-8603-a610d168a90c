/**
 * Quiz Query DTO
 *
 * This DTO defines the validation rules for query parameters used in the
 * GET /quiz/f2f/paperless-marking-worked-solutions endpoint.
 *
 * Based on PRD requirements:
 * - grade (integer, Required, e.g., 12)
 * - subject (string, Required, e.g., "Math")
 * - course (string, Required, e.g., "3U")
 * - classLevel (string, Required, e.g., "A1")
 * - color (string, Required, e.g., "R")
 * - year (integer, Required, e.g., 2025)
 * - term (integer, Required, e.g., 2)
 * - week (integer, Required, e.g., 4)
 * - weekType (string, Required, e.g., "normal" or "holiday")
 * - teachingProgram (string, Optional, e.g., "St George Girls")
 * - lessonName (string, Optional, e.g., "Formulae")
 */

import {
  IsInt,
  IsString,
  IsEnum,
  IsOptional,
  Min,
  Max,
  Length,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { WeekType } from '../entities/material.entity';

export class QuizQueryDto {
  /**
   * Grade level (1-12)
   */
  @ApiProperty({
    description: 'Grade level (1-12)',
    type: 'integer',
    minimum: 1,
    maximum: 12,
    example: 12,
  })
  @Type(() => Number)
  @IsInt({ message: 'Grade must be an integer' })
  @Min(1, { message: 'Grade must be at least 1' })
  @Max(12, { message: 'Grade must be at most 12' })
  grade!: number;

  /**
   * Subject name (e.g., "Math", "English", "Science")
   */
  @ApiProperty({
    description: 'Subject name (e.g., Math, Phys, Chem, Engl)',
    type: 'string',
    minLength: 1,
    maxLength: 100,
    example: 'Math',
  })
  @IsString({ message: 'Subject must be a string' })
  @Length(1, 100, { message: 'Subject must be between 1 and 100 characters' })
  @Transform(({ value }: { value: unknown }) =>
    typeof value === 'string' ? value.trim() : value,
  )
  subject!: string;

  /**
   * Course code (e.g., "3U", "4U")
   */
  @ApiProperty({
    description:
      'Course code (e.g., 3U). For Y9 and Y10 where there is no course, input as empty string',
    type: 'string',
    maxLength: 100,
    example: '3U',
  })
  @IsString({ message: 'Course must be a string' })
  @Length(0, 100, { message: 'Course must be at most 100 characters' })
  @Transform(({ value }: { value: unknown }) =>
    typeof value === 'string' ? value.trim() : value || '',
  )
  course!: string;

  /**
   * Class level (e.g., "A1", "B2")
   */
  @ApiProperty({
    description: 'Class level identifier',
    type: 'string',
    minLength: 1,
    maxLength: 50,
    example: 'A1',
  })
  @IsString({ message: 'Class level must be a string' })
  @Length(1, 50, { message: 'Class level must be between 1 and 50 characters' })
  @Transform(({ value }: { value: unknown }) =>
    typeof value === 'string' ? value.trim() : value,
  )
  classLevel!: string;

  /**
   * Color code (e.g., "R", "G", "B")
   */
  @ApiProperty({
    description:
      'Quiz color code (R for red quiz). If there is only one quiz version, it will be R',
    type: 'string',
    minLength: 1,
    maxLength: 10,
    example: 'R',
  })
  @IsString({ message: 'Color must be a string' })
  @Length(1, 10, { message: 'Color must be between 1 and 10 characters' })
  @Transform(({ value }: { value: unknown }) =>
    typeof value === 'string' ? value.trim() : value,
  )
  color!: string;

  /**
   * Year (2000-2100)
   */
  @ApiProperty({
    description: 'Academic year',
    type: 'integer',
    minimum: 2000,
    maximum: 2100,
    example: 2025,
  })
  @Type(() => Number)
  @IsInt({ message: 'Year must be an integer' })
  @Min(2000, { message: 'Year must be at least 2000' })
  @Max(2100, { message: 'Year must be at most 2100' })
  year!: number;

  /**
   * Term (1-4)
   */
  @ApiProperty({
    description: 'Academic term (1-4)',
    type: 'integer',
    minimum: 1,
    maximum: 4,
    example: 2,
  })
  @Type(() => Number)
  @IsInt({ message: 'Term must be an integer' })
  @Min(1, { message: 'Term must be at least 1' })
  @Max(4, { message: 'Term must be at most 4' })
  term!: number;

  /**
   * Week (1-52)
   */
  @ApiProperty({
    description: 'Week number (1-52)',
    type: 'integer',
    minimum: 1,
    maximum: 52,
    example: 4,
  })
  @Type(() => Number)
  @IsInt({ message: 'Week must be an integer' })
  @Min(1, { message: 'Week must be at least 1' })
  @Max(52, { message: 'Week must be at most 52' })
  week!: number;

  /**
   * Week type (normal or holiday)
   */
  @ApiProperty({
    description: 'Week type',
    enum: WeekType,
    example: 'normal',
  })
  @IsEnum(WeekType, {
    message: 'Week type must be either "normal" or "holiday"',
  })
  weekType!: WeekType;

  /**
   * Teaching program (optional)
   */
  @ApiPropertyOptional({
    description: 'Teaching program name (optional)',
    type: 'string',
    maxLength: 255,
    example: 'St George Girls',
  })
  @IsOptional()
  @IsString({ message: 'Teaching program must be a string' })
  @Length(0, 255, {
    message: 'Teaching program must be at most 255 characters',
  })
  @Transform(({ value }: { value: unknown }) =>
    typeof value === 'string' ? value.trim() : value || '',
  )
  teachingProgram?: string;

  /**
   * Lesson name (optional)
   */
  @ApiPropertyOptional({
    description: 'Lesson name/topic for reference and logging (optional)',
    type: 'string',
    maxLength: 255,
    example: 'Formulae',
  })
  @IsOptional()
  @IsString({ message: 'Lesson name must be a string' })
  @Length(0, 255, { message: 'Lesson name must be at most 255 characters' })
  @Transform(({ value }: { value: unknown }) =>
    typeof value === 'string' ? value.trim() : value || '',
  )
  lessonName?: string;
}
