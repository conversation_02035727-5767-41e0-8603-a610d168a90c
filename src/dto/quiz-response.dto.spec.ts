/**
 * Quiz Response DTO Tests
 *
 * These tests verify the structure and validation of response DTOs.
 * They ensure that the response objects match the expected API specification.
 */

import 'reflect-metadata';
import { validate, ValidationError } from 'class-validator';
import { plainToClass } from 'class-transformer';
import {
  GifUrlDto,
  InternalMetadataDto,
  LessonMetadataDto,
  RetrievedMetadataDto,
  QuizResponseDto,
  QuizUploadResponseDto,
  UploadedGifDto,
} from './quiz-response.dto';

interface TestGifUrlData {
  id: string;
  url: string;
}

interface TestInternalMetadataData {
  questionId: string;
  questionNumber: string;
  smilFile?: string;
  marksJson?: string;
}

interface TestRetrievedMetadataData {
  subject: string;
  grade: number;
  classLevel: string;
  lessonName: string;
  color: string;
  year: number;
  term: number;
  week: number;
  weekType: string;
  course: string;
  teachingProgram?: string;
  internalMetadata: TestInternalMetadataData[];
  originalFilename: string;
  uploadTimestamp: string;
}

interface TestQuizResponseData {
  id: number;
  retrievedMetadata: TestRetrievedMetadataData;
  gifUrls: TestGifUrlData[];
}

interface TestUploadedGifData {
  questionId: string;
  objectName: string;
}

interface TestExtractedMetadataData {
  subject: string;
  grade: number;
  classLevel: string;
  color: string;
  course: string;
  topic: string;
}

interface TestQuizUploadDataData {
  quizId: number;
  metadataPath: string;
  uploadedGifs: TestUploadedGifData[];
  gifCount: number;
  extractedMetadata: TestExtractedMetadataData;
  uploadTimestamp: string;
  originalFilename: string;
}

interface TestQuizUploadResponseData {
  message: string;
  data: TestQuizUploadDataData;
}

describe('Quiz Response DTOs', () => {
  /**
   * Helper function to validate a DTO and return validation errors
   */
  async function validateDto(dto: object): Promise<string[]> {
    const validationErrors: ValidationError[] = await validate(dto);
    return validationErrors.flatMap((error: ValidationError) =>
      error.constraints ? Object.values(error.constraints) : [],
    );
  }

  describe('GifUrlDto', () => {
    it('should validate a valid GIF URL DTO', async () => {
      const dto = plainToClass(GifUrlDto, {
        id: '1137',
        url: 'https://tms-minio:9000/questions-work-solutions/1137.gif?X-Amz-Algorithm=AWS4-HMAC-SHA256',
      });

      const errors = await validateDto(dto);
      expect(errors).toHaveLength(0);
      expect(dto.id).toBe('1137');
      expect(dto.url).toContain('1137.gif');
    });

    it('should reject missing id', async () => {
      const dto = plainToClass(GifUrlDto, {
        url: 'https://example.com/file.gif',
      });

      const errors = await validateDto(dto);
      expect(errors.some((e) => e.includes('id'))).toBe(true);
    });

    it('should reject missing url', async () => {
      const dto = plainToClass(GifUrlDto, {
        id: '1137',
      });

      const errors = await validateDto(dto);
      expect(errors.some((e) => e.includes('url'))).toBe(true);
    });
  });

  describe('InternalMetadataDto', () => {
    it('should validate a valid internal metadata DTO', async () => {
      const dto = plainToClass(InternalMetadataDto, {
        questionId: '1137',
        questionNumber: '2(c)',
        smilFile: 'Q1137.smil',
        marksJson: '[{"index": "0.0", "mark": 2}, {"index": "0.1", "mark": 3}]',
      });

      const errors = await validateDto(dto);
      expect(errors).toHaveLength(0);
      expect(dto.questionId).toBe('1137');
      expect(dto.questionNumber).toBe('2(c)');
    });

    it('should validate with optional fields omitted', async () => {
      const dto = plainToClass(InternalMetadataDto, {
        questionId: '1137',
        questionNumber: '2(c)',
      });

      const errors = await validateDto(dto);
      expect(errors).toHaveLength(0);
    });

    it('should reject missing required fields', async () => {
      const dto = plainToClass(InternalMetadataDto, {
        smilFile: 'question1.smil',
      });

      const errors = await validateDto(dto);
      expect(
        errors.some(
          (e) => e.includes('questionId') || e.includes('questionNumber'),
        ),
      ).toBe(true);
    });
  });

  describe('LessonMetadataDto', () => {
    it('should validate a valid lesson metadata DTO', async () => {
      const dto = plainToClass(LessonMetadataDto, {
        grade: 9,
        subject: 'Math',
        course: '',
        classLevel: 'A1',
        color: 'R',
        topic: 'Trigonometry',
      });

      const errors = await validateDto(dto);
      expect(errors).toHaveLength(0);
      expect(dto.grade).toBe(9);
      expect(dto.subject).toBe('Math');
      expect(dto.topic).toBe('Trigonometry');
    });

    it('should reject missing required fields', async () => {
      const dto = plainToClass(LessonMetadataDto, {
        grade: 9,
        subject: 'Math',
        // Missing other required fields
      });

      const errors = await validateDto(dto);
      expect(errors.length).toBeGreaterThan(0);
    });
  });

  describe('RetrievedMetadataDto', () => {
    function createValidRetrievedMetadata(): TestRetrievedMetadataData {
      return {
        subject: 'Math',
        grade: 9,
        classLevel: 'A1',
        lessonName: 'Formulae',
        color: 'R',
        year: 2025,
        term: 1,
        week: 1,
        weekType: 'normal',
        course: '',
        teachingProgram: 'St George Girls',
        internalMetadata: [
          {
            questionId: '1137',
            questionNumber: '2(c)',
            smilFile: 'Q1137.smil',
            marksJson:
              '[{"index": "0.0", "mark": 2}, {"index": "0.1", "mark": 3}]',
          },
        ],
        originalFilename:
          'Dr Du_Math__V6_Y09_Formulae_A1.R_for_Y09__F2F QZ.zip',
        uploadTimestamp: '2025-05-02T01:38:23.926Z',
      };
    }

    it('should validate a valid retrieved metadata DTO', async () => {
      const dto = plainToClass(
        RetrievedMetadataDto,
        createValidRetrievedMetadata(),
      );

      const errors = await validateDto(dto);
      expect(errors).toHaveLength(0);
      expect(dto.subject).toBe('Math');
      expect(dto.grade).toBe(9);
      expect(dto.internalMetadata).toHaveLength(1);
      expect(dto.internalMetadata[0].questionId).toBe('1137');
    });

    it('should validate with optional teachingProgram omitted', async () => {
      const validData: Partial<TestRetrievedMetadataData> =
        createValidRetrievedMetadata();
      delete validData.teachingProgram;

      const dto = plainToClass(RetrievedMetadataDto, validData);
      const errors = await validateDto(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate internalMetadata array transformation', () => {
      const dto = plainToClass(
        RetrievedMetadataDto,
        createValidRetrievedMetadata(),
      );

      expect(Array.isArray(dto.internalMetadata)).toBe(true);
      expect(dto.internalMetadata[0]).toBeInstanceOf(InternalMetadataDto);
    });
  });

  describe('QuizResponseDto', () => {
    function createValidQuizResponse(): TestQuizResponseData {
      return {
        id: 1,
        retrievedMetadata: {
          subject: 'Math',
          grade: 9,
          classLevel: 'A1',
          lessonName: 'Formulae',
          color: 'R',
          year: 2025,
          term: 1,
          week: 1,
          weekType: 'normal',
          course: '',
          teachingProgram: 'St George Girls',
          internalMetadata: [
            {
              questionId: '1137',
              questionNumber: '2(c)',
              smilFile: 'Q1137.smil',
              marksJson:
                '[{"index": "0.0", "mark": 2}, {"index": "0.1", "mark": 3}]',
            },
          ],
          originalFilename:
            'Dr Du_Math__V6_Y09_Formulae_A1.R_for_Y09__F2F QZ.zip',
          uploadTimestamp: '2025-05-02T01:38:23.926Z',
        },
        gifUrls: [
          {
            id: '1137',
            url: 'https://tms-minio:9000/questions-work-solutions/1137.gif?X-Amz-Algorithm=AWS4-HMAC-SHA256',
          },
        ],
      };
    }

    it('should validate a valid quiz response DTO', async () => {
      const dto = plainToClass(QuizResponseDto, createValidQuizResponse());

      const errors = await validateDto(dto);
      expect(errors).toHaveLength(0);
      expect(dto.id).toBe(1);
      expect(dto.retrievedMetadata).toBeInstanceOf(RetrievedMetadataDto);
      expect(dto.gifUrls).toHaveLength(1);
      expect(dto.gifUrls[0]).toBeInstanceOf(GifUrlDto);
    });

    it('should reject missing required fields', async () => {
      const dto = plainToClass(QuizResponseDto, {
        id: 1,
        // Missing retrievedMetadata and gifUrls
      });

      const errors = await validateDto(dto);
      expect(errors.length).toBeGreaterThan(0);
    });
  });

  describe('UploadedGifDto', () => {
    it('should validate a valid uploaded GIF DTO', async () => {
      const dto = plainToClass(UploadedGifDto, {
        questionId: '1137',
        objectName: 'gifs/f47ac10b-58cc-4372-a567-0e02b2c3d479.gif',
      });

      const errors = await validateDto(dto);
      expect(errors).toHaveLength(0);
      expect(dto.questionId).toBe('1137');
      expect(dto.objectName).toBe(
        'gifs/f47ac10b-58cc-4372-a567-0e02b2c3d479.gif',
      );
    });

    it('should reject missing required fields', async () => {
      const dto = plainToClass(UploadedGifDto, {
        questionId: '1137',
        // Missing objectName
      });

      const errors = await validateDto(dto);
      expect(errors.length).toBeGreaterThan(0);
    });
  });

  describe('QuizUploadResponseDto', () => {
    function createValidUploadResponse(): TestQuizUploadResponseData {
      return {
        message: 'Quiz worked solutions uploaded and processed successfully',
        data: {
          quizId: 42,
          metadataPath: 'metadata/Math/9/A1/2025/2/3/normal/metadata.json',
          uploadedGifs: [
            {
              questionId: '1137',
              objectName: 'gifs/f47ac10b-58cc-4372-a567-0e02b2c3d479.gif',
            },
            {
              questionId: '2013',
              objectName: 'gifs/a1b2c3d4-e5f6-7890-abcd-ef1234567890.gif',
            },
          ],
          gifCount: 2,
          extractedMetadata: {
            subject: 'Math',
            grade: 9,
            classLevel: 'A1',
            color: 'R',
            course: '',
            topic: 'Trigonometry',
          },
          uploadTimestamp: '2025-05-24T21:45:23.926Z',
          originalFilename:
            'Dr Du_Math__V2_Y09_Trigonometry_A1.R_for_Y09__F2F QZ.zip',
        },
      };
    }

    it('should validate a valid quiz upload response DTO', async () => {
      const dto = plainToClass(
        QuizUploadResponseDto,
        createValidUploadResponse(),
      );

      const errors = await validateDto(dto);
      expect(errors).toHaveLength(0);
      expect(dto.message).toBe(
        'Quiz worked solutions uploaded and processed successfully',
      );
      expect(dto.data.quizId).toBe(42);
      expect(dto.data.uploadedGifs).toHaveLength(2);
      expect(dto.data.uploadedGifs[0]).toBeInstanceOf(UploadedGifDto);
      expect(dto.data.uploadedGifs[0].questionId).toBe('1137');
      expect(dto.data.uploadedGifs[0].objectName).toBe(
        'gifs/f47ac10b-58cc-4372-a567-0e02b2c3d479.gif',
      );
      expect(dto.data.gifCount).toBe(2);
      expect(dto.data.extractedMetadata.subject).toBe('Math');
      expect(dto.data.originalFilename).toBe(
        'Dr Du_Math__V2_Y09_Trigonometry_A1.R_for_Y09__F2F QZ.zip',
      );
    });

    it('should validate with empty uploadedGifs array', async () => {
      const validData = createValidUploadResponse();
      validData.data.uploadedGifs = [];
      validData.data.gifCount = 0;

      const dto = plainToClass(QuizUploadResponseDto, validData);
      const errors = await validateDto(dto);
      expect(errors).toHaveLength(0);
      expect(dto.data.uploadedGifs).toHaveLength(0);
      expect(dto.data.gifCount).toBe(0);
    });

    it('should reject missing required fields', async () => {
      const dto = plainToClass(QuizUploadResponseDto, {
        message: 'Quiz uploaded successfully',
        // Missing data object
      });

      const errors = await validateDto(dto);
      expect(errors.length).toBeGreaterThan(0);
    });
  });
});
