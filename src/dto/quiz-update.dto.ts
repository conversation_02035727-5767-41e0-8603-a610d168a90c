/**
 * Quiz Update DTO
 *
 * This DTO defines the validation rules for updating quiz metadata via the
 * PUT /quiz/{id} endpoint. All fields are optional to support partial updates.
 *
 * Supports updating:
 * - Basic metadata (year, term, week, weekType, teachingProgram)
 * - Lesson metadata (subject, grade, course, classLevel, color, lessonName)
 * - Optional file replacement via multipart/form-data
 *
 * Note: When a new ZIP file is provided, it will replace the existing quiz assets
 * and the old files will be cleaned up from MinIO storage.
 */

import {
  IsInt,
  IsString,
  IsEnum,
  IsOptional,
  Min,
  Max,
  Length,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { WeekType } from '../entities/material.entity';

export class QuizUpdateDto {
  /**
   * Year (2000-2100) - Optional
   */
  @ApiPropertyOptional({
    description: 'Academic year (optional)',
    type: 'integer',
    minimum: 2000,
    maximum: 2100,
    example: 2025,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'Year must be an integer' })
  @Min(2000, { message: 'Year must be at least 2000' })
  @Max(2100, { message: 'Year must be at most 2100' })
  year?: number;

  /**
   * Term (1-4) - Optional
   */
  @ApiPropertyOptional({
    description: 'Academic term 1-4 (optional)',
    type: 'integer',
    minimum: 1,
    maximum: 4,
    example: 2,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'Term must be an integer' })
  @Min(1, { message: 'Term must be at least 1' })
  @Max(4, { message: 'Term must be at most 4' })
  term?: number;

  /**
   * Week (1-52) - Optional
   */
  @ApiPropertyOptional({
    description: 'Week number 1-52 (optional)',
    type: 'integer',
    minimum: 1,
    maximum: 52,
    example: 4,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'Week must be an integer' })
  @Min(1, { message: 'Week must be at least 1' })
  @Max(52, { message: 'Week must be at most 52' })
  week?: number;

  /**
   * Week type (normal or holiday) - Optional
   */
  @ApiPropertyOptional({
    description: 'Week type (optional)',
    enum: WeekType,
    example: 'normal',
  })
  @IsOptional()
  @IsEnum(WeekType, {
    message: 'Week type must be either "normal" or "holiday"',
  })
  weekType?: WeekType;

  /**
   * Teaching program - Optional
   */
  @ApiPropertyOptional({
    description: 'Teaching program name (optional)',
    type: 'string',
    maxLength: 255,
    example: 'St George Girls',
  })
  @IsOptional()
  @IsString({ message: 'Teaching program must be a string' })
  @Length(0, 255, {
    message: 'Teaching program must be at most 255 characters',
  })
  @Transform(({ value }: { value: unknown }) =>
    typeof value === 'string' ? value.trim() : value || '',
  )
  teachingProgram?: string;

  /**
   * Subject name (e.g., "Math", "English", "Science") - Optional
   */
  @ApiPropertyOptional({
    description: 'Subject name (optional)',
    type: 'string',
    maxLength: 100,
    example: 'Math',
  })
  @IsOptional()
  @IsString({ message: 'Subject must be a string' })
  @Length(1, 100, { message: 'Subject must be between 1 and 100 characters' })
  @Transform(({ value }: { value: unknown }) =>
    typeof value === 'string' ? value.trim() : value,
  )
  subject?: string;

  /**
   * Grade level (1-12) - Optional
   */
  @ApiPropertyOptional({
    description: 'Grade level 1-12 (optional)',
    type: 'integer',
    minimum: 1,
    maximum: 12,
    example: 12,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'Grade must be an integer' })
  @Min(1, { message: 'Grade must be at least 1' })
  @Max(12, { message: 'Grade must be at most 12' })
  grade?: number;

  /**
   * Course code (e.g., "3U") - Optional
   */
  @ApiPropertyOptional({
    description: 'Course code (optional)',
    type: 'string',
    maxLength: 50,
    example: '3U',
  })
  @IsOptional()
  @IsString({ message: 'Course must be a string' })
  @Length(0, 50, { message: 'Course must be at most 50 characters' })
  @Transform(({ value }: { value: unknown }) =>
    typeof value === 'string' ? value.trim() : value || '',
  )
  course?: string;

  /**
   * Class level (e.g., "A1") - Optional
   */
  @ApiPropertyOptional({
    description: 'Class level identifier (optional)',
    type: 'string',
    minLength: 1,
    maxLength: 50,
    example: 'A1',
  })
  @IsOptional()
  @IsString({ message: 'Class level must be a string' })
  @Length(1, 50, {
    message: 'Class level must be between 1 and 50 characters',
  })
  @Transform(({ value }: { value: unknown }) =>
    typeof value === 'string' ? value.trim() : value,
  )
  classLevel?: string;

  /**
   * Color code (e.g., "R" for red) - Optional
   */
  @ApiPropertyOptional({
    description: 'Quiz color code (optional)',
    type: 'string',
    minLength: 1,
    maxLength: 10,
    example: 'R',
  })
  @IsOptional()
  @IsString({ message: 'Color must be a string' })
  @Length(1, 10, { message: 'Color must be between 1 and 10 characters' })
  @Transform(({ value }: { value: unknown }) =>
    typeof value === 'string' ? value.trim().toUpperCase() : value,
  )
  color?: string;

  /**
   * Lesson name/topic - Optional
   */
  @ApiPropertyOptional({
    description: 'Lesson name/topic (optional)',
    type: 'string',
    maxLength: 255,
    example: 'Formulae',
  })
  @IsOptional()
  @IsString({ message: 'Lesson name must be a string' })
  @Length(0, 255, { message: 'Lesson name must be at most 255 characters' })
  @Transform(({ value }: { value: unknown }) =>
    typeof value === 'string' ? value.trim() : value || '',
  )
  lessonName?: string;
}
