/**
 * Quiz Update DTO Tests
 *
 * These tests verify the validation rules for the QuizUpdateDto class.
 * They test all validation scenarios for the PUT endpoint parameters.
 * All fields are optional to support partial updates.
 */

import { validate, ValidationError } from 'class-validator';
import { plainToClass } from 'class-transformer';
import { QuizUpdateDto } from './quiz-update.dto';
import { WeekType } from '../entities/material.entity';

interface TestQuizUpdateData {
  year?: number;
  term?: number;
  week?: number;
  weekType?: WeekType;
  teachingProgram?: string;
  subject?: string;
  grade?: number;
  course?: string;
  classLevel?: string;
  color?: string;
  lessonName?: string;
}

describe('QuizUpdateDto', () => {
  /**
   * Helper function to validate a DTO and return validation errors
   */
  async function validateDto(dto: object): Promise<string[]> {
    const validationErrors: ValidationError[] = await validate(dto);
    return validationErrors.flatMap((error: ValidationError) =>
      error.constraints ? Object.values(error.constraints) : [],
    );
  }

  /**
   * Helper function to create a complete valid DTO with all fields
   */
  function createCompleteValidDto(): TestQuizUpdateData {
    return {
      year: 2025,
      term: 2,
      week: 4,
      weekType: WeekType.NORMAL,
      teachingProgram: 'St George Girls',
      subject: 'Math',
      grade: 12,
      course: '3U',
      classLevel: 'A1',
      color: 'R',
      lessonName: 'Formulae',
    };
  }

  describe('Valid inputs', () => {
    it('should pass validation with empty object (no updates)', async () => {
      const dto = plainToClass(QuizUpdateDto, {});

      const errors = await validateDto(dto);
      expect(errors).toHaveLength(0);
    });

    it('should pass validation with all fields provided', async () => {
      const dto = plainToClass(QuizUpdateDto, createCompleteValidDto());

      const errors = await validateDto(dto);
      expect(errors).toHaveLength(0);
    });

    it('should pass validation with only year provided', async () => {
      const dto = plainToClass(QuizUpdateDto, { year: 2025 });

      const errors = await validateDto(dto);
      expect(errors).toHaveLength(0);
    });

    it('should pass validation with only metadata fields', async () => {
      const dto = plainToClass(QuizUpdateDto, {
        subject: 'Math',
        grade: 12,
        classLevel: 'A1',
        color: 'R',
      });

      const errors = await validateDto(dto);
      expect(errors).toHaveLength(0);
    });

    it('should handle string numbers correctly with transformation', async () => {
      const dto = plainToClass(QuizUpdateDto, {
        year: '2025',
        term: '2',
        week: '4',
        grade: '12',
      });

      const errors = await validateDto(dto);
      expect(errors).toHaveLength(0);
      expect(dto.year).toBe(2025);
      expect(dto.term).toBe(2);
      expect(dto.week).toBe(4);
      expect(dto.grade).toBe(12);
    });

    it('should trim and transform string fields correctly', async () => {
      const dto = plainToClass(QuizUpdateDto, {
        subject: '  Math  ',
        classLevel: '  A1  ',
        color: '  r  ', // Should be uppercase
        lessonName: '  Formulae  ',
        teachingProgram: '  St George Girls  ',
        course: '  3U  ',
      });

      const errors = await validateDto(dto);
      expect(errors).toHaveLength(0);
      expect(dto.subject).toBe('Math');
      expect(dto.classLevel).toBe('A1');
      expect(dto.color).toBe('R');
      expect(dto.lessonName).toBe('Formulae');
      expect(dto.teachingProgram).toBe('St George Girls');
      expect(dto.course).toBe('3U');
    });
  });

  describe('Year validation', () => {
    it('should reject year below minimum', async () => {
      const dto = plainToClass(QuizUpdateDto, { year: 1999 });

      const errors = await validateDto(dto);
      expect(errors).toContain('Year must be at least 2000');
    });

    it('should reject year above maximum', async () => {
      const dto = plainToClass(QuizUpdateDto, { year: 2101 });

      const errors = await validateDto(dto);
      expect(errors).toContain('Year must be at most 2100');
    });

    it('should reject non-integer year', async () => {
      const dto = plainToClass(QuizUpdateDto, { year: 2025.5 });

      const errors = await validateDto(dto);
      expect(errors).toContain('Year must be an integer');
    });

    it('should accept valid year boundaries', async () => {
      const dto2000 = plainToClass(QuizUpdateDto, { year: 2000 });
      const dto2100 = plainToClass(QuizUpdateDto, { year: 2100 });

      const errors2000 = await validateDto(dto2000);
      const errors2100 = await validateDto(dto2100);

      expect(errors2000).toHaveLength(0);
      expect(errors2100).toHaveLength(0);
    });
  });

  describe('Term validation', () => {
    it('should reject term below minimum', async () => {
      const dto = plainToClass(QuizUpdateDto, { term: 0 });

      const errors = await validateDto(dto);
      expect(errors).toContain('Term must be at least 1');
    });

    it('should reject term above maximum', async () => {
      const dto = plainToClass(QuizUpdateDto, { term: 5 });

      const errors = await validateDto(dto);
      expect(errors).toContain('Term must be at most 4');
    });

    it('should accept valid term boundaries', async () => {
      const dto1 = plainToClass(QuizUpdateDto, { term: 1 });
      const dto4 = plainToClass(QuizUpdateDto, { term: 4 });

      const errors1 = await validateDto(dto1);
      const errors4 = await validateDto(dto4);

      expect(errors1).toHaveLength(0);
      expect(errors4).toHaveLength(0);
    });
  });

  describe('Week validation', () => {
    it('should reject week below minimum', async () => {
      const dto = plainToClass(QuizUpdateDto, { week: 0 });

      const errors = await validateDto(dto);
      expect(errors).toContain('Week must be at least 1');
    });

    it('should reject week above maximum', async () => {
      const dto = plainToClass(QuizUpdateDto, { week: 53 });

      const errors = await validateDto(dto);
      expect(errors).toContain('Week must be at most 52');
    });

    it('should accept valid week boundaries', async () => {
      const dto1 = plainToClass(QuizUpdateDto, { week: 1 });
      const dto52 = plainToClass(QuizUpdateDto, { week: 52 });

      const errors1 = await validateDto(dto1);
      const errors52 = await validateDto(dto52);

      expect(errors1).toHaveLength(0);
      expect(errors52).toHaveLength(0);
    });
  });

  describe('Grade validation', () => {
    it('should reject grade below minimum', async () => {
      const dto = plainToClass(QuizUpdateDto, { grade: 0 });

      const errors = await validateDto(dto);
      expect(errors).toContain('Grade must be at least 1');
    });

    it('should reject grade above maximum', async () => {
      const dto = plainToClass(QuizUpdateDto, { grade: 13 });

      const errors = await validateDto(dto);
      expect(errors).toContain('Grade must be at most 12');
    });

    it('should accept valid grade boundaries', async () => {
      const dto1 = plainToClass(QuizUpdateDto, { grade: 1 });
      const dto12 = plainToClass(QuizUpdateDto, { grade: 12 });

      const errors1 = await validateDto(dto1);
      const errors12 = await validateDto(dto12);

      expect(errors1).toHaveLength(0);
      expect(errors12).toHaveLength(0);
    });
  });

  describe('WeekType validation', () => {
    it('should accept valid week types', async () => {
      const dtoNormal = plainToClass(QuizUpdateDto, {
        weekType: WeekType.NORMAL,
      });
      const dtoHoliday = plainToClass(QuizUpdateDto, {
        weekType: WeekType.HOLIDAY,
      });

      const errorsNormal = await validateDto(dtoNormal);
      const errorsHoliday = await validateDto(dtoHoliday);

      expect(errorsNormal).toHaveLength(0);
      expect(errorsHoliday).toHaveLength(0);
    });

    it('should reject invalid week type', async () => {
      const dto = plainToClass(QuizUpdateDto, {
        weekType: 'invalid' as WeekType,
      });

      const errors = await validateDto(dto);
      expect(errors).toContain(
        'Week type must be either "normal" or "holiday"',
      );
    });
  });

  describe('String field validation', () => {
    it('should reject subject that is too long', async () => {
      const longSubject = 'a'.repeat(101);
      const dto = plainToClass(QuizUpdateDto, { subject: longSubject });

      const errors = await validateDto(dto);
      expect(errors).toContain('Subject must be between 1 and 100 characters');
    });

    it('should reject empty subject', async () => {
      const dto = plainToClass(QuizUpdateDto, { subject: '' });

      const errors = await validateDto(dto);
      expect(errors).toContain('Subject must be between 1 and 100 characters');
    });

    it('should reject classLevel that is too long', async () => {
      const longClassLevel = 'a'.repeat(51);
      const dto = plainToClass(QuizUpdateDto, { classLevel: longClassLevel });

      const errors = await validateDto(dto);
      expect(errors).toContain(
        'Class level must be between 1 and 50 characters',
      );
    });

    it('should reject empty classLevel', async () => {
      const dto = plainToClass(QuizUpdateDto, { classLevel: '' });

      const errors = await validateDto(dto);
      expect(errors).toContain(
        'Class level must be between 1 and 50 characters',
      );
    });

    it('should accept empty course and teachingProgram', async () => {
      const dto = plainToClass(QuizUpdateDto, {
        course: '',
        teachingProgram: '',
        lessonName: '',
      });

      const errors = await validateDto(dto);
      expect(errors).toHaveLength(0);
    });

    it('should reject color that is too long', async () => {
      const longColor = 'a'.repeat(11);
      const dto = plainToClass(QuizUpdateDto, { color: longColor });

      const errors = await validateDto(dto);
      expect(errors).toContain('Color must be between 1 and 10 characters');
    });

    it('should reject empty color', async () => {
      const dto = plainToClass(QuizUpdateDto, { color: '' });

      const errors = await validateDto(dto);
      expect(errors).toContain('Color must be between 1 and 10 characters');
    });
  });

  describe('Partial update scenarios', () => {
    it('should allow updating only scheduling fields', async () => {
      const dto = plainToClass(QuizUpdateDto, {
        year: 2026,
        term: 3,
        week: 10,
        weekType: WeekType.HOLIDAY,
      });

      const errors = await validateDto(dto);
      expect(errors).toHaveLength(0);
    });

    it('should allow updating only lesson metadata', async () => {
      const dto = plainToClass(QuizUpdateDto, {
        subject: 'Physics',
        grade: 11,
        classLevel: 'B2',
        color: 'B',
        lessonName: 'Mechanics',
      });

      const errors = await validateDto(dto);
      expect(errors).toHaveLength(0);
    });

    it('should allow updating only teaching program', async () => {
      const dto = plainToClass(QuizUpdateDto, {
        teachingProgram: 'Sydney Boys High',
      });

      const errors = await validateDto(dto);
      expect(errors).toHaveLength(0);
    });
  });
});
