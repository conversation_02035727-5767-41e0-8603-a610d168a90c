/**
 * Zod Schema for LessonMetadata.json Validation
 *
 * This schema validates the structure and content of LessonMetadata.json files
 * extracted from quiz ZIP uploads. It replaces the manual validation logic
 * with type-safe Zod validation.
 */

import { z } from 'zod';

/**
 * Zod schema for LessonMetadata.json validation
 *
 * Validates the structure and constraints based on:
 * - Current validation logic in quiz.service.ts
 * - DTO validation rules in LessonMetadataDto
 * - Training data patterns from test files
 *
 * @example
 * ```typescript
 * const metadata = LessonMetadataSchema.parse(jsonData);
 * // metadata is now type-safe and validated
 * ```
 */
export const LessonMetadataSchema = z.object({
  /**
   * Grade level (1-12)
   * Required field, must be a number between 1 and 12 inclusive
   */
  grade: z
    .number()
    .int('Grade must be an integer')
    .min(1, 'Grade must be at least 1')
    .max(12, 'Grade must be at most 12'),

  /**
   * Subject name
   * Required field, must be a non-empty string with max length 100
   */
  subject: z
    .string()
    .min(1, 'Subject is required and cannot be empty')
    .max(100, 'Subject must be at most 100 characters'),

  /**
   * Course code (can be empty string)
   * Optional field that can be empty, max length 100
   */
  course: z.string().max(100, 'Course must be at most 100 characters'),

  /**
   * Class level
   * Required field, must be a non-empty string with max length 50
   * Examples from training data: 'A', 'A1', 'A2', 'A3', 'B'
   */
  classLevel: z
    .string()
    .min(1, 'Class level is required and cannot be empty')
    .max(50, 'Class level must be at most 50 characters'),

  /**
   * Color code
   * Required field, must be a non-empty string with max length 10
   * Examples from training data: 'R' (Red), 'Y' (Yellow)
   */
  color: z
    .string()
    .min(1, 'Color is required and cannot be empty')
    .max(10, 'Color must be at most 10 characters'),

  /**
   * Topic/lesson name
   * Required field, must be a non-empty string with max length 255
   */
  topic: z
    .string()
    .min(1, 'Topic is required and cannot be empty')
    .max(255, 'Topic must be at most 255 characters'),
});

/**
 * TypeScript type inferred from the Zod schema
 * This provides type safety when using the validated data
 */
export type LessonMetadata = z.infer<typeof LessonMetadataSchema>;
