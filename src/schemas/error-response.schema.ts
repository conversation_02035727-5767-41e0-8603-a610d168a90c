/**
 * Error Response Schemas for Swagger Documentation
 *
 * This file defines standardized error response schemas that match the Design Doc
 * error format. These schemas are used across all API endpoints for consistent
 * error documentation.
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Base Error Response Schema
 * Matches the Design Doc error format exactly
 */
export class ErrorResponseSchema {
  @ApiProperty({
    description: 'HTTP status code',
    example: 400,
    type: 'number',
  })
  statusCode: number;

  @ApiProperty({
    description: 'Error message describing what went wrong',
    example: 'Validation failed',
    type: 'string',
  })
  message: string;

  @ApiPropertyOptional({
    description: 'Additional error details (optional)',
    example: 'Invalid file type. Expected ZIP file.',
    oneOf: [{ type: 'string' }, { type: 'object' }, { type: 'array' }],
  })
  details?: string | object | unknown[];

  @ApiPropertyOptional({
    description: 'Correlation ID for request tracing (UUID format)',
    example: '123e4567-e89b-12d3-a456-************',
    type: 'string',
    format: 'uuid',
  })
  correlationId?: string;

  @ApiProperty({
    description: 'Timestamp when the error occurred',
    example: '2025-05-26T21:45:23.926Z',
    type: 'string',
    format: 'date-time',
  })
  timestamp: string;

  @ApiProperty({
    description: 'API endpoint path where the error occurred',
    example: '/quiz/f2f/paperless-marking-worked-solutions',
    type: 'string',
  })
  path: string;
}

/**
 * Validation Error Details Schema
 * Used for 400 Bad Request responses with validation errors
 */
export class ValidationErrorDetailSchema {
  @ApiProperty({
    description: 'Property name that failed validation',
    example: 'grade',
    type: 'string',
  })
  property: string;

  @ApiProperty({
    description: 'Invalid value that was provided',
    example: 'invalid',
  })
  value: unknown;

  @ApiProperty({
    description: 'Array of constraint violation messages',
    example: ['Grade must be an integer'],
    type: 'array',
    items: { type: 'string' },
  })
  constraints: string[];
}

/**
 * Validation Error Response Schema
 * 400 Bad Request with detailed validation errors
 */
export class ValidationErrorResponseSchema {
  @ApiProperty({
    description: 'HTTP status code',
    example: 400,
    type: 'number',
  })
  statusCode: 400;

  @ApiProperty({
    description: 'Validation error message',
    example: 'Validation failed',
    type: 'string',
  })
  message: string;

  @ApiProperty({
    description: 'Detailed validation error information',
    type: 'array',
    items: { $ref: '#/components/schemas/ValidationErrorDetailSchema' },
  })
  details: ValidationErrorDetailSchema[];

  @ApiPropertyOptional({
    description: 'Correlation ID for request tracing (UUID format)',
    example: '123e4567-e89b-12d3-a456-************',
    type: 'string',
    format: 'uuid',
  })
  correlationId?: string;

  @ApiProperty({
    description: 'Timestamp when the error occurred',
    example: '2025-05-26T21:45:23.926Z',
    type: 'string',
    format: 'date-time',
  })
  timestamp: string;

  @ApiProperty({
    description: 'API endpoint path where the error occurred',
    example: '/quiz/f2f/paperless-marking-worked-solutions',
    type: 'string',
  })
  path: string;
}

/**
 * Authentication Error Response Schema
 * 401 Unauthorized
 */
export class AuthenticationErrorResponseSchema {
  @ApiProperty({
    description: 'HTTP status code',
    example: 401,
    type: 'number',
  })
  statusCode: 401;

  @ApiProperty({
    description: 'Authentication error message',
    example: 'Unauthorized',
    type: 'string',
  })
  message: string;

  @ApiPropertyOptional({
    description: 'Correlation ID for request tracing (UUID format)',
    example: '123e4567-e89b-12d3-a456-************',
    type: 'string',
    format: 'uuid',
  })
  correlationId?: string;

  @ApiProperty({
    description: 'Timestamp when the error occurred',
    example: '2025-05-26T21:45:23.926Z',
    type: 'string',
    format: 'date-time',
  })
  timestamp: string;

  @ApiProperty({
    description: 'API endpoint path where the error occurred',
    example: '/protected',
    type: 'string',
  })
  path: string;
}

/**
 * Not Found Error Response Schema
 * 404 Not Found
 */
export class NotFoundErrorResponseSchema {
  @ApiProperty({
    description: 'HTTP status code',
    example: 404,
    type: 'number',
  })
  statusCode: 404;

  @ApiProperty({
    description: 'Not found error message',
    example: 'No quiz found matching the provided criteria',
    type: 'string',
  })
  message: string;

  @ApiPropertyOptional({
    description: 'Correlation ID for request tracing (UUID format)',
    example: '123e4567-e89b-12d3-a456-************',
    type: 'string',
    format: 'uuid',
  })
  correlationId?: string;

  @ApiProperty({
    description: 'Timestamp when the error occurred',
    example: '2025-05-26T21:45:23.926Z',
    type: 'string',
    format: 'date-time',
  })
  timestamp: string;

  @ApiProperty({
    description: 'API endpoint path where the error occurred',
    example: '/quiz/f2f/paperless-marking-worked-solutions',
    type: 'string',
  })
  path: string;
}

/**
 * File Too Large Error Response Schema
 * 413 Payload Too Large
 */
export class FileTooLargeErrorResponseSchema {
  @ApiProperty({
    description: 'HTTP status code',
    example: 413,
    type: 'number',
  })
  statusCode: 413;

  @ApiProperty({
    description: 'File size error message',
    example: 'File too large. Maximum size is 10MB.',
    type: 'string',
  })
  message: string;

  @ApiPropertyOptional({
    description: 'Correlation ID for request tracing (UUID format)',
    example: '123e4567-e89b-12d3-a456-************',
    type: 'string',
    format: 'uuid',
  })
  correlationId?: string;

  @ApiProperty({
    description: 'Timestamp when the error occurred',
    example: '2025-05-26T21:45:23.926Z',
    type: 'string',
    format: 'date-time',
  })
  timestamp: string;

  @ApiProperty({
    description: 'API endpoint path where the error occurred',
    example: '/quiz/f2f/paperless-marking-worked-solutions',
    type: 'string',
  })
  path: string;
}

/**
 * Internal Server Error Response Schema
 * 500 Internal Server Error
 */
export class InternalServerErrorResponseSchema {
  @ApiProperty({
    description: 'HTTP status code',
    example: 500,
    type: 'number',
  })
  statusCode: 500;

  @ApiProperty({
    description: 'Internal server error message',
    example: 'Internal server error',
    type: 'string',
  })
  message: string;

  @ApiProperty({
    description: 'Error details for debugging',
    example: 'Database connection failed',
    type: 'string',
  })
  details: string;

  @ApiPropertyOptional({
    description: 'Correlation ID for request tracing (UUID format)',
    example: '123e4567-e89b-12d3-a456-************',
    type: 'string',
    format: 'uuid',
  })
  correlationId?: string;

  @ApiProperty({
    description: 'Timestamp when the error occurred',
    example: '2025-05-26T21:45:23.926Z',
    type: 'string',
    format: 'date-time',
  })
  timestamp: string;

  @ApiProperty({
    description: 'API endpoint path where the error occurred',
    example: '/quiz/f2f/paperless-marking-worked-solutions',
    type: 'string',
  })
  path: string;
}

/**
 * Common Error Response Schemas for Swagger Documentation
 * These can be referenced in @ApiResponse decorators
 */
export const CommonErrorResponses = {
  BadRequest: {
    status: 400,
    description: 'Bad Request - Validation failed or invalid input',
    type: ValidationErrorResponseSchema,
  },
  Unauthorized: {
    status: 401,
    description: 'Unauthorized - Authentication required',
    type: AuthenticationErrorResponseSchema,
  },
  NotFound: {
    status: 404,
    description: 'Not Found - Resource not found',
    type: NotFoundErrorResponseSchema,
  },
  PayloadTooLarge: {
    status: 413,
    description: 'Payload Too Large - File size exceeds limit',
    type: FileTooLargeErrorResponseSchema,
  },
  InternalServerError: {
    status: 500,
    description: 'Internal Server Error - Unexpected server error',
    type: InternalServerErrorResponseSchema,
  },
};

/**
 * Authentication Examples for Swagger Documentation
 */
export const AuthenticationExamples = {
  BasicAuth: {
    description: 'HTTP Basic Authentication',
    value: 'Basic eW91ci11c2VybmFtZTp5b3VyLXBhc3N3b3Jk',
    summary: 'Base64 encoded username:password (your-username:your-password)',
  },
  CorrelationId: {
    description: 'X-Correlation-ID header for request tracing',
    value: '123e4567-e89b-12d3-a456-************',
    summary: 'UUID format correlation ID',
  },
};
