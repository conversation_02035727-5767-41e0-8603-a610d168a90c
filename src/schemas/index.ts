/**
 * Schema Exports
 *
 * Central export point for all Zod validation schemas used in the TMS API.
 */

export {
  LessonMetadataSchema,
  type LessonMetadata,
} from './lesson-metadata.schema';

export {
  QzF2fMetadataSchema,
  QzF2fQuestionSchema,
  type QzF2fMetadata,
  type QzF2fQuestion,
} from './qz-f2f-metadata.schema';

export {
  ErrorResponseSchema,
  ValidationErrorDetailSchema,
  ValidationErrorResponseSchema,
  AuthenticationErrorResponseSchema,
  NotFoundErrorResponseSchema,
  FileTooLargeErrorResponseSchema,
  InternalServerErrorResponseSchema,
  CommonErrorResponses,
  AuthenticationExamples,
} from './error-response.schema';
