/**
 * Authentication Configuration
 *
 * This file contains the authentication configuration for Basic Auth.
 * It provides configuration options for validating user credentials.
 */

import { registerAs } from '@nestjs/config';

export interface AuthConfig {
  username: string;
  password: string;
}

export default registerAs('auth', (): AuthConfig => {
  const username = process.env.AUTH_USERNAME;
  const password = process.env.AUTH_PASSWORD;

  if (!username || !password) {
    throw new Error(
      'SECURITY ERROR: AUTH_USERNAME and AUTH_PASSWORD environment variables are required. ' +
        'No default credentials are provided for security reasons.',
    );
  }

  return {
    username,
    password,
  };
});
