/**
 * Quiz Service Unit Tests
 *
 * Tests for the QuizService functionality.
 */

import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';

import {
  NotFoundException,
  InternalServerErrorException,
  BadRequestException,
} from '@nestjs/common';
import { QuizService } from './quiz.service';
import {
  Quiz,
  QuizAsset,
  QuizType,
  MaterialCategory,
  MaterialVersion,
  WeekType,
  AssetType,
} from '../entities';
import { QuizQueryDto, QuizUploadDto } from '../dto';
import { MinioService } from '../minio/minio.service';

// Type definitions for test data
interface LessonMetadata {
  grade: number;
  subject: string;
  course: string;
  classLevel: string;
  color: string;
  topic: string;
}

interface InternalMetadata {
  questionId: string;
  questionNumber: string;
  smilFile: string;
  marksJson: string;
}

// Type for ZIP contents structure
interface ZipContents {
  lessonMetadata: string;
  qzF2fMetadata: string;
  gifFiles: { filename: string; data: Buffer }[];
}

// Type for accessing private methods in tests
interface QuizServiceWithPrivateMethods {
  validateZipStructure(zipContents: ZipContents, correlationId: string): void;
  parseLessonMetadata(
    metadataJson: string,
    correlationId: string,
  ): LessonMetadata;
  parseQzF2fMetadata(
    metadataJson: string,
    correlationId: string,
  ): InternalMetadata[];
  cleanupUploadedFiles(
    uploadedFiles: { bucketName: string; objectName: string }[],
    correlationId: string,
  ): Promise<void>;
}

describe('QuizService', () => {
  let service: QuizService;

  const mockRepository = {
    createQueryBuilder: jest.fn(),
    findOne: jest.fn(),
  };

  const mockQuizAssetRepository = {
    save: jest.fn(),
  };

  const mockMinioService = {
    getPresignedUrl: jest.fn(),
    ensureBucket: jest.fn(),
    uploadFile: jest.fn(),
    deleteFile: jest.fn(),
    getDefaultBucket: jest.fn(),
  };

  const mockDataSource = {
    createQueryRunner: jest.fn(),
  };

  const mockDeleteQueryBuilder = {
    delete: jest.fn().mockReturnThis(),
    from: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    execute: jest.fn(),
  };

  const mockQueryRunner = {
    connect: jest.fn(),
    startTransaction: jest.fn(),
    commitTransaction: jest.fn(),
    rollbackTransaction: jest.fn(),
    release: jest.fn(),
    manager: {
      save: jest.fn(),
      delete: jest.fn(),
      createQueryBuilder: jest.fn().mockReturnValue({
        relation: jest.fn().mockReturnValue({
          of: jest.fn().mockReturnValue({
            add: jest.fn(),
          }),
        }),
        delete: jest.fn().mockReturnValue(mockDeleteQueryBuilder),
      }),
    },
  };

  const mockQueryBuilder = {
    leftJoinAndSelect: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    getOne: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        QuizService,
        {
          provide: getRepositoryToken(Quiz),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(QuizAsset),
          useValue: mockQuizAssetRepository,
        },
        {
          provide: MinioService,
          useValue: mockMinioService,
        },
        {
          provide: DataSource,
          useValue: mockDataSource,
        },
      ],
    }).compile();

    service = module.get<QuizService>(QuizService);

    // Reset mocks
    mockRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);
    mockDataSource.createQueryRunner.mockReturnValue(mockQueryRunner);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findQuizWorkedSolutions', () => {
    const validQueryDto: QuizQueryDto = {
      grade: 12,
      subject: 'Math',
      course: '3U',
      classLevel: 'A1',
      color: 'R',
      year: 2025,
      term: 2,
      week: 4,
      weekType: WeekType.NORMAL,
      teachingProgram: 'St George Girls',
      lessonName: 'Formulae',
    };

    const mockQuizAsset: QuizAsset = {
      id: 'asset-uuid-1',
      assetId: '3421',
      filePath: 'questions-work-solutions/3421.gif',
      type: AssetType.QUESTION,
      fileSize: 1024,
      mimeType: 'image/gif',
      originalFilename: '3421.gif',
      width: 800,
      height: 600,
      quizzes: [],
      createdAt: new Date(),
      updatedAt: new Date(),
      beforeInsert: () => {},
      beforeUpdate: () => {},
    } as unknown as QuizAsset;

    const mockQuiz = {
      id: 'quiz-uuid-1',
      category: MaterialCategory.QUIZ,
      versionType: MaterialVersion.WORKED_SOLUTIONS,
      quizType: QuizType.F2F,
      grade: 12,
      subject: 'Math',
      course: '3U',
      classLevel: 'A1',
      color: 'R',
      year: 2025,
      term: 2,
      week: 4,
      weekType: WeekType.NORMAL,
      teachingProgram: 'St George Girls',
      lessonName: 'Formulae',
      internalMetadata: [
        {
          questionId: '2013',
          questionNumber: '1',
          smilFile: 'Q2013.smil',
          marksJson: '[{"index": "0", "mark": 0}]',
        },
      ] as unknown as Record<string, unknown>,
      originalFilename: 'Dr Du_Math__V6_Y09_Formulae_A1.R_for_Y09__F2F QZ.zip',
      uploadTimestamp: new Date('2025-05-02T01:38:23.926Z'),
      assets: [mockQuizAsset],
      totalMarks: 100,
      createdAt: new Date(),
      updatedAt: new Date(),
      beforeInsert: () => {},
      beforeUpdate: () => {},
    } as unknown as Quiz;

    const correlationId = '123e4567-e89b-12d3-a456-************';

    it('should return quiz response when quiz is found', async () => {
      // Arrange
      mockQueryBuilder.getOne.mockResolvedValue(mockQuiz);
      mockMinioService.getPresignedUrl.mockResolvedValue(
        'https://tms-minio:9000/questions-work-solutions/3421.gif?X-Amz-Algorithm=AWS4-HMAX-SHA...',
      );

      // Act
      const result = await service.findQuizWorkedSolutions(
        validQueryDto,
        correlationId,
      );

      // Assert
      expect(result).toBeDefined();
      expect(result.id).toBeGreaterThan(0); // Should be a positive number (hashed from UUID)
      expect(result.retrievedMetadata.subject).toBe('Math');
      expect(result.retrievedMetadata.grade).toBe(12);
      expect(result.gifUrls).toHaveLength(1);
      expect(result.gifUrls[0].id).toBe('3421');
      expect(result.gifUrls[0].url).toContain(
        'questions-work-solutions/3421.gif',
      );

      // Verify repository calls
      expect(mockRepository.createQueryBuilder).toHaveBeenCalledWith('quiz');
      expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledWith(
        'quiz.assets',
        'assets',
      );
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        'quiz.category = :category',
        {
          category: MaterialCategory.QUIZ,
        },
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'quiz.versionType = :versionType',
        {
          versionType: MaterialVersion.WORKED_SOLUTIONS,
        },
      );
    });

    it('should throw NotFoundException when quiz is not found', async () => {
      // Arrange
      mockQueryBuilder.getOne.mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.findQuizWorkedSolutions(validQueryDto, correlationId),
      ).rejects.toThrow(NotFoundException);
      await expect(
        service.findQuizWorkedSolutions(validQueryDto, correlationId),
      ).rejects.toThrow('No quiz found matching the provided criteria');
    });

    it('should handle quiz without optional fields', async () => {
      // Arrange
      const quizWithoutOptionals = {
        ...mockQuiz,
        teachingProgram: null,
        lessonName: null,
        course: '',
      };
      mockQueryBuilder.getOne.mockResolvedValue(quizWithoutOptionals);
      mockMinioService.getPresignedUrl.mockResolvedValue(
        'https://example.com/asset.gif',
      );

      // Act
      const result = await service.findQuizWorkedSolutions(
        validQueryDto,
        correlationId,
      );

      // Assert
      expect(result.retrievedMetadata.teachingProgram).toBeUndefined();
      expect(result.retrievedMetadata.lessonName).toBe(null);
      expect(result.retrievedMetadata.course).toBe('');
    });

    it('should handle quiz with no assets', async () => {
      // Arrange
      const quizWithoutAssets = { ...mockQuiz, assets: [] };
      mockQueryBuilder.getOne.mockResolvedValue(quizWithoutAssets);

      // Act
      const result = await service.findQuizWorkedSolutions(
        validQueryDto,
        correlationId,
      );

      // Assert
      expect(result.gifUrls).toHaveLength(0);
      expect(mockMinioService.getPresignedUrl).not.toHaveBeenCalled();
    });

    it('should continue with other assets if one fails to generate URL', async () => {
      // Arrange
      const asset2: QuizAsset = {
        ...mockQuizAsset,
        id: 'asset-uuid-2',
        assetId: '3422',
        filePath: 'questions-work-solutions/3422.gif',
      } as QuizAsset;
      const quizWithMultipleAssets = {
        ...mockQuiz,
        assets: [mockQuizAsset, asset2],
      };

      mockQueryBuilder.getOne.mockResolvedValue(quizWithMultipleAssets);
      mockMinioService.getPresignedUrl
        .mockRejectedValueOnce(
          new Error('Failed to generate URL for first asset'),
        )
        .mockResolvedValueOnce('https://example.com/3422.gif');

      // Act
      const result = await service.findQuizWorkedSolutions(
        validQueryDto,
        correlationId,
      );

      // Assert
      expect(result.gifUrls).toHaveLength(1);
      expect(result.gifUrls[0].id).toBe('3422');
      expect(mockMinioService.getPresignedUrl).toHaveBeenCalledTimes(2);
    });

    it('should handle database errors', async () => {
      // Arrange
      mockQueryBuilder.getOne.mockRejectedValue(
        new Error('Database connection failed'),
      );

      // Act & Assert
      await expect(
        service.findQuizWorkedSolutions(validQueryDto, correlationId),
      ).rejects.toThrow(InternalServerErrorException);
      await expect(
        service.findQuizWorkedSolutions(validQueryDto, correlationId),
      ).rejects.toThrow('An error occurred while retrieving quiz data');
    });

    it('should build correct query for all required filters', async () => {
      // Arrange
      mockQueryBuilder.getOne.mockResolvedValue(mockQuiz);
      mockMinioService.getPresignedUrl.mockResolvedValue(
        'https://example.com/asset.gif',
      );

      // Act
      await service.findQuizWorkedSolutions(validQueryDto, correlationId);

      // Assert - Verify all required filters are applied
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'quiz.grade = :grade',
        { grade: 12 },
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'quiz.subject = :subject',
        {
          subject: 'Math',
        },
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'quiz.course = :course',
        {
          course: '3U',
        },
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'quiz.classLevel = :classLevel',
        {
          classLevel: 'A1',
        },
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'quiz.color = :color',
        { color: 'R' },
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'quiz.year = :year',
        { year: 2025 },
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'quiz.term = :term',
        { term: 2 },
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'quiz.week = :week',
        { week: 4 },
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'quiz.weekType = :weekType',
        {
          weekType: WeekType.NORMAL,
        },
      );
    });

    it('should add optional filters when provided', async () => {
      // Arrange
      mockQueryBuilder.getOne.mockResolvedValue(mockQuiz);
      mockMinioService.getPresignedUrl.mockResolvedValue(
        'https://example.com/asset.gif',
      );

      // Act
      await service.findQuizWorkedSolutions(validQueryDto, correlationId);

      // Assert - Verify optional filters are applied
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'quiz.teachingProgram = :teachingProgram',
        {
          teachingProgram: 'St George Girls',
        },
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'quiz.lessonName = :lessonName',
        {
          lessonName: 'Formulae',
        },
      );
    });

    it('should not add optional filters when not provided', async () => {
      // Arrange
      const queryWithoutOptionals: QuizQueryDto = {
        grade: 10,
        subject: 'Science',
        course: '2U',
        classLevel: 'B2',
        color: 'B',
        year: 2024,
        term: 1,
        week: 10,
        weekType: WeekType.HOLIDAY,
      };

      mockQueryBuilder.getOne.mockResolvedValue(mockQuiz);
      mockMinioService.getPresignedUrl.mockResolvedValue(
        'https://example.com/asset.gif',
      );

      // Act
      await service.findQuizWorkedSolutions(
        queryWithoutOptionals,
        correlationId,
      );

      // Assert - Verify optional filters are NOT applied
      expect(mockQueryBuilder.andWhere).not.toHaveBeenCalledWith(
        'quiz.teachingProgram = :teachingProgram',
        expect.any(Object),
      );
      expect(mockQueryBuilder.andWhere).not.toHaveBeenCalledWith(
        'quiz.lessonName = :lessonName',
        expect.any(Object),
      );
    });
  });

  describe('uploadQuizWorkedSolutions', () => {
    const validUploadDto: QuizUploadDto = {
      year: 2025,
      term: 2,
      week: 4,
      weekType: WeekType.NORMAL,
      teachingProgram: 'St George Girls',
    };

    const mockFile: Express.Multer.File = {
      fieldname: 'file',
      originalname: 'Dr Du_Math__V2_Y09_Trigonometry_A1.R_for_Y09__F2F QZ.zip',
      encoding: '7bit',
      mimetype: 'application/zip',
      size: 1024,
      buffer: Buffer.from('mock zip content'),
      destination: '',
      filename: '',
      path: '',
      stream: {} as unknown as import('stream').Readable,
    };

    const correlationId = '123e4567-e89b-12d3-a456-************';

    const mockLessonMetadata = {
      grade: 9,
      subject: 'Math',
      course: '',
      classLevel: 'A1',
      color: 'R',
      topic: 'Trigonometry',
    };

    const mockInternalMetadata = [
      {
        questionId: '1137',
        questionNumber: '1',
        smilFile: 'Q1137.smil',
        marksJson: '[{"index": "0", "mark": 0}]',
      },
      {
        questionId: '2013',
        questionNumber: '2',
        smilFile: 'Q2013.smil',
        marksJson: '[{"index": "0", "mark": 0}]',
      },
    ];

    const mockZipContents = {
      lessonMetadata: JSON.stringify(mockLessonMetadata),
      qzF2fMetadata: JSON.stringify(mockInternalMetadata),
      gifFiles: [
        { filename: '1137.gif', data: Buffer.from('gif1') },
        { filename: '2013.gif', data: Buffer.from('gif2') },
      ],
    };

    beforeEach(() => {
      // Reset all mocks
      jest.clearAllMocks();
      mockDataSource.createQueryRunner.mockReturnValue(mockQueryRunner);
      mockQueryRunner.manager.save.mockResolvedValue({
        id: 'quiz-uuid-1',
        createdAt: new Date('2025-05-24T21:45:23.926Z'),
      });
      mockMinioService.ensureBucket.mockResolvedValue(undefined);
      mockMinioService.uploadFile.mockResolvedValue(undefined);
      mockMinioService.deleteFile.mockResolvedValue(undefined);
      mockMinioService.getDefaultBucket.mockReturnValue('tms-dev-minio-bucket');
    });

    it('should successfully upload quiz with valid ZIP file', async () => {
      // Arrange
      const extractZipContentsSpy = jest
        .spyOn(service, 'extractZipContents' as keyof QuizService)
        .mockResolvedValue(mockZipContents as any);
      const validateZipStructureSpy = jest
        .spyOn(service as any, 'validateZipStructure')
        .mockImplementation(() => {});
      const parseLessonMetadataSpy = jest
        .spyOn(service as any, 'parseLessonMetadata')
        .mockReturnValue(mockLessonMetadata);
      const parseQzF2fMetadataSpy = jest
        .spyOn(service as any, 'parseQzF2fMetadata')
        .mockReturnValue(mockInternalMetadata);

      // Act
      const result = await service.uploadQuizWorkedSolutions(
        validUploadDto,
        mockFile,
        correlationId,
      );

      // Assert
      expect(result).toBeDefined();
      expect(result.message).toBe(
        'Quiz worked solutions uploaded and processed successfully',
      );
      expect(result.data).toBeDefined();
      expect(result.data.quizId).toBeDefined();
      expect(result.data.uploadedGifs).toHaveLength(2);
      expect(result.data.gifCount).toBe(2);
      expect(result.data.metadataPath).toContain('Math/9/A1/2025/2/4/normal');
      expect(result.data.extractedMetadata).toBeDefined();
      expect(result.data.extractedMetadata.subject).toBe('Math');
      expect(result.data.uploadTimestamp).toBeDefined();
      expect(result.data.originalFilename).toBe(mockFile.originalname);

      // Verify method calls
      expect(extractZipContentsSpy).toHaveBeenCalledWith(
        mockFile.buffer,
        correlationId,
      );
      expect(validateZipStructureSpy).toHaveBeenCalledWith(
        mockZipContents,
        correlationId,
      );
      expect(parseLessonMetadataSpy).toHaveBeenCalledWith(
        mockZipContents.lessonMetadata,
        correlationId,
      );
      expect(parseQzF2fMetadataSpy).toHaveBeenCalledWith(
        mockZipContents.qzF2fMetadata,
        correlationId,
      );

      // Verify transaction management
      expect(mockQueryRunner.connect).toHaveBeenCalled();
      expect(mockQueryRunner.startTransaction).toHaveBeenCalled();
      expect(mockQueryRunner.commitTransaction).toHaveBeenCalled();
      expect(mockQueryRunner.release).toHaveBeenCalled();

      // Verify MinIO operations
      expect(mockMinioService.ensureBucket).toHaveBeenCalledWith(
        'tms-dev-minio-bucket',
      );
      expect(mockMinioService.uploadFile).toHaveBeenCalledTimes(2);
    });

    it('should handle ZIP extraction errors', async () => {
      // Arrange
      jest
        .spyOn(service, 'extractZipContents' as keyof QuizService)
        .mockRejectedValue(new BadRequestException('Invalid ZIP file format'));

      // Act & Assert
      await expect(
        service.uploadQuizWorkedSolutions(
          validUploadDto,
          mockFile,
          correlationId,
        ),
      ).rejects.toThrow(BadRequestException);

      // Verify transaction rollback
      expect(mockQueryRunner.rollbackTransaction).toHaveBeenCalled();
      expect(mockQueryRunner.release).toHaveBeenCalled();
    });

    it('should handle ZIP structure validation errors', async () => {
      // Arrange
      jest
        .spyOn(service, 'extractZipContents' as keyof QuizService)
        .mockResolvedValue(mockZipContents as any);
      jest
        .spyOn(service as any, 'validateZipStructure')
        .mockImplementation(() => {
          throw new BadRequestException('Missing required file: QzF2f.json');
        });

      // Act & Assert
      await expect(
        service.uploadQuizWorkedSolutions(
          validUploadDto,
          mockFile,
          correlationId,
        ),
      ).rejects.toThrow(BadRequestException);

      // Verify transaction rollback
      expect(mockQueryRunner.rollbackTransaction).toHaveBeenCalled();
      expect(mockQueryRunner.release).toHaveBeenCalled();
    });

    it('should handle metadata parsing errors', async () => {
      // Arrange
      jest
        .spyOn(service, 'extractZipContents' as keyof QuizService)
        .mockResolvedValue(mockZipContents as any);
      jest
        .spyOn(service as any, 'validateZipStructure')
        .mockImplementation(() => {});
      jest
        .spyOn(service as any, 'parseLessonMetadata')
        .mockImplementation(() => {
          throw new BadRequestException('Invalid LessonMetadata.json format');
        });

      // Act & Assert
      await expect(
        service.uploadQuizWorkedSolutions(
          validUploadDto,
          mockFile,
          correlationId,
        ),
      ).rejects.toThrow(BadRequestException);

      // Verify transaction rollback
      expect(mockQueryRunner.rollbackTransaction).toHaveBeenCalled();
      expect(mockQueryRunner.release).toHaveBeenCalled();
    });

    it('should handle database save errors and cleanup MinIO files', async () => {
      // Arrange
      jest
        .spyOn(service, 'extractZipContents' as keyof QuizService)
        .mockResolvedValue(mockZipContents as any);
      jest
        .spyOn(service as any, 'validateZipStructure')
        .mockImplementation(() => {});
      jest
        .spyOn(service as any, 'parseLessonMetadata')
        .mockReturnValue(mockLessonMetadata);
      jest
        .spyOn(service as any, 'parseQzF2fMetadata')
        .mockReturnValue(mockInternalMetadata);

      // Mock database save failure
      mockQueryRunner.manager.save.mockRejectedValue(
        new Error('Database connection failed'),
      );

      // Act & Assert
      await expect(
        service.uploadQuizWorkedSolutions(
          validUploadDto,
          mockFile,
          correlationId,
        ),
      ).rejects.toThrow(InternalServerErrorException);

      // Verify transaction rollback and cleanup
      expect(mockQueryRunner.rollbackTransaction).toHaveBeenCalled();
      expect(mockQueryRunner.release).toHaveBeenCalled();
    });

    it('should handle MinIO upload errors and rollback transaction', async () => {
      // Arrange
      jest
        .spyOn(service, 'extractZipContents' as keyof QuizService)
        .mockResolvedValue(mockZipContents as any);
      jest
        .spyOn(service as any, 'validateZipStructure')
        .mockImplementation(() => {});
      jest
        .spyOn(service as any, 'parseLessonMetadata')
        .mockReturnValue(mockLessonMetadata);
      jest
        .spyOn(service as any, 'parseQzF2fMetadata')
        .mockReturnValue(mockInternalMetadata);

      // Mock MinIO upload failure
      mockMinioService.uploadFile.mockRejectedValue(
        new Error('MinIO upload failed'),
      );

      // Act & Assert
      await expect(
        service.uploadQuizWorkedSolutions(
          validUploadDto,
          mockFile,
          correlationId,
        ),
      ).rejects.toThrow(InternalServerErrorException);

      // Verify transaction rollback
      expect(mockQueryRunner.rollbackTransaction).toHaveBeenCalled();
      expect(mockQueryRunner.release).toHaveBeenCalled();
    });
  });

  describe('validateZipStructure', () => {
    const correlationId = '123e4567-e89b-12d3-a456-************';

    it('should pass validation with valid ZIP contents', () => {
      // Arrange
      const validZipContents = {
        lessonMetadata: '{"grade": 9, "subject": "Math"}',
        qzF2fMetadata: '[{"questionId": "1137"}]',
        gifFiles: [{ filename: '1137.gif', data: Buffer.from('gif') }],
      };

      // Act & Assert
      expect(() => {
        (service as any).validateZipStructure(validZipContents, correlationId);
      }).not.toThrow();
    });

    it('should throw error when LessonMetadata.json is missing', () => {
      // Arrange
      const invalidZipContents = {
        lessonMetadata: '',
        qzF2fMetadata: '[{"questionId": "1137"}]',
        gifFiles: [{ filename: '1137.gif', data: Buffer.from('gif') }],
      };

      // Act & Assert
      expect(() => {
        (service as any).validateZipStructure(
          invalidZipContents,
          correlationId,
        );
      }).toThrow(BadRequestException);
      expect(() => {
        (service as any).validateZipStructure(
          invalidZipContents,
          correlationId,
        );
      }).toThrow('Missing required file: LessonMetadata.json');
    });

    it('should throw error when QzF2f.json is missing', () => {
      // Arrange
      const invalidZipContents = {
        lessonMetadata: '{"grade": 9, "subject": "Math"}',
        qzF2fMetadata: '',
        gifFiles: [{ filename: '1137.gif', data: Buffer.from('gif') }],
      };

      // Act & Assert
      expect(() => {
        (service as any).validateZipStructure(
          invalidZipContents,
          correlationId,
        );
      }).toThrow(BadRequestException);
      expect(() => {
        (service as any).validateZipStructure(
          invalidZipContents,
          correlationId,
        );
      }).toThrow('Missing required file: QzF2f.json');
    });

    it('should throw error when no GIF files are found', () => {
      // Arrange
      const invalidZipContents = {
        lessonMetadata: '{"grade": 9, "subject": "Math"}',
        qzF2fMetadata: '[{"questionId": "1137"}]',
        gifFiles: [],
      };

      // Act & Assert
      expect(() => {
        (service as any).validateZipStructure(
          invalidZipContents,
          correlationId,
        );
      }).toThrow(BadRequestException);
      expect(() => {
        (service as any).validateZipStructure(
          invalidZipContents,
          correlationId,
        );
      }).toThrow('No GIF files found in solution/ directory');
    });
  });

  describe('parseLessonMetadata', () => {
    const correlationId = '123e4567-e89b-12d3-a456-************';

    it('should parse valid lesson metadata', () => {
      // Arrange
      const validMetadata = JSON.stringify({
        grade: 9,
        subject: 'Math',
        course: '',
        classLevel: 'A1',
        color: 'R',
        topic: 'Trigonometry',
      });

      // Act
      const result: LessonMetadata = (service as any).parseLessonMetadata(
        validMetadata,
        correlationId,
      );

      // Assert
      expect(result).toBeDefined();
      expect(result.grade).toBe(9);
      expect(result.subject).toBe('Math');
      expect(result.classLevel).toBe('A1');
      expect(result.color).toBe('R');
    });

    // === EDGE CASE TESTS FROM TRAINING DATA ANALYSIS ===

    it('should handle empty course field correctly', () => {
      // Arrange - Based on actual training data pattern
      const metadataWithEmptyCourse = JSON.stringify({
        grade: 10,
        subject: 'Math',
        course: '', // Empty course field found in training data
        classLevel: 'A1',
        color: 'R',
        topic: 'Polynomial Function (II)',
      });

      // Act
      const result: LessonMetadata = (service as any).parseLessonMetadata(
        metadataWithEmptyCourse,
        correlationId,
      );

      // Assert
      expect(result).toBeDefined();
      expect(result.course).toBe(''); // Should preserve empty course
      expect(result.grade).toBe(10);
      expect(result.subject).toBe('Math');
      expect(result.classLevel).toBe('A1');
      expect(result.color).toBe('R');
      expect(result.topic).toBe('Polynomial Function (II)');
    });

    it('should handle special characters in topic names', () => {
      // Arrange - Based on actual training data patterns
      const specialCharacterTopics = [
        'Polynomial Function (II)', // Parentheses
        'Trigonometric Functions (II)', // Parentheses with Roman numerals
        'Half-Yearly Enhancement (I)', // Hyphen and parentheses
        'Topic Enhancement (I)', // Parentheses with Roman numerals
        'Plane Geometry (IV)', // Parentheses with Roman numerals
      ];

      for (const topic of specialCharacterTopics) {
        const metadata = JSON.stringify({
          grade: 11,
          subject: 'Math',
          course: '3U',
          classLevel: 'A1',
          color: 'R',
          topic: topic,
        });

        // Act
        const result: LessonMetadata = (service as any).parseLessonMetadata(
          metadata,
          correlationId,
        );

        // Assert
        expect(result).toBeDefined();
        expect(result.topic).toBe(topic);
        expect(result.grade).toBe(11);
        expect(result.subject).toBe('Math');
      }
    });

    it('should handle different class level variations', () => {
      // Arrange - Based on actual training data patterns
      const classLevelVariations = ['A', 'A1', 'A2', 'A3', 'B'];

      for (const classLevel of classLevelVariations) {
        const metadata = JSON.stringify({
          grade: 11,
          subject: 'Math',
          course: '3U',
          classLevel: classLevel,
          color: 'R',
          topic: 'Polynomial Functions (II)',
        });

        // Act
        const result: LessonMetadata = (service as any).parseLessonMetadata(
          metadata,
          correlationId,
        );

        // Assert
        expect(result).toBeDefined();
        expect(result.classLevel).toBe(classLevel);
        expect(result.grade).toBe(11);
        expect(result.subject).toBe('Math');
      }
    });

    it('should handle different color codes', () => {
      // Arrange - Based on actual training data patterns
      const colorCodes = ['R', 'Y']; // Red and Yellow versions

      for (const color of colorCodes) {
        const metadata = JSON.stringify({
          grade: 11,
          subject: 'Math',
          course: '3U',
          classLevel: 'B',
          color: color,
          topic: 'Binomial Expansion',
        });

        // Act
        const result: LessonMetadata = (service as any).parseLessonMetadata(
          metadata,
          correlationId,
        );

        // Assert
        expect(result).toBeDefined();
        expect(result.color).toBe(color);
        expect(result.grade).toBe(11);
        expect(result.subject).toBe('Math');
      }
    });

    it('should handle different course levels', () => {
      // Arrange - Based on actual training data patterns
      const courseLevels = ['2U', '3U', '4U', '']; // Including empty course

      for (const course of courseLevels) {
        const metadata = JSON.stringify({
          grade: course === '4U' ? 12 : 11, // 4U is typically Year 12
          subject: 'Math',
          course: course,
          classLevel: 'A1',
          color: 'R',
          topic:
            course === '4U'
              ? 'Topic Enhancement (I)'
              : 'Polynomial Functions (II)',
        });

        // Act
        const result: LessonMetadata = (service as any).parseLessonMetadata(
          metadata,
          correlationId,
        );

        // Assert
        expect(result).toBeDefined();
        expect(result.course).toBe(course);
        expect(result.subject).toBe('Math');
      }
    });

    it('should handle different grade levels from training data', () => {
      // Arrange - Based on actual training data patterns
      const gradeLevels = [9, 10, 11, 12];

      for (const grade of gradeLevels) {
        const metadata = JSON.stringify({
          grade: grade,
          subject: 'Math',
          course: grade >= 11 ? '3U' : '', // Higher grades typically have course levels
          classLevel: 'A1',
          color: 'R',
          topic: grade === 9 ? 'Trigonometry' : 'Polynomial Functions (II)',
        });

        // Act
        const result: LessonMetadata = (service as any).parseLessonMetadata(
          metadata,
          correlationId,
        );

        // Assert
        expect(result).toBeDefined();
        expect(result.grade).toBe(grade);
        expect(result.subject).toBe('Math');
      }
    });

    it('should throw error for invalid JSON', () => {
      // Arrange
      const invalidJson = 'invalid json content';

      // Act & Assert
      expect(() => {
        (service as any).parseLessonMetadata(invalidJson, correlationId);
      }).toThrow(BadRequestException);
      expect(() => {
        (service as any).parseLessonMetadata(invalidJson, correlationId);
      }).toThrow('Invalid LessonMetadata.json format');
    });

    it('should throw error for missing required fields', () => {
      // Arrange
      const incompleteMetadata = JSON.stringify({
        grade: 9,
        // Missing required fields: subject, classLevel, color
      });

      // Act & Assert
      expect(() => {
        (service as any).parseLessonMetadata(incompleteMetadata, correlationId);
      }).toThrow(BadRequestException);
      expect(() => {
        (service as any).parseLessonMetadata(incompleteMetadata, correlationId);
      }).toThrow(
        'LessonMetadata.json validation failed: subject: Required, course: Required, classLevel: Required, color: Required, topic: Required',
      );
    });
  });

  describe('parseQzF2fMetadata', () => {
    const correlationId = '123e4567-e89b-12d3-a456-************';

    it('should parse valid QzF2f metadata', () => {
      // Arrange
      const validMetadata = JSON.stringify([
        {
          questionId: '1137',
          questionNumber: '1',
          smilFile: 'Q1137.smil',
          marksJson: '[{"index": "0", "mark": 0}]',
        },
        {
          questionId: '2013',
          questionNumber: '2',
          smilFile: 'Q2013.smil',
          marksJson: '[{"index": "0", "mark": 0}]',
        },
      ]);

      // Act
      const result: InternalMetadata[] = (service as any).parseQzF2fMetadata(
        validMetadata,
        correlationId,
      );

      // Assert
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(2);
      expect(result[0].questionId).toBe('1137');
      expect(result[1].questionId).toBe('2013');
    });

    // === EDGE CASE TESTS FROM TRAINING DATA ANALYSIS ===

    it('should handle complex question numbering patterns', () => {
      // Arrange - Based on actual training data patterns found in complex numbering files
      const complexNumberingMetadata = JSON.stringify([
        {
          questionId: '1137',
          questionNumber: '1(a)', // Complex numbering pattern
          smilFile: 'Q1137.smil',
          marksJson: '[{"index": "0", "mark": 0}]',
        },
        {
          questionId: '1138',
          questionNumber: '1(b)', // Complex numbering pattern
          smilFile: 'Q1138.smil',
          marksJson: '[{"index": "0", "mark": 0}]',
        },
        {
          questionId: '2013',
          questionNumber: '2(a)', // Complex numbering pattern
          smilFile: 'Q2013.smil',
          marksJson: '[{"index": "0", "mark": 0}]',
        },
        {
          questionId: '2014',
          questionNumber: '2(b)', // Complex numbering pattern
          smilFile: 'Q2014.smil',
          marksJson: '[{"index": "0", "mark": 0}]',
        },
      ]);

      // Act
      const result: InternalMetadata[] = (service as any).parseQzF2fMetadata(
        complexNumberingMetadata,
        correlationId,
      );

      // Assert
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(4);
      expect(result[0].questionNumber).toBe('1(a)');
      expect(result[1].questionNumber).toBe('1(b)');
      expect(result[2].questionNumber).toBe('2(a)');
      expect(result[3].questionNumber).toBe('2(b)');
    });

    it('should handle different marking scheme formats from training data', () => {
      // Arrange - Based on actual marking schemes found in training data
      const differentMarkingSchemes = [
        '[{"index": "0", "mark": 0}]', // Simple marking
        '[{"index": "0", "mark": 0}, {"index": "1", "mark": 1}]', // Multiple marks
        '[{"index": "0", "mark": 0}, {"index": "1", "mark": 1}, {"index": "2", "mark": 2}]', // Complex marking
        '[]', // Empty marking scheme
      ];

      for (let i = 0; i < differentMarkingSchemes.length; i++) {
        const metadata = JSON.stringify([
          {
            questionId: `${1137 + i}`,
            questionNumber: `${i + 1}`,
            smilFile: `Q${1137 + i}.smil`,
            marksJson: differentMarkingSchemes[i],
          },
        ]);

        // Act
        const result: InternalMetadata[] = (service as any).parseQzF2fMetadata(
          metadata,
          correlationId,
        );

        // Assert
        expect(result).toBeDefined();
        expect(Array.isArray(result)).toBe(true);
        expect(result).toHaveLength(1);
        expect(result[0].marksJson).toBe(differentMarkingSchemes[i]);
      }
    });

    it('should handle large question sets from training data', () => {
      // Arrange - Based on actual large question sets found in training data
      const largeQuestionSet: InternalMetadata[] = [];
      for (let i = 1; i <= 10; i++) {
        largeQuestionSet.push({
          questionId: `${1000 + i}`,
          questionNumber: `${i}`,
          smilFile: `Q${1000 + i}.smil`,
          marksJson: '[{"index": "0", "mark": 0}]',
        });
      }
      const largeMetadata = JSON.stringify(largeQuestionSet);

      // Act
      const result: InternalMetadata[] = (service as any).parseQzF2fMetadata(
        largeMetadata,
        correlationId,
      );

      // Assert
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(10);
      for (let i = 0; i < 10; i++) {
        expect(result[i].questionId).toBe(`${1001 + i}`);
        expect(result[i].questionNumber).toBe(`${i + 1}`);
      }
    });

    it('should handle mixed question numbering styles', () => {
      // Arrange - Based on actual mixed patterns found in training data
      const mixedNumberingMetadata = JSON.stringify([
        {
          questionId: '1137',
          questionNumber: '1', // Simple numbering
          smilFile: 'Q1137.smil',
          marksJson: '[{"index": "0", "mark": 0}]',
        },
        {
          questionId: '1138',
          questionNumber: '2(a)', // Complex numbering
          smilFile: 'Q1138.smil',
          marksJson: '[{"index": "0", "mark": 0}]',
        },
        {
          questionId: '1139',
          questionNumber: '2(b)', // Complex numbering
          smilFile: 'Q1139.smil',
          marksJson: '[{"index": "0", "mark": 0}]',
        },
        {
          questionId: '2013',
          questionNumber: '3', // Simple numbering
          smilFile: 'Q2013.smil',
          marksJson: '[{"index": "0", "mark": 0}]',
        },
      ]);

      // Act
      const result: InternalMetadata[] = (service as any).parseQzF2fMetadata(
        mixedNumberingMetadata,
        correlationId,
      );

      // Assert
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(4);
      expect(result[0].questionNumber).toBe('1');
      expect(result[1].questionNumber).toBe('2(a)');
      expect(result[2].questionNumber).toBe('2(b)');
      expect(result[3].questionNumber).toBe('3');
    });

    it('should handle boundary conditions for question IDs', () => {
      // Arrange - Test boundary conditions that might appear in training data
      const boundaryConditions = [
        { questionId: '1', questionNumber: '1' }, // Minimum ID
        { questionId: '9999', questionNumber: '2' }, // Large ID
        { questionId: '0001', questionNumber: '3' }, // Leading zeros
        { questionId: '1137', questionNumber: '4' }, // Typical ID from training data
      ];

      const boundaryMetadata = JSON.stringify(
        boundaryConditions.map((q) => ({
          ...q,
          smilFile: `Q${q.questionId}.smil`,
          marksJson: '[{"index": "0", "mark": 0}]',
        })),
      );

      // Act
      const result: InternalMetadata[] = (service as any).parseQzF2fMetadata(
        boundaryMetadata,
        correlationId,
      );

      // Assert
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(4);
      expect(result[0].questionId).toBe('1');
      expect(result[1].questionId).toBe('9999');
      expect(result[2].questionId).toBe('0001');
      expect(result[3].questionId).toBe('1137');
    });

    it('should throw error for invalid JSON', () => {
      // Arrange
      const invalidJson = 'invalid json content';

      // Act & Assert
      expect(() => {
        (service as any).parseQzF2fMetadata(invalidJson, correlationId);
      }).toThrow(BadRequestException);
      expect(() => {
        (service as any).parseQzF2fMetadata(invalidJson, correlationId);
      }).toThrow('Invalid QzF2f.json format');
    });

    it('should throw error when content is not an array', () => {
      // Arrange
      const nonArrayContent = JSON.stringify({
        questionId: '1137',
        questionNumber: '1',
      });

      // Act & Assert
      expect(() => {
        (service as any).parseQzF2fMetadata(nonArrayContent, correlationId);
      }).toThrow(BadRequestException);
      expect(() => {
        (service as any).parseQzF2fMetadata(nonArrayContent, correlationId);
      }).toThrow(
        'QzF2f.json validation failed: : Expected array, received object',
      );
    });

    it('should throw error for questions missing required fields', () => {
      // Arrange
      const incompleteQuestions = JSON.stringify([
        {
          questionId: '1137',
          // Missing questionNumber
        },
        {
          questionNumber: '2',
          // Missing questionId
        },
      ]);

      // Act & Assert
      expect(() => {
        (service as any).parseQzF2fMetadata(incompleteQuestions, correlationId);
      }).toThrow(BadRequestException);
      expect(() => {
        (service as any).parseQzF2fMetadata(incompleteQuestions, correlationId);
      }).toThrow(
        'QzF2f.json validation failed: 0.questionNumber: Required, 1.questionId: Required',
      );
    });
  });

  describe('deleteQuizWorkedSolutions', () => {
    const validQuizId = '123e4567-e89b-12d3-a456-************';
    const correlationId = '123e4567-e89b-12d3-a456-************';

    const mockQuizWithAssets = {
      id: validQuizId,
      subject: 'Math',
      grade: 9,
      classLevel: 'A1',
      color: 'R',
      assets: [
        {
          id: 'asset-uuid-1',
          assetId: '3421',
          filePath: 'questions-work-solutions/3421.gif',
        },
        {
          id: 'asset-uuid-2',
          assetId: '1234',
          filePath: 'questions-work-solutions/1234.gif',
        },
      ],
    };

    it('should successfully delete quiz with assets', async () => {
      // Arrange
      mockRepository.findOne.mockResolvedValue(mockQuizWithAssets);

      // Mock database operations to succeed
      mockQueryRunner.manager.delete.mockResolvedValue({ affected: 1 });
      mockDeleteQueryBuilder.execute.mockResolvedValue({ affected: 1 });

      const cleanupUploadedFilesSpy = jest
        .spyOn(service as any, 'cleanupUploadedFiles')
        .mockResolvedValue(undefined);

      // Act
      await service.deleteQuizWorkedSolutions(validQuizId, correlationId);

      // Assert
      expect(mockRepository.findOne).toHaveBeenCalledWith({
        where: { id: validQuizId },
        relations: ['assets'],
      });

      // Verify relationship deletions (2 assets)
      expect(mockQueryRunner.manager.createQueryBuilder).toHaveBeenCalled();
      expect(mockQueryRunner.manager.delete).toHaveBeenCalledTimes(3); // 2 assets + 1 quiz

      // Verify quiz deletion
      expect(mockQueryRunner.manager.delete).toHaveBeenCalledWith(Quiz, {
        id: validQuizId,
      });

      // Verify transaction management
      expect(mockQueryRunner.startTransaction).toHaveBeenCalled();
      expect(mockQueryRunner.commitTransaction).toHaveBeenCalled();
      expect(mockQueryRunner.release).toHaveBeenCalled();

      // Verify MinIO cleanup
      expect(cleanupUploadedFilesSpy).toHaveBeenCalledWith(
        [
          {
            bucketName: 'questions-work-solutions',
            objectName: '3421.gif',
          },
          {
            bucketName: 'questions-work-solutions',
            objectName: '1234.gif',
          },
        ],
        correlationId,
      );
    });

    it('should successfully delete quiz without assets', async () => {
      // Arrange
      const quizWithoutAssets = { ...mockQuizWithAssets, assets: [] };
      mockRepository.findOne.mockResolvedValue(quizWithoutAssets);

      // Mock database operations to succeed
      mockQueryRunner.manager.delete.mockResolvedValue({ affected: 1 });

      const cleanupUploadedFilesSpy = jest
        .spyOn(service as any, 'cleanupUploadedFiles')
        .mockResolvedValue(undefined);

      // Act
      await service.deleteQuizWorkedSolutions(validQuizId, correlationId);

      // Assert
      expect(mockRepository.findOne).toHaveBeenCalledWith({
        where: { id: validQuizId },
        relations: ['assets'],
      });

      // Verify only quiz deletion (no assets to delete)
      expect(mockQueryRunner.manager.delete).toHaveBeenCalledTimes(1);
      expect(mockQueryRunner.manager.delete).toHaveBeenCalledWith(Quiz, {
        id: validQuizId,
      });

      // Verify transaction management
      expect(mockQueryRunner.startTransaction).toHaveBeenCalled();
      expect(mockQueryRunner.commitTransaction).toHaveBeenCalled();
      expect(mockQueryRunner.release).toHaveBeenCalled();

      // Verify no MinIO cleanup called
      expect(cleanupUploadedFilesSpy).not.toHaveBeenCalled();
    });

    it('should throw NotFoundException when quiz is not found', async () => {
      // Arrange
      mockRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.deleteQuizWorkedSolutions(validQuizId, correlationId),
      ).rejects.toThrow(NotFoundException);
      await expect(
        service.deleteQuizWorkedSolutions(validQuizId, correlationId),
      ).rejects.toThrow(`Quiz with ID ${validQuizId} not found`);

      // Verify transaction rollback
      expect(mockQueryRunner.rollbackTransaction).toHaveBeenCalled();
      expect(mockQueryRunner.release).toHaveBeenCalled();
    });

    it('should handle database deletion errors and rollback transaction', async () => {
      // Arrange
      mockRepository.findOne.mockResolvedValue(mockQuizWithAssets);
      mockQueryRunner.manager.delete.mockRejectedValue(
        new Error('Database deletion failed'),
      );

      // Act & Assert
      await expect(
        service.deleteQuizWorkedSolutions(validQuizId, correlationId),
      ).rejects.toThrow(InternalServerErrorException);
      await expect(
        service.deleteQuizWorkedSolutions(validQuizId, correlationId),
      ).rejects.toThrow('An error occurred while deleting the quiz');

      // Verify transaction rollback
      expect(mockQueryRunner.rollbackTransaction).toHaveBeenCalled();
      expect(mockQueryRunner.release).toHaveBeenCalled();
    });

    it('should handle MinIO cleanup errors gracefully', async () => {
      // Arrange
      mockRepository.findOne.mockResolvedValue(mockQuizWithAssets);

      // Mock database operations to succeed
      mockQueryRunner.manager.delete.mockResolvedValue({ affected: 1 });
      mockDeleteQueryBuilder.execute.mockResolvedValue({ affected: 1 });

      const cleanupUploadedFilesSpy = jest
        .spyOn(service as any, 'cleanupUploadedFiles')
        .mockRejectedValue(new Error('MinIO cleanup failed'));

      // Act - Should not throw error even if MinIO cleanup fails
      await service.deleteQuizWorkedSolutions(validQuizId, correlationId);

      // Assert
      expect(mockQueryRunner.commitTransaction).toHaveBeenCalled();
      expect(mockQueryRunner.release).toHaveBeenCalled();
      expect(cleanupUploadedFilesSpy).toHaveBeenCalled();
    });

    it('should handle complex file paths correctly', async () => {
      // Arrange
      const quizWithComplexPaths = {
        ...mockQuizWithAssets,
        assets: [
          {
            id: 'asset-uuid-1',
            assetId: '3421',
            filePath: 'bucket-name/subfolder/nested/3421.gif',
          },
          {
            id: 'asset-uuid-2',
            assetId: '1234',
            filePath: 'another-bucket/1234.gif',
          },
        ],
      };
      mockRepository.findOne.mockResolvedValue(quizWithComplexPaths);

      // Mock database operations to succeed
      mockQueryRunner.manager.delete.mockResolvedValue({ affected: 1 });
      mockDeleteQueryBuilder.execute.mockResolvedValue({ affected: 1 });

      const cleanupUploadedFilesSpy = jest
        .spyOn(service as any, 'cleanupUploadedFiles')
        .mockResolvedValue(undefined);

      // Act
      await service.deleteQuizWorkedSolutions(validQuizId, correlationId);

      // Assert
      expect(cleanupUploadedFilesSpy).toHaveBeenCalledWith(
        [
          {
            bucketName: 'bucket-name',
            objectName: 'subfolder/nested/3421.gif',
          },
          {
            bucketName: 'another-bucket',
            objectName: '1234.gif',
          },
        ],
        correlationId,
      );
    });
  });
});
