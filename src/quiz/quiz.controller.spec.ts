/**
 * Quiz Controller Unit Tests
 *
 * Tests for the QuizController functionality.
 */

import { Test, TestingModule } from '@nestjs/testing';
import { HttpException, HttpStatus, NotFoundException } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ThrottlerModule } from '@nestjs/throttler';
import { QuizController } from './quiz.controller';
import { QuizService } from './quiz.service';
import { QuizQueryDto, QuizResponseDto, QuizUpdateDto } from '../dto';
import { WeekType } from '../entities';

import { AuthModule } from '../auth/auth.module';

// Mock the security utilities
jest.mock('../utils/security.utils', () => ({
  sanitizeFileUpload: jest
    .fn()
    .mockImplementation((originalname: string, mimetype: string) => {
      // Return invalid for non-ZIP files
      if (originalname && !originalname.toLowerCase().endsWith('.zip')) {
        return {
          isValid: false,
          reason: 'Invalid file type. Only ZIP files are allowed.',
        };
      }
      return { isValid: true };
    }),
}));

describe('QuizController', () => {
  let controller: QuizController;

  const mockQuizService = {
    findQuizWorkedSolutions: jest.fn(),
    updateQuizWorkedSolutions: jest.fn(),
    deleteQuizWorkedSolutions: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: '.env',
        }),
        ThrottlerModule.forRoot([
          {
            name: 'default',
            ttl: 60000,
            limit: 1000,
          },
        ]),
        AuthModule,
      ],
      controllers: [QuizController],
      providers: [
        {
          provide: QuizService,
          useValue: mockQuizService,
        },
      ],
    }).compile();

    controller = module.get<QuizController>(QuizController);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getQuizWorkedSolutions', () => {
    const validQueryDto: QuizQueryDto = {
      grade: 12,
      subject: 'Math',
      course: '3U',
      classLevel: 'A1',
      color: 'R',
      year: 2025,
      term: 2,
      week: 4,
      weekType: WeekType.NORMAL,
      teachingProgram: 'St George Girls',
      lessonName: 'Formulae',
    };

    const mockQuizResponse: QuizResponseDto = {
      id: 1,
      retrievedMetadata: {
        subject: 'Math',
        grade: 12,
        classLevel: 'A1',
        lessonName: 'Formulae',
        color: 'R',
        year: 2025,
        term: 2,
        week: 4,
        weekType: 'normal',
        course: '3U',
        teachingProgram: 'St George Girls',
        internalMetadata: [
          {
            questionId: '2013',
            questionNumber: '1',
            smilFile: 'Q2013.smil',
            marksJson: '[{"index": "0", "mark": 0}]',
          },
        ],
        originalFilename:
          'Dr Du_Math__V6_Y09_Formulae_A1.R_for_Y09__F2F QZ.zip',
        uploadTimestamp: '2025-05-02T01:38:23.926Z',
      },
      gifUrls: [
        {
          id: '3421',
          url: 'https://tms-minio:9000/questions-work-solutions/3421.gif?X-Amz-Algorithm=AWS4-HMAX-SHA...',
        },
      ],
    };

    const correlationId = '123e4567-e89b-12d3-a456-************';

    it('should return quiz response when quiz is found', async () => {
      // Arrange
      mockQuizService.findQuizWorkedSolutions.mockResolvedValue(
        mockQuizResponse,
      );

      // Act
      const result = await controller.getQuizWorkedSolutions(
        validQueryDto,
        correlationId,
      );

      // Assert
      expect(result).toEqual(mockQuizResponse);
      expect(mockQuizService.findQuizWorkedSolutions).toHaveBeenCalledWith(
        validQueryDto,
        correlationId,
      );
      expect(mockQuizService.findQuizWorkedSolutions).toHaveBeenCalledTimes(1);
    });

    it('should throw NotFoundException when quiz is not found', async () => {
      // Arrange
      const notFoundError = new NotFoundException(
        'No quiz found matching the provided criteria',
      );
      mockQuizService.findQuizWorkedSolutions.mockRejectedValue(notFoundError);

      // Act & Assert
      await expect(
        controller.getQuizWorkedSolutions(validQueryDto, correlationId),
      ).rejects.toThrow(NotFoundException);

      expect(mockQuizService.findQuizWorkedSolutions).toHaveBeenCalledWith(
        validQueryDto,
        correlationId,
      );
    });

    it('should throw HttpException for service errors', async () => {
      // Arrange
      const serviceError = new Error('Database connection failed');
      mockQuizService.findQuizWorkedSolutions.mockRejectedValue(serviceError);

      // Act & Assert
      await expect(
        controller.getQuizWorkedSolutions(validQueryDto, correlationId),
      ).rejects.toThrow(HttpException);

      await expect(
        controller.getQuizWorkedSolutions(validQueryDto, correlationId),
      ).rejects.toThrow(
        'An unexpected error occurred while retrieving quiz data',
      );

      expect(mockQuizService.findQuizWorkedSolutions).toHaveBeenCalledWith(
        validQueryDto,
        correlationId,
      );
    });

    it('should re-throw HttpException from service', async () => {
      // Arrange
      const httpError = new HttpException(
        'Service unavailable',
        HttpStatus.SERVICE_UNAVAILABLE,
      );
      mockQuizService.findQuizWorkedSolutions.mockRejectedValue(httpError);

      // Act & Assert
      await expect(
        controller.getQuizWorkedSolutions(validQueryDto, correlationId),
      ).rejects.toThrow(httpError);

      expect(mockQuizService.findQuizWorkedSolutions).toHaveBeenCalledWith(
        validQueryDto,
        correlationId,
      );
    });

    it('should handle query with optional fields omitted', async () => {
      // Arrange
      const queryWithoutOptionals: QuizQueryDto = {
        grade: 10,
        subject: 'Science',
        course: '2U',
        classLevel: 'B2',
        color: 'B',
        year: 2024,
        term: 1,
        week: 10,
        weekType: WeekType.HOLIDAY,
      };

      const responseWithoutOptionals: QuizResponseDto = {
        ...mockQuizResponse,
        retrievedMetadata: {
          ...mockQuizResponse.retrievedMetadata,
          grade: 10,
          subject: 'Science',
          course: '2U',
          classLevel: 'B2',
          color: 'B',
          year: 2024,
          term: 1,
          week: 10,
          weekType: 'holiday',
          teachingProgram: undefined,
          lessonName: '',
        },
      };

      mockQuizService.findQuizWorkedSolutions.mockResolvedValue(
        responseWithoutOptionals,
      );

      // Act
      const result = await controller.getQuizWorkedSolutions(
        queryWithoutOptionals,
        correlationId,
      );

      // Assert
      expect(result).toEqual(responseWithoutOptionals);
      expect(mockQuizService.findQuizWorkedSolutions).toHaveBeenCalledWith(
        queryWithoutOptionals,
        correlationId,
      );
    });

    it('should handle different correlation IDs', async () => {
      // Arrange
      const differentCorrelationId = 'f47ac10b-58cc-4372-a567-0e02b2c3d479';
      mockQuizService.findQuizWorkedSolutions.mockResolvedValue(
        mockQuizResponse,
      );

      // Act
      const result = await controller.getQuizWorkedSolutions(
        validQueryDto,
        differentCorrelationId,
      );

      // Assert
      expect(result).toEqual(mockQuizResponse);
      expect(mockQuizService.findQuizWorkedSolutions).toHaveBeenCalledWith(
        validQueryDto,
        differentCorrelationId,
      );
    });
  });

  describe('updateQuiz', () => {
    const validQuizId = '123e4567-e89b-12d3-a456-************';
    const correlationId = 'f47ac10b-58cc-4372-a567-0e02b2c3d479';

    const validUpdateDto: QuizUpdateDto = {
      year: 2026,
      term: 3,
      week: 10,
      weekType: WeekType.HOLIDAY,
      subject: 'Physics',
      grade: 11,
    };

    const mockUpdatedQuizResponse: QuizResponseDto = {
      id: 1,
      retrievedMetadata: {
        subject: 'Physics',
        grade: 11,
        classLevel: 'A1',
        lessonName: 'Formulae',
        color: 'R',
        year: 2026,
        term: 3,
        week: 10,
        weekType: 'holiday',
        course: '3U',
        teachingProgram: 'St George Girls',
        internalMetadata: [
          {
            questionId: '2013',
            questionNumber: '1',
            smilFile: 'Q2013.smil',
            marksJson: '[{"index": "0", "mark": 0}]',
          },
        ],
        originalFilename:
          'Dr Du_Math__V6_Y09_Formulae_A1.R_for_Y09__F2F QZ.zip',
        uploadTimestamp: '2025-05-02T01:38:23.926Z',
      },
      gifUrls: [
        {
          id: '3421',
          url: 'https://tms-minio:9000/questions-work-solutions/3421.gif?X-Amz-Algorithm=AWS4-HMAX-SHA...',
        },
      ],
    };

    const mockFile: Express.Multer.File = {
      fieldname: 'file',
      originalname: 'updated_quiz.zip',
      encoding: '7bit',
      mimetype: 'application/zip',
      size: 1024,
      buffer: Buffer.from('mock zip content'),
      destination: '',
      filename: '',
      path: '',
      stream: {} as any,
    };

    it('should update quiz with metadata only (no file)', async () => {
      // Arrange
      mockQuizService.updateQuizWorkedSolutions.mockResolvedValue(
        mockUpdatedQuizResponse,
      );

      // Act
      const result = await controller.updateQuiz(
        validQuizId,
        validUpdateDto,
        undefined,
        correlationId,
      );

      // Assert
      expect(result).toEqual(mockUpdatedQuizResponse);
      expect(mockQuizService.updateQuizWorkedSolutions).toHaveBeenCalledWith(
        validQuizId,
        validUpdateDto,
        undefined,
        correlationId,
      );
      expect(mockQuizService.updateQuizWorkedSolutions).toHaveBeenCalledTimes(
        1,
      );
    });

    it('should update quiz with metadata and file', async () => {
      // Arrange
      mockQuizService.updateQuizWorkedSolutions.mockResolvedValue(
        mockUpdatedQuizResponse,
      );

      // Act
      const result = await controller.updateQuiz(
        validQuizId,
        validUpdateDto,
        mockFile,
        correlationId,
      );

      // Assert
      expect(result).toEqual(mockUpdatedQuizResponse);
      expect(mockQuizService.updateQuizWorkedSolutions).toHaveBeenCalledWith(
        validQuizId,
        validUpdateDto,
        mockFile,
        correlationId,
      );
    });

    it('should throw BadRequestException for invalid UUID format', async () => {
      // Arrange
      const invalidId = 'invalid-uuid';

      // Act & Assert
      await expect(
        controller.updateQuiz(
          invalidId,
          validUpdateDto,
          undefined,
          correlationId,
        ),
      ).rejects.toThrow(HttpException);

      await expect(
        controller.updateQuiz(
          invalidId,
          validUpdateDto,
          undefined,
          correlationId,
        ),
      ).rejects.toThrow('Invalid quiz ID format. Must be a valid UUID.');

      expect(mockQuizService.updateQuizWorkedSolutions).not.toHaveBeenCalled();
    });

    it('should throw BadRequestException for invalid file type', async () => {
      // Arrange
      const invalidFile: Express.Multer.File = {
        ...mockFile,
        originalname: 'invalid.txt',
      };

      // Act & Assert
      await expect(
        controller.updateQuiz(
          validQuizId,
          validUpdateDto,
          invalidFile,
          correlationId,
        ),
      ).rejects.toThrow(HttpException);

      await expect(
        controller.updateQuiz(
          validQuizId,
          validUpdateDto,
          invalidFile,
          correlationId,
        ),
      ).rejects.toThrow('Invalid file type. Only ZIP files are allowed.');

      expect(mockQuizService.updateQuizWorkedSolutions).not.toHaveBeenCalled();
    });

    it('should throw NotFoundException when quiz is not found', async () => {
      // Arrange
      const notFoundError = new NotFoundException(
        `Quiz with ID ${validQuizId} not found`,
      );
      mockQuizService.updateQuizWorkedSolutions.mockRejectedValue(
        notFoundError,
      );

      // Act & Assert
      await expect(
        controller.updateQuiz(
          validQuizId,
          validUpdateDto,
          undefined,
          correlationId,
        ),
      ).rejects.toThrow(NotFoundException);

      expect(mockQuizService.updateQuizWorkedSolutions).toHaveBeenCalledWith(
        validQuizId,
        validUpdateDto,
        undefined,
        correlationId,
      );
    });

    it('should throw HttpException for service errors', async () => {
      // Arrange
      const serviceError = new Error('Database connection failed');
      mockQuizService.updateQuizWorkedSolutions.mockRejectedValue(serviceError);

      // Act & Assert
      await expect(
        controller.updateQuiz(
          validQuizId,
          validUpdateDto,
          undefined,
          correlationId,
        ),
      ).rejects.toThrow(HttpException);

      await expect(
        controller.updateQuiz(
          validQuizId,
          validUpdateDto,
          undefined,
          correlationId,
        ),
      ).rejects.toThrow('An unexpected error occurred while updating the quiz');
    });

    it('should handle empty update DTO (no changes)', async () => {
      // Arrange
      const emptyUpdateDto: QuizUpdateDto = {};
      mockQuizService.updateQuizWorkedSolutions.mockResolvedValue(
        mockUpdatedQuizResponse,
      );

      // Act
      const result = await controller.updateQuiz(
        validQuizId,
        emptyUpdateDto,
        undefined,
        correlationId,
      );

      // Assert
      expect(result).toEqual(mockUpdatedQuizResponse);
      expect(mockQuizService.updateQuizWorkedSolutions).toHaveBeenCalledWith(
        validQuizId,
        emptyUpdateDto,
        undefined,
        correlationId,
      );
    });

    it('should accept valid UUID variations', async () => {
      // Arrange
      const uuidVariations = [
        '123e4567-e89b-12d3-a456-************', // lowercase
        '123E4567-E89B-12D3-A456-************', // uppercase
        '123e4567-e89b-42d3-a456-************', // version 4
        '123e4567-e89b-52d3-a456-************', // version 5
      ];

      mockQuizService.updateQuizWorkedSolutions.mockResolvedValue(
        mockUpdatedQuizResponse,
      );

      // Act & Assert
      for (const uuid of uuidVariations) {
        await expect(
          controller.updateQuiz(uuid, validUpdateDto, undefined, correlationId),
        ).resolves.toEqual(mockUpdatedQuizResponse);
      }

      expect(mockQuizService.updateQuizWorkedSolutions).toHaveBeenCalledTimes(
        uuidVariations.length,
      );
    });
  });

  describe('deleteQuiz', () => {
    const validQuizId = '123e4567-e89b-12d3-a456-************';
    const correlationId = 'f47ac10b-58cc-4372-a567-0e02b2c3d479';

    it('should successfully delete quiz', async () => {
      // Arrange
      mockQuizService.deleteQuizWorkedSolutions.mockResolvedValue(undefined);

      // Act
      const result = await controller.deleteQuiz(validQuizId, correlationId);

      // Assert
      expect(result).toEqual({
        message: `Quiz with ID ${validQuizId} has been successfully deleted`,
      });
      expect(mockQuizService.deleteQuizWorkedSolutions).toHaveBeenCalledWith(
        validQuizId,
        correlationId,
      );
      expect(mockQuizService.deleteQuizWorkedSolutions).toHaveBeenCalledTimes(
        1,
      );
    });

    it('should throw BadRequestException for invalid UUID format', async () => {
      // Arrange
      const invalidId = 'invalid-uuid';

      // Act & Assert
      await expect(
        controller.deleteQuiz(invalidId, correlationId),
      ).rejects.toThrow(HttpException);

      await expect(
        controller.deleteQuiz(invalidId, correlationId),
      ).rejects.toThrow('Invalid quiz ID format. Must be a valid UUID.');

      expect(mockQuizService.deleteQuizWorkedSolutions).not.toHaveBeenCalled();
    });

    it('should throw NotFoundException when quiz is not found', async () => {
      // Arrange
      const notFoundError = new NotFoundException(
        `Quiz with ID ${validQuizId} not found`,
      );
      mockQuizService.deleteQuizWorkedSolutions.mockRejectedValue(
        notFoundError,
      );

      // Act & Assert
      await expect(
        controller.deleteQuiz(validQuizId, correlationId),
      ).rejects.toThrow(NotFoundException);

      expect(mockQuizService.deleteQuizWorkedSolutions).toHaveBeenCalledWith(
        validQuizId,
        correlationId,
      );
    });

    it('should throw InternalServerErrorException for unexpected errors', async () => {
      // Arrange
      const unexpectedError = new Error('Database connection failed');
      mockQuizService.deleteQuizWorkedSolutions.mockRejectedValue(
        unexpectedError,
      );

      // Act & Assert
      await expect(
        controller.deleteQuiz(validQuizId, correlationId),
      ).rejects.toThrow(HttpException);

      await expect(
        controller.deleteQuiz(validQuizId, correlationId),
      ).rejects.toThrow('An unexpected error occurred while deleting the quiz');

      expect(mockQuizService.deleteQuizWorkedSolutions).toHaveBeenCalledWith(
        validQuizId,
        correlationId,
      );
    });

    it('should accept valid UUID variations', async () => {
      // Arrange
      const uuidVariations = [
        '123e4567-e89b-12d3-a456-************', // lowercase
        '123E4567-E89B-12D3-A456-************', // uppercase
        '123e4567-e89b-42d3-a456-************', // version 4
        '123e4567-e89b-52d3-a456-************', // version 5
      ];

      mockQuizService.deleteQuizWorkedSolutions.mockResolvedValue(undefined);

      // Act & Assert
      for (const uuid of uuidVariations) {
        await expect(
          controller.deleteQuiz(uuid, correlationId),
        ).resolves.toEqual({
          message: `Quiz with ID ${uuid} has been successfully deleted`,
        });
      }

      expect(mockQuizService.deleteQuizWorkedSolutions).toHaveBeenCalledTimes(
        uuidVariations.length,
      );
    });

    it('should handle edge case UUIDs correctly', async () => {
      // Arrange
      const edgeCaseUuids = [
        '00000000-0000-1000-8000-000000000000', // minimum valid v1
        'ffffffff-ffff-5fff-bfff-ffffffffffff', // maximum valid v5
      ];

      mockQuizService.deleteQuizWorkedSolutions.mockResolvedValue(undefined);

      // Act & Assert
      for (const uuid of edgeCaseUuids) {
        await expect(
          controller.deleteQuiz(uuid, correlationId),
        ).resolves.toEqual({
          message: `Quiz with ID ${uuid} has been successfully deleted`,
        });
      }

      expect(mockQuizService.deleteQuizWorkedSolutions).toHaveBeenCalledTimes(
        edgeCaseUuids.length,
      );
    });

    it('should reject malformed UUIDs', async () => {
      // Arrange
      const malformedUuids = [
        '123e4567-e89b-12d3-a456-42661417400', // too short
        '123e4567-e89b-12d3-a456-************x', // too long
        '123e4567-e89b-12d3-a456-426614174g00', // invalid character
        '123e4567-e89b-12d3-a456', // incomplete
        '', // empty
        'not-a-uuid-at-all', // completely invalid
      ];

      // Act & Assert
      for (const uuid of malformedUuids) {
        await expect(
          controller.deleteQuiz(uuid, correlationId),
        ).rejects.toThrow('Invalid quiz ID format. Must be a valid UUID.');
      }

      expect(mockQuizService.deleteQuizWorkedSolutions).not.toHaveBeenCalled();
    });
  });
});
