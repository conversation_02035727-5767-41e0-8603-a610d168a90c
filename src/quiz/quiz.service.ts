/**
 * Quiz Service
 *
 * This service handles the business logic for quiz operations.
 * It interacts with the database to retrieve quiz data and generates
 * pre-signed URLs for quiz assets stored in Minio.
 */

import {
  Injectable,
  Logger,
  NotFoundException,
  InternalServerErrorException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, QueryRunner } from 'typeorm';
import type { Express } from 'express';
import {
  Quiz,
  QuizAsset,
  QuizType,
  MaterialCategory,
  MaterialVersion,
  AssetType,
} from '../entities';
import {
  QuizQueryDto,
  QuizResponseDto,
  QuizUploadDto,
  QuizUploadResponseDto,
  QuizUpdateDto,
  RetrievedMetadataDto,
  GifUrlDto,
  InternalMetadataDto,
  LessonMetadataDto,
} from '../dto';
import { MinioService } from '../minio/minio.service';
import { LessonMetadataSchema, QzF2fMetadataSchema } from '../schemas';
import * as yauzl from 'yauzl';
import { v4 as uuidv4 } from 'uuid';
import { ZodError } from 'zod';

@Injectable()
export class QuizService {
  private readonly logger = new Logger(QuizService.name);

  constructor(
    @InjectRepository(Quiz)
    private readonly quizRepository: Repository<Quiz>,
    @InjectRepository(QuizAsset)
    private readonly quizAssetRepository: Repository<QuizAsset>,
    private readonly minioService: MinioService,
    private readonly dataSource: DataSource,
  ) {}

  /**
   * Find quiz worked solutions based on query parameters
   *
   * @param queryDto - Query parameters for filtering quizzes
   * @param correlationId - Correlation ID for request tracing
   * @returns Promise<QuizResponseDto> - Quiz data with pre-signed URLs
   */
  async findQuizWorkedSolutions(
    queryDto: QuizQueryDto,
    correlationId: string,
  ): Promise<QuizResponseDto> {
    this.logger.log(
      `Finding quiz worked solutions for correlation ID: ${correlationId}`,
    );

    try {
      // Build the query to find matching quiz
      const queryBuilder = this.quizRepository
        .createQueryBuilder('quiz')
        .leftJoinAndSelect('quiz.assets', 'assets')
        .where('quiz.category = :category', { category: MaterialCategory.QUIZ })
        .andWhere('quiz.versionType = :versionType', {
          versionType: MaterialVersion.WORKED_SOLUTIONS,
        })
        .andWhere('quiz.quizType = :quizType', { quizType: QuizType.F2F })
        .andWhere('quiz.grade = :grade', { grade: queryDto.grade })
        .andWhere('quiz.subject = :subject', { subject: queryDto.subject })
        .andWhere('quiz.course = :course', { course: queryDto.course })
        .andWhere('quiz.classLevel = :classLevel', {
          classLevel: queryDto.classLevel,
        })
        .andWhere('quiz.color = :color', { color: queryDto.color })
        .andWhere('quiz.year = :year', { year: queryDto.year })
        .andWhere('quiz.term = :term', { term: queryDto.term })
        .andWhere('quiz.week = :week', { week: queryDto.week })
        .andWhere('quiz.weekType = :weekType', { weekType: queryDto.weekType });

      // Add optional filters if provided
      if (queryDto.teachingProgram) {
        queryBuilder.andWhere('quiz.teachingProgram = :teachingProgram', {
          teachingProgram: queryDto.teachingProgram,
        });
      }

      if (queryDto.lessonName) {
        queryBuilder.andWhere('quiz.lessonName = :lessonName', {
          lessonName: queryDto.lessonName,
        });
      }

      // Execute the query
      const quiz = await queryBuilder.getOne();

      if (!quiz) {
        this.logger.warn(
          `No quiz found matching criteria for correlation ID: ${correlationId}`,
        );
        throw new NotFoundException(
          'No quiz found matching the provided criteria',
        );
      }

      this.logger.log(
        `Found quiz with ID: ${quiz.id} for correlation ID: ${correlationId}`,
      );

      // Generate pre-signed URLs for quiz assets
      const gifUrls = await this.generateGifUrls(quiz, correlationId);

      // Build the response DTO
      const response: QuizResponseDto = {
        id: this.convertUuidToNumber(quiz.id), // Convert UUID string to number for response
        retrievedMetadata: this.buildRetrievedMetadata(quiz),
        gifUrls,
      };

      return response;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      this.logger.error(
        `Error finding quiz worked solutions for correlation ID: ${correlationId}`,
        error,
      );

      throw new InternalServerErrorException(
        'An error occurred while retrieving quiz data',
      );
    }
  }

  /**
   * Generate pre-signed URLs for quiz GIF assets
   *
   * @param quiz - Quiz entity with assets
   * @param correlationId - Correlation ID for request tracing
   * @returns Promise<GifUrlDto[]> - Array of GIF URLs with question IDs
   */
  private async generateGifUrls(
    quiz: Quiz,
    correlationId: string,
  ): Promise<GifUrlDto[]> {
    this.logger.debug(
      `Generating GIF URLs for quiz ${quiz.id}, correlation ID: ${correlationId}`,
    );

    const gifUrls: GifUrlDto[] = [];

    for (const asset of quiz.assets) {
      try {
        // Parse the file path to extract bucket and object name
        // Assuming filePath format: "bucketName/objectName"
        const pathParts = asset.filePath.split('/');
        const bucketName = pathParts[0];
        const objectName = pathParts.slice(1).join('/');

        // Generate pre-signed URL for the asset
        const presignedUrl = await this.minioService.getPresignedUrl(
          bucketName,
          objectName,
          24 * 60 * 60, // 24 hours expiry
        );

        gifUrls.push({
          id: asset.assetId,
          url: presignedUrl,
        });
      } catch (error) {
        this.logger.error(
          `Error generating pre-signed URL for asset ${asset.assetId} in quiz ${quiz.id}`,
          error,
        );
        // Continue with other assets even if one fails
      }
    }

    this.logger.debug(
      `Generated ${gifUrls.length} GIF URLs for quiz ${quiz.id}`,
    );

    return gifUrls;
  }

  /**
   * Build the retrieved metadata DTO from quiz entity
   *
   * @param quiz - Quiz entity
   * @returns RetrievedMetadataDto - Formatted metadata
   */
  private buildRetrievedMetadata(quiz: Quiz): RetrievedMetadataDto {
    // Ensure internalMetadata is an array
    let internalMetadata: InternalMetadataDto[] = [];
    if (quiz.internalMetadata) {
      if (Array.isArray(quiz.internalMetadata)) {
        internalMetadata = quiz.internalMetadata as InternalMetadataDto[];
      } else if (typeof quiz.internalMetadata === 'object') {
        // If it's an object, try to convert it to an array
        internalMetadata = Object.values(
          quiz.internalMetadata,
        ) as InternalMetadataDto[];
      }
    }

    return {
      subject: quiz.subject,
      grade: quiz.grade,
      classLevel: quiz.classLevel,
      lessonName: quiz.lessonName,
      color: quiz.color,
      year: quiz.year,
      term: quiz.term,
      week: quiz.week,
      weekType: quiz.weekType,
      course: quiz.course || '',
      teachingProgram: quiz.teachingProgram || undefined,
      internalMetadata,
      originalFilename: quiz.originalFilename,
      uploadTimestamp: quiz.uploadTimestamp.toISOString(),
    };
  }

  /**
   * Convert UUID string to a number for API response compatibility
   * Uses a simple hash function to generate a consistent number from UUID
   *
   * @param uuid - UUID string
   * @returns number - Hashed number representation
   */
  private convertUuidToNumber(uuid: string): number {
    let hash = 0;
    for (let i = 0; i < uuid.length; i++) {
      const char = uuid.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

  /**
   * Upload quiz worked solutions from ZIP file
   *
   * @param uploadDto - Upload query parameters
   * @param file - Uploaded ZIP file
   * @param correlationId - Correlation ID for request tracing
   * @returns Promise<QuizUploadResponseDto> - Upload confirmation details
   */
  async uploadQuizWorkedSolutions(
    uploadDto: QuizUploadDto,
    file: Express.Multer.File,
    correlationId: string,
  ): Promise<QuizUploadResponseDto> {
    this.logger.log(
      `Processing quiz upload for correlation ID: ${correlationId}`,
    );

    // Start a database transaction
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    // Track uploaded files for cleanup on error
    const uploadedFiles: { bucketName: string; objectName: string }[] = [];

    try {
      // Extract and validate ZIP file contents
      const zipContents = await this.extractZipContents(
        file.buffer,
        correlationId,
      );

      // Validate required files are present
      this.validateZipStructure(zipContents, correlationId);

      // Parse and validate metadata files
      const lessonMetadata = this.parseLessonMetadata(
        zipContents.lessonMetadata,
        correlationId,
      );
      const internalMetadata = this.parseQzF2fMetadata(
        zipContents.qzF2fMetadata,
        correlationId,
      );

      // Create quiz entity with combined metadata
      const quiz = this.createQuizEntity(
        uploadDto,
        lessonMetadata,
        internalMetadata,
        file.originalname,
      );

      // Save quiz to database
      const savedQuiz = await queryRunner.manager.save(Quiz, quiz);
      this.logger.log(
        `Created quiz with ID: ${savedQuiz.id} for correlation ID: ${correlationId}`,
      );

      // Upload GIF files to MinIO and create quiz assets
      const uploadedGifs = await this.uploadGifFiles(
        zipContents.gifFiles,
        savedQuiz,
        queryRunner,
        correlationId,
        uploadedFiles,
      );

      // Commit transaction
      await queryRunner.commitTransaction();

      this.logger.log(
        `Successfully processed quiz upload with ${uploadedGifs.length} GIF files for correlation ID: ${correlationId}`,
      );

      // Build response according to Design Doc specification
      const response: QuizUploadResponseDto = {
        message: 'Quiz worked solutions uploaded and processed successfully',
        data: {
          quizId: this.convertUuidToNumber(savedQuiz.id),
          metadataPath: this.generateMetadataPath(lessonMetadata, uploadDto),
          uploadedGifs,
          gifCount: uploadedGifs.length,
          extractedMetadata: {
            subject: lessonMetadata.subject,
            grade: lessonMetadata.grade,
            classLevel: lessonMetadata.classLevel,
            color: lessonMetadata.color,
            course: lessonMetadata.course,
            topic: lessonMetadata.topic,
          },
          uploadTimestamp: savedQuiz.createdAt.toISOString(),
          originalFilename: file.originalname,
        },
      };

      return response;
    } catch (error) {
      // Rollback transaction on error
      await queryRunner.rollbackTransaction();

      // Clean up uploaded MinIO files on error
      await this.cleanupUploadedFiles(uploadedFiles, correlationId);

      this.logger.error(
        `Error processing quiz upload for correlation ID: ${correlationId}`,
        error,
      );

      if (error instanceof BadRequestException) {
        throw error;
      }

      throw new InternalServerErrorException(
        'An error occurred while processing the quiz upload',
      );
    } finally {
      // Release query runner
      await queryRunner.release();
    }
  }

  /**
   * Extract contents from ZIP file buffer
   */
  private async extractZipContents(
    buffer: Buffer,
    correlationId: string,
  ): Promise<{
    lessonMetadata: string;
    qzF2fMetadata: string;
    gifFiles: { filename: string; data: Buffer }[];
  }> {
    return new Promise((resolve, reject) => {
      const zipContents = {
        lessonMetadata: '',
        qzF2fMetadata: '',
        gifFiles: [] as { filename: string; data: Buffer }[],
      };

      yauzl.fromBuffer(
        buffer,
        { lazyEntries: true },
        (err: Error | null, zipfile?: yauzl.ZipFile) => {
          if (err) {
            this.logger.error(
              `Error opening ZIP file for correlation ID: ${correlationId}`,
              err,
            );
            return reject(new BadRequestException('Invalid ZIP file format'));
          }

          if (!zipfile) {
            return reject(new BadRequestException('Unable to read ZIP file'));
          }

          zipfile.readEntry();

          zipfile.on('entry', (entry: yauzl.Entry) => {
            if (/\/$/.test(entry.fileName)) {
              // Directory entry, skip
              zipfile.readEntry();
              return;
            }

            zipfile.openReadStream(
              entry,
              (err: Error | null, readStream?: NodeJS.ReadableStream) => {
                if (err) {
                  this.logger.error(
                    `Error reading ZIP entry ${entry.fileName}`,
                    err,
                  );
                  zipfile.readEntry();
                  return;
                }

                if (!readStream) {
                  zipfile.readEntry();
                  return;
                }

                const chunks: Buffer[] = [];
                readStream.on('data', (chunk: Buffer) => chunks.push(chunk));
                readStream.on('end', () => {
                  const data = Buffer.concat(chunks);

                  if (entry.fileName === 'LessonMetadata.json') {
                    zipContents.lessonMetadata = data.toString('utf8');
                  } else if (entry.fileName === 'QzF2f.json') {
                    zipContents.qzF2fMetadata = data.toString('utf8');
                  } else if (
                    entry.fileName.startsWith('solution/') &&
                    entry.fileName.endsWith('.gif')
                  ) {
                    const filename = entry.fileName.replace('solution/', '');
                    zipContents.gifFiles.push({ filename, data });
                  }

                  zipfile.readEntry();
                });

                readStream.on('error', (err: Error) => {
                  this.logger.error(
                    `Error reading stream for ${entry.fileName}`,
                    err,
                  );
                  zipfile.readEntry();
                });
              },
            );
          });

          zipfile.on('end', () => {
            resolve(zipContents);
          });

          zipfile.on('error', (err: Error) => {
            this.logger.error(
              `ZIP file processing error for correlation ID: ${correlationId}`,
              err,
            );
            reject(new BadRequestException('Error processing ZIP file'));
          });
        },
      );
    });
  }

  /**
   * Validate ZIP file structure contains required files
   */
  private validateZipStructure(
    zipContents: {
      lessonMetadata: string;
      qzF2fMetadata: string;
      gifFiles: { filename: string; data: Buffer }[];
    },
    correlationId: string,
  ): void {
    if (!zipContents.lessonMetadata) {
      throw new BadRequestException(
        'Missing required file: LessonMetadata.json',
      );
    }

    if (!zipContents.qzF2fMetadata) {
      throw new BadRequestException('Missing required file: QzF2f.json');
    }

    if (zipContents.gifFiles.length === 0) {
      throw new BadRequestException(
        'No GIF files found in solution/ directory',
      );
    }

    this.logger.debug(
      `ZIP validation passed: LessonMetadata.json, QzF2f.json, and ${zipContents.gifFiles.length} GIF files found for correlation ID: ${correlationId}`,
    );
  }

  /**
   * Parse and validate LessonMetadata.json content using Zod schema
   */
  private parseLessonMetadata(
    content: string,
    correlationId: string,
  ): LessonMetadataDto {
    try {
      // Parse JSON content
      const rawData: unknown = JSON.parse(content);

      // Validate using Zod schema
      const validatedMetadata = LessonMetadataSchema.parse(rawData);

      this.logger.debug(
        `Parsed LessonMetadata: grade=${validatedMetadata.grade}, subject=${validatedMetadata.subject}, classLevel=${validatedMetadata.classLevel} for correlation ID: ${correlationId}`,
      );

      // Convert to DTO format for compatibility with existing code
      const metadata: LessonMetadataDto = {
        grade: validatedMetadata.grade,
        subject: validatedMetadata.subject,
        course: validatedMetadata.course,
        classLevel: validatedMetadata.classLevel,
        color: validatedMetadata.color,
        topic: validatedMetadata.topic,
      };

      return metadata;
    } catch (error) {
      if (error instanceof ZodError) {
        // Convert Zod validation errors to user-friendly messages
        const errorMessages = error.errors.map(
          (err) => `${err.path.join('.')}: ${err.message}`,
        );
        this.logger.error(
          `LessonMetadata.json validation failed for correlation ID: ${correlationId}`,
          errorMessages,
        );
        throw new BadRequestException(
          `LessonMetadata.json validation failed: ${errorMessages.join(', ')}`,
        );
      }

      if (error instanceof BadRequestException) {
        throw error;
      }

      this.logger.error(
        `Error parsing LessonMetadata.json for correlation ID: ${correlationId}`,
        error,
      );
      throw new BadRequestException('Invalid LessonMetadata.json format');
    }
  }

  /**
   * Parse and validate QzF2f.json content using Zod schema
   */
  private parseQzF2fMetadata(
    content: string,
    correlationId: string,
  ): InternalMetadataDto[] {
    try {
      // Parse JSON content
      const rawData: unknown = JSON.parse(content);

      // Validate using Zod schema
      const validatedMetadata = QzF2fMetadataSchema.parse(rawData);

      this.logger.debug(
        `Parsed QzF2f metadata with ${validatedMetadata.length} questions for correlation ID: ${correlationId}`,
      );

      // Convert to DTO format for compatibility with existing code
      const metadata: InternalMetadataDto[] = validatedMetadata.map(
        (question) => ({
          questionId: question.questionId,
          questionNumber: question.questionNumber,
          smilFile: question.smilFile,
          marksJson: question.marksJson,
        }),
      );

      return metadata;
    } catch (error) {
      if (error instanceof ZodError) {
        // Convert Zod validation errors to user-friendly messages
        const errorMessages = error.errors.map(
          (err) => `${err.path.join('.')}: ${err.message}`,
        );
        this.logger.error(
          `QzF2f.json validation failed for correlation ID: ${correlationId}`,
          errorMessages,
        );
        throw new BadRequestException(
          `QzF2f.json validation failed: ${errorMessages.join(', ')}`,
        );
      }

      if (error instanceof BadRequestException) {
        throw error;
      }

      this.logger.error(
        `Error parsing QzF2f.json for correlation ID: ${correlationId}`,
        error,
      );
      throw new BadRequestException('Invalid QzF2f.json format');
    }
  }

  /**
   * Create quiz entity from metadata
   */
  private createQuizEntity(
    uploadDto: QuizUploadDto,
    lessonMetadata: LessonMetadataDto,
    internalMetadata: InternalMetadataDto[],
    originalFilename: string,
  ): Quiz {
    const quiz = new Quiz({
      // From LessonMetadata.json
      subject: lessonMetadata.subject,
      grade: lessonMetadata.grade,
      classLevel: lessonMetadata.classLevel,
      color: lessonMetadata.color,
      course: lessonMetadata.course || '',
      lessonName: lessonMetadata.topic || '', // topic maps to lessonName

      // From query parameters
      year: uploadDto.year,
      term: uploadDto.term,
      week: uploadDto.week,
      weekType: uploadDto.weekType,
      teachingProgram: uploadDto.teachingProgram || '',

      // Quiz-specific fields
      category: MaterialCategory.QUIZ,
      versionType: MaterialVersion.WORKED_SOLUTIONS,
      quizType: QuizType.F2F,

      // File metadata
      originalFilename,
      uploadTimestamp: new Date(),
      internalMetadata: internalMetadata as unknown as Record<string, unknown>,
    });

    return quiz;
  }

  /**
   * Upload GIF files to MinIO and create quiz assets
   */
  private async uploadGifFiles(
    gifFiles: { filename: string; data: Buffer }[],
    quiz: Quiz,
    queryRunner: QueryRunner,
    correlationId: string,
    uploadedFiles: { bucketName: string; objectName: string }[],
  ): Promise<{ questionId: string; objectName: string }[]> {
    const uploadedGifs: { questionId: string; objectName: string }[] = [];
    const bucketName = this.minioService.getDefaultBucket();

    // Ensure bucket exists
    await this.minioService.ensureBucket(bucketName);
    this.logger.log(
      `Ensured bucket exists: ${bucketName} for correlation ID: ${correlationId}`,
    );

    for (const gifFile of gifFiles) {
      try {
        // Generate UUID for the file
        const fileId = uuidv4();
        const objectName = `gifs/${fileId}.gif`;

        // Upload to MinIO
        await this.minioService.uploadFile(gifFile.data, {
          bucketName,
          objectName,
          contentType: 'image/gif',
        });

        // Track uploaded file for potential cleanup
        uploadedFiles.push({ bucketName, objectName });

        // Create QuizAsset record
        const quizAsset = new QuizAsset({
          assetId: gifFile.filename.replace('.gif', ''), // questionId from filename
          type: AssetType.SOLUTION,
          filePath: `${bucketName}/${objectName}`,
          fileSize: gifFile.data.length,
          mimeType: 'image/gif',
          originalFilename: gifFile.filename,
        });

        const savedAsset = await queryRunner.manager.save(QuizAsset, quizAsset);

        // Link asset to quiz (Many-to-Many relationship)
        await queryRunner.manager
          .createQueryBuilder()
          .relation(Quiz, 'assets')
          .of(quiz.id)
          .add(savedAsset.id);

        uploadedGifs.push({
          questionId: gifFile.filename.replace('.gif', ''), // questionId from filename
          objectName,
        });

        this.logger.debug(
          `Uploaded GIF: ${gifFile.filename} as ${objectName} for correlation ID: ${correlationId}`,
        );
      } catch (error) {
        this.logger.error(
          `Error uploading GIF file ${gifFile.filename} for correlation ID: ${correlationId}`,
          error instanceof Error ? error.message : String(error),
        );
        throw new InternalServerErrorException(
          `Failed to upload GIF file: ${gifFile.filename}`,
        );
      }
    }

    return uploadedGifs;
  }

  /**
   * Generate metadata path for response
   */
  private generateMetadataPath(
    lessonMetadata: LessonMetadataDto,
    uploadDto: QuizUploadDto,
  ): string {
    return `metadata/${lessonMetadata.subject}/${lessonMetadata.grade}/${lessonMetadata.classLevel}/${uploadDto.year}/${uploadDto.term}/${uploadDto.week}/${uploadDto.weekType}/metadata.json`;
  }

  /**
   * Clean up uploaded MinIO files on transaction rollback
   */
  private async cleanupUploadedFiles(
    uploadedFiles: { bucketName: string; objectName: string }[],
    correlationId: string,
  ): Promise<void> {
    if (uploadedFiles.length === 0) {
      return;
    }

    this.logger.log(
      `Cleaning up ${uploadedFiles.length} uploaded files for correlation ID: ${correlationId}`,
    );

    for (const file of uploadedFiles) {
      try {
        await this.minioService.deleteFile(file.bucketName, file.objectName);
        this.logger.debug(
          `Cleaned up file: ${file.bucketName}/${file.objectName} for correlation ID: ${correlationId}`,
        );
      } catch (error) {
        this.logger.warn(
          `Failed to clean up file: ${file.bucketName}/${file.objectName} for correlation ID: ${correlationId}`,
          error instanceof Error ? error.message : String(error),
        );
        // Continue with other files even if one fails
      }
    }
  }

  /**
   * Update quiz worked solutions with new metadata and optionally replace ZIP file
   *
   * @param id - Quiz UUID to update
   * @param updateDto - Update query parameters (partial)
   * @param file - Optional uploaded ZIP file to replace existing assets
   * @param correlationId - Correlation ID for request tracing
   * @returns Promise<QuizResponseDto> - Updated quiz data with pre-signed URLs
   */
  async updateQuizWorkedSolutions(
    id: string,
    updateDto: QuizUpdateDto,
    file: Express.Multer.File | undefined,
    correlationId: string,
  ): Promise<QuizResponseDto> {
    this.logger.log(
      `Processing quiz update for ID: ${id}, correlation ID: ${correlationId}`,
    );

    // Start a database transaction
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    // Track uploaded files for cleanup on error
    const uploadedFiles: { bucketName: string; objectName: string }[] = [];
    // Track old files for cleanup on success
    const oldFilesToCleanup: { bucketName: string; objectName: string }[] = [];

    try {
      // Find existing quiz
      const existingQuiz = await this.quizRepository.findOne({
        where: { id },
        relations: ['assets'],
      });

      if (!existingQuiz) {
        throw new NotFoundException(`Quiz with ID ${id} not found`);
      }

      this.logger.log(
        `Found existing quiz with ID: ${id} for correlation ID: ${correlationId}`,
      );

      // If file is provided, process ZIP file replacement
      let newInternalMetadata: InternalMetadataDto[] | undefined;

      if (file) {
        this.logger.debug(
          `Processing ZIP file replacement: ${file.originalname}`,
        );

        // Extract and validate ZIP file contents
        const zipContents = await this.extractZipContents(
          file.buffer,
          correlationId,
        );

        // Validate required files are present
        this.validateZipStructure(zipContents, correlationId);

        // Parse metadata files
        newInternalMetadata = this.parseQzF2fMetadata(
          zipContents.qzF2fMetadata,
          correlationId,
        );

        // Mark old assets for cleanup
        for (const asset of existingQuiz.assets) {
          const pathParts = asset.filePath.split('/');
          const bucketName = pathParts[0];
          const objectName = pathParts.slice(1).join('/');
          oldFilesToCleanup.push({ bucketName, objectName });
        }

        // Delete old quiz assets from database
        // For Many-to-Many relationships, we need to remove the relationship first
        // then delete orphaned assets
        for (const asset of existingQuiz.assets) {
          // Remove the relationship between quiz and asset
          await queryRunner.manager
            .createQueryBuilder()
            .delete()
            .from('quiz_asset_relations')
            .where('quiz_id = :quizId AND asset_id = :assetId', {
              quizId: existingQuiz.id,
              assetId: asset.id,
            })
            .execute();

          // Delete the asset itself
          await queryRunner.manager.delete(QuizAsset, { id: asset.id });
        }

        // Upload new GIF files to MinIO and create new quiz assets
        await this.uploadGifFiles(
          zipContents.gifFiles,
          existingQuiz,
          queryRunner,
          correlationId,
          uploadedFiles,
        );

        // Update originalFilename if new file provided
        existingQuiz.originalFilename = file.originalname;
      }

      // Update quiz metadata with provided fields (partial update)
      if (updateDto.year !== undefined) existingQuiz.year = updateDto.year;
      if (updateDto.term !== undefined) existingQuiz.term = updateDto.term;
      if (updateDto.week !== undefined) existingQuiz.week = updateDto.week;
      if (updateDto.weekType !== undefined)
        existingQuiz.weekType = updateDto.weekType;
      if (updateDto.teachingProgram !== undefined) {
        existingQuiz.teachingProgram = updateDto.teachingProgram;
      }

      // Update lesson metadata fields if provided
      if (updateDto.subject !== undefined)
        existingQuiz.subject = updateDto.subject;
      if (updateDto.grade !== undefined) existingQuiz.grade = updateDto.grade;
      if (updateDto.course !== undefined)
        existingQuiz.course = updateDto.course;
      if (updateDto.classLevel !== undefined)
        existingQuiz.classLevel = updateDto.classLevel;
      if (updateDto.color !== undefined) existingQuiz.color = updateDto.color;
      if (updateDto.lessonName !== undefined)
        existingQuiz.lessonName = updateDto.lessonName;

      // Update internal metadata if new file was provided
      if (newInternalMetadata) {
        existingQuiz.internalMetadata =
          newInternalMetadata as unknown as Record<string, unknown>;
      }

      // Save updated quiz
      await queryRunner.manager.save(Quiz, existingQuiz);

      // Commit transaction
      await queryRunner.commitTransaction();

      // Clean up old files from MinIO after successful transaction
      if (oldFilesToCleanup.length > 0) {
        await this.cleanupUploadedFiles(oldFilesToCleanup, correlationId);
      }

      this.logger.log(
        `Successfully updated quiz with ID: ${id} for correlation ID: ${correlationId}`,
      );

      // Reload quiz with updated assets for response
      const finalQuiz = await this.quizRepository.findOne({
        where: { id },
        relations: ['assets'],
      });

      if (!finalQuiz) {
        throw new InternalServerErrorException('Quiz not found after update');
      }

      // Generate pre-signed URLs for quiz assets
      const gifUrls = await this.generateGifUrls(finalQuiz, correlationId);

      // Build the response DTO
      const response: QuizResponseDto = {
        id: this.convertUuidToNumber(finalQuiz.id),
        retrievedMetadata: this.buildRetrievedMetadata(finalQuiz),
        gifUrls,
      };

      return response;
    } catch (error) {
      // Rollback transaction on error
      await queryRunner.rollbackTransaction();

      // Clean up newly uploaded MinIO files on error
      await this.cleanupUploadedFiles(uploadedFiles, correlationId);

      this.logger.error(
        `Error updating quiz with ID: ${id} for correlation ID: ${correlationId}`,
        error,
      );

      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }

      throw new InternalServerErrorException(
        'An error occurred while updating the quiz',
      );
    } finally {
      // Release query runner
      await queryRunner.release();
    }
  }

  /**
   * Delete quiz worked solutions and all associated assets
   *
   * @param id - Quiz UUID to delete
   * @param correlationId - Correlation ID for request tracing
   * @returns Promise<void> - Resolves when deletion is complete
   */
  async deleteQuizWorkedSolutions(
    id: string,
    correlationId: string,
  ): Promise<void> {
    this.logger.log(
      `Processing quiz deletion for ID: ${id}, correlation ID: ${correlationId}`,
    );

    // Start a database transaction
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    // Track files for cleanup from MinIO
    const filesToCleanup: { bucketName: string; objectName: string }[] = [];

    try {
      // Find existing quiz with assets
      const existingQuiz = await this.quizRepository.findOne({
        where: { id },
        relations: ['assets'],
      });

      if (!existingQuiz) {
        throw new NotFoundException(`Quiz with ID ${id} not found`);
      }

      this.logger.log(
        `Found quiz with ID: ${id} and ${existingQuiz.assets.length} assets for deletion`,
      );

      // Collect files for cleanup from MinIO
      for (const asset of existingQuiz.assets) {
        const pathParts = asset.filePath.split('/');
        const bucketName = pathParts[0];
        const objectName = pathParts.slice(1).join('/');
        filesToCleanup.push({ bucketName, objectName });
      }

      // Delete quiz assets from database
      // For Many-to-Many relationships, we need to remove the relationship first
      // then delete orphaned assets
      for (const asset of existingQuiz.assets) {
        // Remove the relationship between quiz and asset
        await queryRunner.manager
          .createQueryBuilder()
          .delete()
          .from('quiz_asset_relations')
          .where('quiz_id = :quizId AND asset_id = :assetId', {
            quizId: existingQuiz.id,
            assetId: asset.id,
          })
          .execute();

        // Delete the asset itself
        await queryRunner.manager.delete(QuizAsset, { id: asset.id });
      }

      // Delete the quiz itself
      await queryRunner.manager.delete(Quiz, { id: existingQuiz.id });

      // Commit transaction
      await queryRunner.commitTransaction();

      // Clean up files from MinIO after successful database deletion
      // Note: MinIO cleanup errors should not fail the entire operation
      // since the database deletion has already been committed
      if (filesToCleanup.length > 0) {
        try {
          await this.cleanupUploadedFiles(filesToCleanup, correlationId);
          this.logger.log(
            `Successfully deleted quiz with ID: ${id} and cleaned up ${filesToCleanup.length} files for correlation ID: ${correlationId}`,
          );
        } catch (minioError) {
          this.logger.warn(
            `Quiz with ID: ${id} was deleted from database, but MinIO cleanup failed for correlation ID: ${correlationId}`,
            minioError,
          );
          // Continue execution - database deletion was successful
        }
      } else {
        this.logger.log(
          `Successfully deleted quiz with ID: ${id} (no files to clean up) for correlation ID: ${correlationId}`,
        );
      }
    } catch (error) {
      // Rollback transaction on error
      await queryRunner.rollbackTransaction();

      this.logger.error(
        `Error deleting quiz with ID: ${id} for correlation ID: ${correlationId}`,
        error,
      );

      if (error instanceof NotFoundException) {
        throw error;
      }

      throw new InternalServerErrorException(
        'An error occurred while deleting the quiz',
      );
    } finally {
      // Release query runner
      await queryRunner.release();
    }
  }
}
