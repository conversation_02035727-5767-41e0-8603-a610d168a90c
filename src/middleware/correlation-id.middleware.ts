/**
 * Correlation ID Middleware
 *
 * This middleware extracts and validates the X-Correlation-ID header from incoming requests.
 * It ensures that all requests have a valid UUID correlation ID for tracing purposes.
 */

import {
  Injectable,
  NestMiddleware,
  BadRequestException,
} from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { validate as isUUID } from 'uuid';
import { HEADERS } from '../common/constants';

// Extend Express Request interface to include correlationId
declare module 'express-serve-static-core' {
  interface Request {
    correlationId?: string;
  }
}

@Injectable()
export class CorrelationIdMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    const correlationId = req.headers[HEADERS.CORRELATION_ID] as string;

    // Check if correlation ID is provided
    if (!correlationId) {
      throw new BadRequestException({
        statusCode: 400,
        message: 'X-Correlation-ID header is required',
        error: 'Bad Request',
      });
    }

    // Validate that correlation ID is a valid UUID
    if (!isUUID(correlationId)) {
      throw new BadRequestException({
        statusCode: 400,
        message: 'X-Correlation-ID must be a valid UUID',
        error: 'Bad Request',
      });
    }

    // Add correlation ID to request object for use in controllers and services
    req.correlationId = correlationId;

    // Add correlation ID to response headers for tracing
    res.setHeader(HEADERS.CORRELATION_ID, correlationId);

    next();
  }
}
