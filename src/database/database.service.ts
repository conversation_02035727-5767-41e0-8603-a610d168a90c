import { Injectable, Logger } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { DATABASE } from '../common/constants';

@Injectable()
export class DatabaseService {
  private readonly logger = new Logger(DatabaseService.name);

  constructor(
    @InjectDataSource()
    private dataSource: DataSource,
  ) {}

  /**
   * Check if the database connection is healthy
   * @returns A promise that resolves to a boolean indicating if the database is connected
   */
  async isHealthy(): Promise<boolean> {
    try {
      // Check if the data source is initialized and connected
      if (!this.dataSource.isInitialized) {
        return false;
      }

      // Perform a simple query to verify the connection is working
      await this.dataSource.query(DATABASE.HEALTH_CHECK_QUERY);
      return true;
    } catch (error) {
      // Log the error for debugging purposes
      this.logger.error('Database health check failed:', error);
      return false;
    }
  }

  /**
   * Get the current database connection status
   * @returns An object with the database connection status
   */
  async getStatus(): Promise<{ isConnected: boolean; message: string }> {
    const isConnected = await this.isHealthy();
    return {
      isConnected,
      message: isConnected
        ? 'Database connection is healthy'
        : 'Database connection is not established',
    };
  }
}
