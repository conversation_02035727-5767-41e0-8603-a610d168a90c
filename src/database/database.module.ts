import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { DatabaseService } from './database.service';
import { Quiz, QuizAsset } from '../entities';

@Module({
  imports: [
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        const username = configService.get<string>('DB_USERNAME');
        const password = configService.get<string>('DB_PASSWORD');
        const database = configService.get<string>('DB_DATABASE');

        if (!username || !password) {
          throw new Error(
            'SECURITY ERROR: DB_USERNAME and DB_PASSWORD environment variables are required. ' +
              'No default credentials are provided for security reasons.',
          );
        }

        if (!database) {
          throw new Error(
            'CONFIGURATION ERROR: DB_DATABASE environment variable is required.',
          );
        }

        return {
          type: 'postgres',
          host: configService.get<string>('DB_HOST', 'localhost'),
          port: configService.get<number>('DB_PORT', 5432),
          username,
          password,
          database,
          entities: [Quiz, QuizAsset],
          synchronize: configService.get<boolean>('DB_SYNC', false),
          logging: configService.get<boolean>('DB_LOGGING', false),
          // Add connection pool settings to better manage connections
          poolSize: 10,
          connectionTimeoutMillis: 0,
          // Explicitly close idle connections to prevent hanging
          extra: {
            // Max number of clients the pool should contain
            max: 10,
            // Close idle clients after 1 second
            idleTimeoutMillis: 1000,
          },
        };
      },
    }),
  ],
  providers: [DatabaseService],
  exports: [DatabaseService],
})
export class DatabaseModule {}
