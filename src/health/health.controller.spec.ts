/**
 * Health Controller Tests
 *
 * These tests use a real PostgreSQL database connection instead of mocks,
 * following our principle of "never mock components unless absolutely necessary".
 *
 * The tests connect to a dedicated test database instance (configured in .env)
 * to provide more realistic testing of database interactions.
 */

import { TestingModule } from '@nestjs/testing';
import { HealthController } from './health.controller';
import { HealthService } from './health.service';
import { Logger } from '@nestjs/common';
import { DatabaseService } from '../database/database.service';
import { MinioService } from '../minio/minio.service';
import { DataSource } from 'typeorm';
import { cleanupDatabase } from '../../test/utils/database-cleanup';
import { createTestModule } from '../../test/utils/test-module-factory';
import { ConfigModule } from '@nestjs/config';
import minioConfig from '../config/minio.config';

describe('HealthController', () => {
  let controller: HealthController;
  let service: HealthService;
  let dataSource: DataSource;
  let loggerSpy: jest.SpyInstance;

  let module: TestingModule;

  beforeAll(async () => {
    // Environment variables are now loaded from .env file

    // Create a module with a real database connection using the shared factory
    module = await createTestModule({
      imports: [ConfigModule.forFeature(minioConfig)],
      controllers: [HealthController],
      providers: [HealthService, DatabaseService, MinioService],
      enableLogging: false,
    });

    controller = module.get<HealthController>(HealthController);
    service = module.get<HealthService>(HealthService);
    // Get the database service to ensure it's initialized
    module.get<DatabaseService>(DatabaseService);
    dataSource = module.get<DataSource>(DataSource);

    // Mock the logger to avoid console output during tests
    loggerSpy = jest
      .spyOn(Logger.prototype, 'log')
      .mockImplementation(() => {});
  });

  // Use the shared cleanup utility for consistent cleanup behavior
  afterAll(async () => {
    // Restore the logger spy if it exists
    if (loggerSpy) {
      loggerSpy.mockRestore();
    }

    // Use the shared cleanup utility
    await cleanupDatabase(dataSource, module);
  }, 60000); // 60 second timeout

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should return health status with database information', async () => {
    // Use the real health service with real database connection
    const health = await controller.getHealth();

    // Verify the structure and basic content of the health response
    expect(health).toHaveProperty('status');
    expect(health).toHaveProperty('timestamp');
    expect(health).toHaveProperty('service');
    expect(health).toHaveProperty('version');
    expect(health).toHaveProperty('components');
    expect(health.components).toHaveProperty('database');
    expect(health.components).toHaveProperty('minio');

    // Verify database status is included
    expect(health.components.database).toHaveProperty('status');
    expect(health.components.database).toHaveProperty('message');

    // Verify minio status is included
    expect(health.components.minio).toHaveProperty('status');
    expect(health.components.minio).toHaveProperty('message');

    // Since we're using a real database that should be connected,
    // we expect the database status to be 'ok'
    expect(health.components.database.status).toBe('ok');
    // Minio status depends on whether Minio is running
    expect(['ok', 'error']).toContain(health.components.minio.status);
  });

  it('should log the health check request', async () => {
    // Reset the mock to clear previous calls
    loggerSpy.mockClear();

    // Call the health endpoint
    await controller.getHealth();

    // Verify the log message
    expect(loggerSpy).toHaveBeenCalledWith('Health check requested');
  });

  it('should call the health service when health endpoint is accessed', async () => {
    // Spy on the health service
    const serviceSpy = jest.spyOn(service, 'getHealth');

    // Call the health endpoint
    await controller.getHealth();

    // Verify the service was called
    expect(serviceSpy).toHaveBeenCalled();
  });
});
