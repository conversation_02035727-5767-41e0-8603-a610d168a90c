import { Injectable, Logger } from '@nestjs/common';
import { DatabaseService } from '../database/database.service';
import { MinioService } from '../minio/minio.service';
import { STATUS_MESSAGES, SERVICE_INFO } from '../common/constants';

@Injectable()
export class HealthService {
  private readonly logger = new Logger(HealthService.name);

  constructor(
    private readonly databaseService: DatabaseService,
    private readonly minioService: MinioService,
  ) {}

  async getHealth() {
    this.logger.log('Checking system health');

    const dbStatus = await this.databaseService.getStatus();
    const minioStatus = await this.minioService.getStatus();

    const isSystemHealthy = dbStatus.isConnected && minioStatus.isConnected;

    return {
      status: isSystemHealthy ? STATUS_MESSAGES.OK : STATUS_MESSAGES.DEGRADED,
      timestamp: new Date().toISOString(),
      service: SERVICE_INFO.NAME,
      version: process.env.npm_package_version || SERVICE_INFO.DEFAULT_VERSION,
      components: {
        database: {
          status: dbStatus.isConnected
            ? STATUS_MESSAGES.OK
            : STATUS_MESSAGES.ERROR,
          message: dbStatus.message,
        },
        minio: {
          status: minioStatus.isConnected
            ? STATUS_MESSAGES.OK
            : STATUS_MESSAGES.ERROR,
          message: minioStatus.message,
        },
      },
    };
  }
}
