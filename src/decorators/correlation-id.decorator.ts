/**
 * Correlation ID Decorator
 *
 * This decorator provides easy access to the correlation ID from request context
 * in controllers and other components.
 */

import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { Request } from 'express';

/**
 * Custom parameter decorator to extract correlation ID from request
 *
 * Usage in controllers:
 * @Get()
 * getExample(@CorrelationId() correlationId: string) {
 *   // correlationId contains the UUID from X-Correlation-ID header
 * }
 */
export const CorrelationId = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): string => {
    const request = ctx.switchToHttp().getRequest<Request>();
    return request.correlationId || '';
  },
);
