/**
 * Logging Interceptor Integration Tests
 *
 * Tests for the LoggingInterceptor integration with the full application stack.
 */

import { Test } from '@nestjs/testing';
import { INestApplication, Logger } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import request from 'supertest';
import { Server } from 'http';
import { AppModule } from '../app.module';
import { LoggingInterceptor } from './logging.interceptor';
import { ValidationPipe } from '../pipes/validation.pipe';
import { GlobalExceptionFilter } from '../filters/global-exception.filter';
import { cleanupDatabase } from '../../test/utils/database-cleanup';
import { TEST_CREDENTIALS } from '../../test/test-constants';

// Type definitions for log data
interface LoggedData {
  correlationId: string;
  method: string;
  url: string;
  statusCode?: number;
  responseTime?: number;
  userAgent?: string;
  ip?: string;
  contentLength?: number;
  timestamp: string;
  type: string;
  error?: string;
}

// Helper function to get supertest instance
function getHttpServer(app: INestApplication) {
  return request(app.getHttpServer() as Server);
}

describe('LoggingInterceptor Integration', () => {
  let app: INestApplication;
  let dataSource: DataSource;
  let logSpy: jest.SpyInstance;
  let warnSpy: jest.SpyInstance;

  beforeAll(async () => {
    // Environment variables are now loaded from .env file

    const moduleRef = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleRef.createNestApplication();

    // Configure the same global pipes, filters, and interceptors as in main.ts
    app.useGlobalPipes(new ValidationPipe());
    app.useGlobalFilters(new GlobalExceptionFilter());
    app.useGlobalInterceptors(new LoggingInterceptor());

    await app.init();

    // Get database connection for cleanup
    dataSource = moduleRef.get<DataSource>(DataSource);

    // Spy on logger methods with proper typing
    logSpy = jest
      .spyOn(Logger.prototype, 'log')
      .mockImplementation(() => undefined);
    warnSpy = jest
      .spyOn(Logger.prototype, 'warn')
      .mockImplementation(() => undefined);
    jest.spyOn(Logger.prototype, 'error').mockImplementation(() => undefined);
  });

  afterAll(async () => {
    await cleanupDatabase(dataSource, app);
  }, 60000); // 60 second timeout

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Request/Response Logging', () => {
    it('should log successful GET requests with correlation ID', async () => {
      const correlationId = uuidv4();

      await getHttpServer(app)
        .get('/health')
        .set('X-Correlation-ID', correlationId)
        .expect(200);

      // Verify request logging
      expect(logSpy).toHaveBeenCalledWith(
        'GET /health - Request received',
        expect.stringContaining(`"correlationId":"${correlationId}"`),
      );

      // Verify response logging
      expect(logSpy).toHaveBeenCalledWith(
        expect.stringMatching(/GET \/health - 200 \(\d+ms\)/),
        expect.stringContaining(`"correlationId":"${correlationId}"`),
      );
    });

    it('should log POST requests with authentication', async () => {
      const correlationId = uuidv4();

      await getHttpServer(app)
        .get('/protected')
        .set('X-Correlation-ID', correlationId)
        .auth(TEST_CREDENTIALS.USERNAME, TEST_CREDENTIALS.PASSWORD)
        .expect(200);

      // Verify request logging
      expect(logSpy).toHaveBeenCalledWith(
        'GET /protected - Request received',
        expect.stringContaining(`"correlationId":"${correlationId}"`),
      );

      // Verify response logging
      expect(logSpy).toHaveBeenCalledWith(
        expect.stringMatching(/GET \/protected - 200 \(\d+ms\)/),
        expect.stringContaining(`"correlationId":"${correlationId}"`),
      );
    });

    it('should log timing information accurately', async () => {
      const correlationId = uuidv4();
      const startTime = Date.now();

      await getHttpServer(app)
        .get('/health')
        .set('X-Correlation-ID', correlationId)
        .expect(200);

      const endTime = Date.now();
      const maxExpectedTime = endTime - startTime + 100; // Add tolerance

      // Find the response log call
      const logCalls = logSpy.mock.calls as Array<[string, string]>;
      const responseLogCall = logCalls.find((call) => call[0].includes('200'));

      expect(responseLogCall).toBeDefined();
      if (!responseLogCall) return; // Type guard

      // Parse the logged data to verify response time
      const loggedData = JSON.parse(responseLogCall[1]) as LoggedData;
      expect(loggedData.responseTime).toBeGreaterThanOrEqual(0);
      expect(loggedData.responseTime).toBeLessThanOrEqual(maxExpectedTime);
    });

    it('should include request metadata in logs', async () => {
      const correlationId = uuidv4();

      await getHttpServer(app)
        .get('/health')
        .set('X-Correlation-ID', correlationId)
        .set('User-Agent', 'test-integration-agent')
        .expect(200);

      // Find the request log call
      const logCalls = logSpy.mock.calls as Array<[string, string]>;
      const requestLogCall = logCalls.find((call) =>
        call[0].includes('Request received'),
      );

      expect(requestLogCall).toBeDefined();
      if (!requestLogCall) return; // Type guard

      // Parse the logged data to verify metadata
      const loggedData = JSON.parse(requestLogCall[1]) as LoggedData;
      expect(loggedData.method).toBe('GET');
      expect(loggedData.url).toBe('/health');
      expect(loggedData.userAgent).toBe('test-integration-agent');
      expect(loggedData.ip).toBeDefined();
      expect(loggedData.timestamp).toBeDefined();
      expect(loggedData.type).toBe('REQUEST');
    });
  });

  describe('Error Response Logging', () => {
    it('should not log requests that fail in middleware (handled by GlobalExceptionFilter)', async () => {
      // Request without correlation ID should trigger 400 error in middleware
      // The interceptor never gets called because middleware throws before reaching it
      await getHttpServer(app).get('/health').expect(400);

      // Note: When middleware throws an exception, the interceptor is never called
      // Only the GlobalExceptionFilter handles the logging
      expect(warnSpy).toHaveBeenCalledWith(
        'HTTP 400 - X-Correlation-ID header is required',
        expect.stringContaining('"correlationId":"N/A"'),
      );

      // The interceptor should NOT have logged the request since middleware threw first
      const logCalls = logSpy.mock.calls as Array<[string, string]>;
      const requestLogCalls = logCalls.filter((call) =>
        call[0].includes('Request received'),
      );
      expect(requestLogCalls).toHaveLength(0);
    });

    it('should not log requests that fail authentication (handled by guards before interceptor)', async () => {
      const correlationId = uuidv4();

      await getHttpServer(app)
        .get('/protected')
        .set('X-Correlation-ID', correlationId)
        .expect(401); // No authentication provided

      // Note: Authentication guards run before interceptors, so interceptor never gets called
      // Only the GlobalExceptionFilter handles the logging
      expect(warnSpy).toHaveBeenCalledWith(
        'HTTP 401 - Unauthorized',
        expect.stringContaining(`"correlationId":"${correlationId}"`),
      );

      // The interceptor should NOT have logged the request since guard threw first
      const logCalls = logSpy.mock.calls as Array<[string, string]>;
      const requestLogCalls = logCalls.filter((call) =>
        call[0].includes('Request received'),
      );
      expect(requestLogCalls).toHaveLength(0);
    });

    it('should not log requests for 404 errors (handled by routing before interceptor)', async () => {
      const correlationId = uuidv4();

      await getHttpServer(app)
        .get('/non-existent-endpoint')
        .set('X-Correlation-ID', correlationId)
        .expect(404);

      // Note: 404 errors are thrown by NestJS routing before interceptors run
      // Only the GlobalExceptionFilter handles the logging
      expect(warnSpy).toHaveBeenCalledWith(
        'HTTP 404 - Cannot GET /non-existent-endpoint',
        expect.stringContaining(`"correlationId":"${correlationId}"`),
      );

      // The interceptor should NOT have logged the request since routing threw first
      const logCalls = logSpy.mock.calls as Array<[string, string]>;
      const requestLogCalls = logCalls.filter((call) =>
        call[0].includes('Request received'),
      );
      expect(requestLogCalls).toHaveLength(0);
    });
  });

  describe('Integration with Middleware and Filters', () => {
    it('should work correctly with correlation ID middleware', async () => {
      const correlationId = uuidv4();

      const response = await getHttpServer(app)
        .get('/correlation-test')
        .set('X-Correlation-ID', correlationId)
        .expect(200);

      // Verify response contains correlation ID
      expect((response.body as { correlationId: string }).correlationId).toBe(
        correlationId,
      );

      // Verify logging includes correlation ID
      const logCalls = logSpy.mock.calls as Array<[string, string]>;
      const responseLogCall = logCalls.find((call) => call[0].includes('200'));

      expect(responseLogCall).toBeDefined();
      if (!responseLogCall) return; // Type guard
      const loggedData = JSON.parse(responseLogCall[1]) as LoggedData;
      expect(loggedData.correlationId).toBe(correlationId);
    });

    it('should work correctly with global exception filter', async () => {
      // Request without correlation ID triggers exception filter in middleware
      const response = await getHttpServer(app).get('/health').expect(400);

      // Verify standardized error response format
      const responseBody = response.body as Record<string, unknown>;
      expect(responseBody).toMatchObject({
        statusCode: 400,
        message: 'X-Correlation-ID header is required',
        timestamp: expect.any(String) as string,
        path: '/',
      });

      // Note: When middleware throws, interceptor is never called
      // Only GlobalExceptionFilter handles the logging
      expect(warnSpy).toHaveBeenCalledWith(
        'HTTP 400 - X-Correlation-ID header is required',
        expect.stringContaining('"correlationId":"N/A"'),
      );

      // Verify interceptor was NOT called (since middleware threw first)
      const logCalls = logSpy.mock.calls as Array<[string, string]>;
      const requestLogCalls = logCalls.filter((call) =>
        call[0].includes('Request received'),
      );
      expect(requestLogCalls).toHaveLength(0);
    });

    it('should work correctly with authentication guard', async () => {
      const correlationId = uuidv4();

      await getHttpServer(app)
        .get('/protected')
        .set('X-Correlation-ID', correlationId)
        .auth(TEST_CREDENTIALS.USERNAME, TEST_CREDENTIALS.PASSWORD)
        .expect(200);

      // Verify both request and response were logged
      expect(logSpy).toHaveBeenCalledWith(
        'GET /protected - Request received',
        expect.stringContaining(`"correlationId":"${correlationId}"`),
      );

      expect(logSpy).toHaveBeenCalledWith(
        expect.stringMatching(/GET \/protected - 200 \(\d+ms\)/),
        expect.stringContaining(`"correlationId":"${correlationId}"`),
      );
    });
  });

  describe('Content Length Logging', () => {
    it('should log content length for JSON responses', async () => {
      const correlationId = uuidv4();

      const response = await getHttpServer(app)
        .get('/health')
        .set('X-Correlation-ID', correlationId)
        .expect(200);

      // Find the response log call
      const logCalls = logSpy.mock.calls as Array<[string, string]>;
      const responseLogCall = logCalls.find((call) => call[0].includes('200'));

      expect(responseLogCall).toBeDefined();
      if (!responseLogCall) return; // Type guard

      // Parse the logged data to verify content length
      const loggedData = JSON.parse(responseLogCall[1]) as LoggedData;
      expect(loggedData.contentLength).toBeDefined();
      expect(loggedData.contentLength).toBeGreaterThan(0);

      // Verify content length matches actual response
      const expectedLength = Buffer.byteLength(
        JSON.stringify(response.body),
        'utf8',
      );
      expect(loggedData.contentLength).toBe(expectedLength);
    });
  });

  describe('Structured Logging Format', () => {
    it('should produce valid JSON logs', async () => {
      const correlationId = uuidv4();

      await getHttpServer(app)
        .get('/health')
        .set('X-Correlation-ID', correlationId)
        .expect(200);

      // Verify all log calls produce valid JSON
      const logCalls = logSpy.mock.calls as Array<[string, string]>;

      logCalls.forEach((call) => {
        if (call[1]) {
          // Should be able to parse as JSON without throwing
          expect(() => {
            JSON.parse(call[1]);
          }).not.toThrow();

          const loggedData = JSON.parse(call[1]) as LoggedData;
          expect(loggedData).toHaveProperty('correlationId');
          expect(loggedData).toHaveProperty('timestamp');
          expect(loggedData).toHaveProperty('type');
        }
      });
    });

    it('should include all required fields in log structure', async () => {
      const correlationId = uuidv4();

      await getHttpServer(app)
        .get('/health')
        .set('X-Correlation-ID', correlationId)
        .expect(200);

      // Find the response log call
      const logCalls = logSpy.mock.calls as Array<[string, string]>;
      const responseLogCall = logCalls.find((call) => call[0].includes('200'));

      expect(responseLogCall).toBeDefined();
      if (!responseLogCall) return; // Type guard

      const loggedData = JSON.parse(responseLogCall[1]) as LoggedData;

      // Verify all required fields are present
      expect(loggedData).toHaveProperty('correlationId', correlationId);
      expect(loggedData).toHaveProperty('method', 'GET');
      expect(loggedData).toHaveProperty('url', '/health');
      expect(loggedData).toHaveProperty('statusCode', 200);
      expect(loggedData).toHaveProperty('responseTime');
      expect(loggedData).toHaveProperty('userAgent');
      expect(loggedData).toHaveProperty('ip');
      expect(loggedData).toHaveProperty('timestamp');
      expect(loggedData).toHaveProperty('contentLength');

      // Verify data types
      expect(typeof loggedData.responseTime).toBe('number');
      expect(typeof loggedData.statusCode).toBe('number');
      expect(typeof loggedData.timestamp).toBe('string');
    });
  });
});
