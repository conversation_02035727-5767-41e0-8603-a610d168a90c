/**
 * Logging Interceptor
 *
 * This interceptor provides comprehensive request/response logging for all HTTP requests.
 * It captures timing information, correlation IDs, and request details while ensuring
 * sensitive data is not logged.
 *
 * Features:
 * - Request/response timing measurement
 * - Correlation ID extraction and logging
 * - Structured JSON logging format
 * - Sensitive data filtering (Authorization headers, etc.)
 * - Different log levels based on response status codes
 * - Integration with existing middleware and filters
 */

import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
  Logger,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { Request, Response } from 'express';

/**
 * Interface for structured log data
 */
interface LogData {
  correlationId: string;
  method: string;
  url: string;
  statusCode: number;
  responseTime: number;
  userAgent?: string;
  ip?: string;
  contentLength?: number;
  timestamp: string;
}

/**
 * List of sensitive headers that should not be logged
 */
const SENSITIVE_HEADERS = [
  'authorization',
  'cookie',
  'set-cookie',
  'x-api-key',
  'x-auth-token',
];

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(LoggingInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<unknown> {
    const ctx = context.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();

    // Record start time for response time calculation
    const startTime = Date.now();

    // Extract correlation ID from request (set by CorrelationIdMiddleware)
    const correlationId = request.correlationId || 'N/A';

    // Log incoming request
    this.logRequest(request, correlationId);

    return next.handle().pipe(
      tap({
        next: (data) => {
          // Calculate response time
          const responseTime = Date.now() - startTime;

          // Log successful response
          this.logResponse(
            request,
            response,
            correlationId,
            responseTime,
            data,
          );
        },
        error: (error) => {
          // Calculate response time for error cases
          const responseTime = Date.now() - startTime;

          // Log error response (status code will be set by GlobalExceptionFilter)
          this.logErrorResponse(
            request,
            response,
            correlationId,
            responseTime,
            error,
          );
        },
      }),
    );
  }

  /**
   * Log incoming request details
   */
  private logRequest(request: Request, correlationId: string): void {
    const requestData = {
      correlationId,
      method: request.method,
      url: request.url,
      userAgent: request.get('User-Agent') || 'N/A',
      ip: request.ip || request.connection.remoteAddress || 'N/A',
      timestamp: new Date().toISOString(),
      type: 'REQUEST',
    };

    this.logger.log(
      `${request.method} ${request.url} - Request received`,
      JSON.stringify(requestData),
    );
  }

  /**
   * Log successful response details
   */
  private logResponse(
    request: Request,
    response: Response,
    correlationId: string,
    responseTime: number,
    data: unknown,
  ): void {
    const statusCode = response.statusCode;
    const contentLength = this.getContentLength(data);

    const logData: LogData = {
      correlationId,
      method: request.method,
      url: request.url,
      statusCode,
      responseTime,
      userAgent: request.get('User-Agent') || 'N/A',
      ip: request.ip || request.connection.remoteAddress || 'N/A',
      contentLength,
      timestamp: new Date().toISOString(),
    };

    // Use different log levels based on status code
    if (statusCode >= 400) {
      this.logger.warn(
        `${request.method} ${request.url} - ${statusCode} (${responseTime}ms)`,
        JSON.stringify({ ...logData, type: 'RESPONSE_ERROR' }),
      );
    } else {
      this.logger.log(
        `${request.method} ${request.url} - ${statusCode} (${responseTime}ms)`,
        JSON.stringify({ ...logData, type: 'RESPONSE_SUCCESS' }),
      );
    }
  }

  /**
   * Log error response details
   */
  private logErrorResponse(
    request: Request,
    response: Response,
    correlationId: string,
    responseTime: number,
    error: unknown,
  ): void {
    // Status code might not be set yet, so we'll use a default
    const statusCode = response.statusCode || 500;

    const logData: LogData = {
      correlationId,
      method: request.method,
      url: request.url,
      statusCode,
      responseTime,
      userAgent: request.get('User-Agent') || 'N/A',
      ip: request.ip || request.connection.remoteAddress || 'N/A',
      timestamp: new Date().toISOString(),
    };

    const errorMessage = (error as Error)?.message || 'Unknown error';

    this.logger.error(
      `${request.method} ${request.url} - ${statusCode} (${responseTime}ms) - ${errorMessage}`,
      JSON.stringify({
        ...logData,
        type: 'RESPONSE_ERROR',
        error: errorMessage,
      }),
    );
  }

  /**
   * Calculate content length from response data
   */
  private getContentLength(data: unknown): number | undefined {
    if (!data) return undefined;

    try {
      if (typeof data === 'string') {
        return Buffer.byteLength(data, 'utf8');
      }
      if (typeof data === 'object') {
        return Buffer.byteLength(JSON.stringify(data), 'utf8');
      }
    } catch {
      // If we can't calculate content length, return undefined
      return undefined;
    }

    return undefined;
  }

  /**
   * Filter sensitive headers from request headers
   * (Currently not used but available for future enhancement)
   */
  private filterSensitiveHeaders(
    headers: Record<string, unknown>,
  ): Record<string, unknown> {
    const filtered: Record<string, unknown> = {};

    for (const [key, value] of Object.entries(headers)) {
      if (SENSITIVE_HEADERS.includes(key.toLowerCase())) {
        filtered[key] = '[REDACTED]';
      } else {
        filtered[key] = value;
      }
    }

    return filtered;
  }
}
