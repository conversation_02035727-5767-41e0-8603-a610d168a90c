/**
 * Authentication Module
 *
 * This module provides authentication functionality for the TMS API.
 * It includes Basic Auth strategy, guards, and services.
 */

import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { PassportModule } from '@nestjs/passport';
import { AuthService } from './auth.service';
import { BasicStrategy } from './strategies/basic.strategy';
import { BasicAuthGuard } from './guards/basic-auth.guard';
import authConfig from '../config/auth.config';

@Module({
  imports: [ConfigModule.forFeature(authConfig), PassportModule],
  providers: [AuthService, BasicStrategy, BasicAuthGuard],
  exports: [AuthService, BasicAuthGuard],
})
export class AuthModule {}
