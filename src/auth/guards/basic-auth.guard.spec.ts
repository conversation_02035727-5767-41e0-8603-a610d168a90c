/**
 * Basic Auth Guard Unit Tests
 *
 * Tests for the BasicAuthGuard class functionality.
 */

import { Test, TestingModule } from '@nestjs/testing';
import { BasicAuthGuard } from './basic-auth.guard';

describe('BasicAuthGuard', () => {
  let guard: BasicAuthGuard;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [BasicAuthGuard],
    }).compile();

    guard = module.get<BasicAuthGuard>(BasicAuthGuard);
  });

  it('should be defined', () => {
    expect(guard).toBeDefined();
  });

  it('should extend AuthGuard with basic strategy', () => {
    expect(guard).toBeInstanceOf(BasicAuthGuard);
  });

  it('should have the correct strategy name', () => {
    // The BasicAuthGuard extends AuthGuard('basic')
    // This test verifies the guard is properly configured

    // We can't easily test the internal strategy name without mocking Passport,
    // but we can verify the guard is instantiated correctly
    expect(guard).toHaveProperty('canActivate');
  });
});
