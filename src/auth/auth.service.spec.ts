/**
 * Authentication Service Unit Tests
 *
 * Tests for the AuthService class functionality.
 */

import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { AuthService } from './auth.service';
import { TEST_CREDENTIALS } from '../../test/test-constants';

describe('AuthService', () => {
  let service: AuthService;

  const mockConfigService = {
    get: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('validateUser', () => {
    it('should return user object for valid credentials', () => {
      // Arrange
      const mockAuthConfig = {
        username: TEST_CREDENTIALS.USERNAME,
        password: TEST_CREDENTIALS.PASSWORD,
      };
      mockConfigService.get.mockReturnValue(mockAuthConfig);

      // Act
      const result = service.validateUser(
        TEST_CREDENTIALS.USERNAME,
        TEST_CREDENTIALS.PASSWORD,
      );

      // Assert
      expect(result).toEqual({ username: TEST_CREDENTIALS.USERNAME });
      expect(mockConfigService.get).toHaveBeenCalledWith('auth');
    });

    it('should return null for invalid username', () => {
      // Arrange
      const mockAuthConfig = {
        username: TEST_CREDENTIALS.USERNAME,
        password: TEST_CREDENTIALS.PASSWORD,
      };
      mockConfigService.get.mockReturnValue(mockAuthConfig);

      // Act
      const result = service.validateUser(
        'wrong-user',
        TEST_CREDENTIALS.PASSWORD,
      );

      // Assert
      expect(result).toBeNull();
    });

    it('should return null for invalid password', () => {
      // Arrange
      const mockAuthConfig = {
        username: TEST_CREDENTIALS.USERNAME,
        password: TEST_CREDENTIALS.PASSWORD,
      };
      mockConfigService.get.mockReturnValue(mockAuthConfig);

      // Act
      const result = service.validateUser(
        TEST_CREDENTIALS.USERNAME,
        'wrong-password',
      );

      // Assert
      expect(result).toBeNull();
    });

    it('should return null when auth config is not available', () => {
      // Arrange
      mockConfigService.get.mockReturnValue(undefined);

      // Act
      const result = service.validateUser(
        TEST_CREDENTIALS.USERNAME,
        TEST_CREDENTIALS.PASSWORD,
      );

      // Assert
      expect(result).toBeNull();
    });
  });

  describe('getAuthConfig', () => {
    it('should return auth configuration', () => {
      // Arrange
      const mockAuthConfig = {
        username: TEST_CREDENTIALS.USERNAME,
        password: TEST_CREDENTIALS.PASSWORD,
      };
      mockConfigService.get.mockReturnValue(mockAuthConfig);

      // Act
      const result = service.getAuthConfig();

      // Assert
      expect(result).toEqual(mockAuthConfig);
      expect(mockConfigService.get).toHaveBeenCalledWith('auth');
    });

    it('should return undefined when config is not available', () => {
      // Arrange
      mockConfigService.get.mockReturnValue(undefined);

      // Act
      const result = service.getAuthConfig();

      // Assert
      expect(result).toBeUndefined();
    });
  });
});
