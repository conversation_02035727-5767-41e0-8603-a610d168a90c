/**
 * Basic Strategy Unit Tests
 *
 * Tests for the BasicStrategy class functionality.
 */

import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { UnauthorizedException } from '@nestjs/common';
import { BasicStrategy } from './basic.strategy';

describe('BasicStrategy', () => {
  let strategy: BasicStrategy;

  const mockConfigService = {
    get: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BasicStrategy,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    strategy = module.get<BasicStrategy>(BasicStrategy);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(strategy).toBeDefined();
  });

  describe('validate', () => {
    it('should return user object for valid credentials', () => {
      // Arrange
      const mockAuthConfig = {
        username: 'test-user',
        password: 'test-password',
      };
      mockConfigService.get.mockReturnValue(mockAuthConfig);

      // Act
      const result = strategy.validate('test-user', 'test-password');

      // Assert
      expect(result).toEqual({ username: 'test-user' });
      expect(mockConfigService.get).toHaveBeenCalledWith('auth');
    });

    it('should throw UnauthorizedException for invalid username', () => {
      // Arrange
      const mockAuthConfig = {
        username: 'test-user',
        password: 'test-password',
      };
      mockConfigService.get.mockReturnValue(mockAuthConfig);

      // Act & Assert
      expect(() => strategy.validate('wrong-user', 'test-password')).toThrow(
        UnauthorizedException,
      );
      expect(() => strategy.validate('wrong-user', 'test-password')).toThrow(
        'Invalid credentials',
      );
    });

    it('should throw UnauthorizedException for invalid password', () => {
      // Arrange
      const mockAuthConfig = {
        username: 'test-user',
        password: 'test-password',
      };
      mockConfigService.get.mockReturnValue(mockAuthConfig);

      // Act & Assert
      expect(() => strategy.validate('test-user', 'wrong-password')).toThrow(
        UnauthorizedException,
      );
      expect(() => strategy.validate('test-user', 'wrong-password')).toThrow(
        'Invalid credentials',
      );
    });

    it('should throw UnauthorizedException when auth config is not available', () => {
      // Arrange
      mockConfigService.get.mockReturnValue(undefined);

      // Act & Assert
      expect(() => strategy.validate('test-user', 'test-password')).toThrow(
        UnauthorizedException,
      );
      expect(() => strategy.validate('test-user', 'test-password')).toThrow(
        'Authentication configuration not found',
      );
    });
  });
});
