/**
 * Authentication Service
 *
 * This service handles authentication-related business logic.
 * It provides methods for credential validation and user management.
 */

import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AuthConfig } from '../config/auth.config';

export interface User {
  username: string;
}

@Injectable()
export class AuthService {
  constructor(private configService: ConfigService) {}

  /**
   * Validates user credentials against configured values
   * @param username - The username to validate
   * @param password - The password to validate
   * @returns User object if valid, null if invalid
   */
  validateUser(username: string, password: string): User | null {
    const authConfig = this.configService.get<AuthConfig>('auth');

    if (!authConfig) {
      return null;
    }

    if (username === authConfig.username && password === authConfig.password) {
      return { username };
    }

    return null;
  }

  /**
   * Gets the configured authentication credentials
   * @returns AuthConfig object with username and password
   */
  getAuthConfig(): AuthConfig | undefined {
    return this.configService.get<AuthConfig>('auth');
  }
}
