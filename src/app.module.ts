import { Module, NestModule, MiddlewareConsumer } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ThrottlerModule } from '@nestjs/throttler';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { HealthModule } from './health/health.module';
import { DatabaseModule } from './database/database.module';
import { AuthModule } from './auth/auth.module';
import { QuizModule } from './quiz/quiz.module';

import { CorrelationIdMiddleware } from './middleware/correlation-id.middleware';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    // Rate limiting configuration
    ThrottlerModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        const nodeEnv = configService.get<string>('NODE_ENV', 'development');
        const isTestEnvironment =
          nodeEnv === 'test' || nodeEnv === 'development';

        // Environment-based rate limiting configuration
        const windowMs = parseInt(
          configService.get('RATE_LIMIT_WINDOW_MS', '60000'), // 1 minute default
          10,
        );
        const maxRequests = parseInt(
          configService.get('RATE_LIMIT_MAX_REQUESTS', '1000'), // 1000 requests default
          10,
        );

        // Test/Development environment: Higher limits for comprehensive testing
        // Production environment: Secure limits for protection
        const authLimit = isTestEnvironment
          ? parseInt(configService.get('RATE_LIMIT_AUTH_TEST', '500'), 10)
          : parseInt(configService.get('RATE_LIMIT_AUTH_PROD', '100'), 10);

        const uploadLimit = isTestEnvironment
          ? parseInt(configService.get('RATE_LIMIT_UPLOAD_TEST', '500'), 10)
          : parseInt(configService.get('RATE_LIMIT_UPLOAD_PROD', '100'), 10);

        return [
          {
            name: 'default',
            ttl: windowMs,
            limit: maxRequests,
          },
          {
            name: 'auth',
            ttl: 60000, // 1 minute
            limit: authLimit,
          },
          {
            name: 'upload',
            ttl: 60000, // 1 minute
            limit: uploadLimit,
          },
        ];
      },
    }),
    DatabaseModule,
    HealthModule,
    AuthModule,
    QuizModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(CorrelationIdMiddleware).forRoutes('*'); // Apply to all routes
  }
}
