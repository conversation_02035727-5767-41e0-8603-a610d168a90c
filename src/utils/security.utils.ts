/**
 * Security Utilities
 *
 * This module provides security-related utility functions for file validation
 * and request size checking.
 */

/**
 * Sanitizes file upload input to prevent malicious file uploads
 * @param filename - The original filename
 * @param mimetype - The file MIME type
 * @returns Sanitized filename and validation result
 */
export function sanitizeFileUpload(
  filename: string,
  mimetype: string,
): {
  sanitizedFilename: string;
  isValid: boolean;
  reason: string;
} {
  // Remove any path traversal attempts
  const sanitizedFilename = filename.replace(/[/\\:*?"<>|]/g, '_');

  // Check for allowed file extensions (ZIP files only for quiz uploads)
  const allowedExtensions = ['.zip'];
  const fileExtension = sanitizedFilename
    .toLowerCase()
    .substring(sanitizedFilename.lastIndexOf('.'));

  if (!allowedExtensions.includes(fileExtension)) {
    return {
      sanitizedFilename,
      isValid: false,
      reason: `File extension ${fileExtension} not allowed. Only ZIP files are permitted.`,
    };
  }

  // Check MIME type
  const allowedMimeTypes = [
    'application/zip',
    'application/x-zip-compressed',
    'application/octet-stream', // Some browsers send this for ZIP files
  ];

  if (!allowedMimeTypes.includes(mimetype)) {
    return {
      sanitizedFilename,
      isValid: false,
      reason: `MIME type ${mimetype} not allowed. Only ZIP files are permitted.`,
    };
  }

  // Check filename length
  if (sanitizedFilename.length > 255) {
    return {
      sanitizedFilename: sanitizedFilename.substring(0, 255),
      isValid: false,
      reason: 'Filename too long. Maximum 255 characters allowed.',
    };
  }

  // Check for suspicious patterns
  const suspiciousPatterns = [
    /\.exe$/i,
    /\.bat$/i,
    /\.cmd$/i,
    /\.scr$/i,
    /\.pif$/i,
    /\.com$/i,
    /\.jar$/i,
    /\.js$/i,
    /\.vbs$/i,
    /\.ps1$/i,
  ];

  for (const pattern of suspiciousPatterns) {
    if (pattern.test(sanitizedFilename)) {
      return {
        sanitizedFilename,
        isValid: false,
        reason: 'Filename contains suspicious patterns.',
      };
    }
  }

  return {
    sanitizedFilename,
    isValid: true,
    reason: 'File validation passed',
  };
}

/**
 * Validates request size limits
 * @param contentLength - Content length from request headers
 * @returns Validation result
 */
export function validateRequestSize(contentLength: number): {
  isValid: boolean;
  reason: string;
} {
  const maxSize = 100 * 1024 * 1024; // 100MB limit for ZIP files

  if (contentLength > maxSize) {
    return {
      isValid: false,
      reason: `Request size ${contentLength} bytes exceeds maximum allowed size of ${maxSize} bytes (100MB).`,
    };
  }

  return {
    isValid: true,
    reason: 'Request size validation passed',
  };
}
