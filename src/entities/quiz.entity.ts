/**
 * Quiz Entity
 *
 * This class extends Material entity and represents a quiz in the system.
 * It provides quiz-specific fields and functionality.
 */

import { Entity, Column, ManyToMany } from 'typeorm';
import { Material, MaterialCategory } from './material.entity';
import { QuizAsset } from './quiz-asset.entity';

/**
 * Enum for quiz types
 */
export enum QuizType {
  F2F = 'f2f',
  ONLINE = 'online',
}

// QuizMarkingMethod enum removed as requested

/**
 * Quiz entity representing a quiz in the system
 */
@Entity('quizzes')
export class Quiz extends Material {
  @Column({
    type: 'enum',
    enum: QuizType,
    default: QuizType.F2F,
  })
  quizType: QuizType = QuizType.F2F;

  // markingMethod, duration, and totalMarks properties removed as requested

  @Column({ length: 10 })
  color!: string;

  @Column({ name: 'total_marks', nullable: true })
  totalMarks!: number;

  @ManyToMany(() => QuizAsset, (asset) => asset.quizzes, {
    cascade: true,
  })
  assets!: QuizAsset[];

  /**
   * Constructor with default values
   */
  constructor(partial?: Partial<Quiz>) {
    super(partial);

    // Set default values
    this.category = MaterialCategory.QUIZ;

    // Apply any provided properties
    if (partial) {
      Object.assign(this, partial);
    }
  }
}
