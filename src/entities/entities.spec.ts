/**
 * Entity Tests
 *
 * This file contains tests for all entity models to ensure they can be properly
 * instantiated and validated.
 */

import {
  Quiz,
  QuizAsset,
  MaterialCategory,
  MaterialVersion,
  WeekType,
  QuizType,
  AssetType,
} from './index';
import { BaseEntity } from './base.entity';

// Create a concrete implementation of BaseEntity for testing
class TestEntity extends BaseEntity {
  // Add a method to expose protected methods for testing
  testBeforeInsert(): void {
    this.beforeInsert();
  }

  testBeforeUpdate(): void {
    this.beforeUpdate();
  }
}

describe('Entity Models', () => {
  describe('BaseEntity', () => {
    it('should create a base entity instance', () => {
      const entity = new TestEntity();

      expect(entity).toBeDefined();
      expect(entity.id).toBeUndefined();
      expect(entity.createdAt).toBeUndefined();
      expect(entity.updatedAt).toBeUndefined();
      expect(entity.deletedAt).toBeUndefined();
      expect(entity.createdBy).toBeUndefined();
      expect(entity.updatedBy).toBeUndefined();
    });

    it('should call beforeInsert method', () => {
      const entity = new TestEntity();
      const spy = jest.spyOn(entity, 'testBeforeInsert');

      entity.testBeforeInsert();

      expect(spy).toHaveBeenCalled();
    });

    it('should call beforeUpdate method', () => {
      const entity = new TestEntity();
      const spy = jest.spyOn(entity, 'testBeforeUpdate');

      entity.testBeforeUpdate();

      expect(spy).toHaveBeenCalled();
    });
  });

  describe('Quiz Entity', () => {
    it('should create a quiz instance with default values', () => {
      const quiz = new Quiz();

      expect(quiz).toBeDefined();
      expect(quiz.category).toBe(MaterialCategory.QUIZ);
      expect(quiz.quizType).toBe(QuizType.F2F);
      expect(quiz.assets).toBeUndefined();
    });

    it('should create a quiz instance with provided values', () => {
      const quiz = new Quiz({
        subject: 'Math',
        grade: 12,
        classLevel: 'A1',
        lessonName: 'Formulae',
        color: 'R',
        year: 2025,
        term: 1,
        week: 1,
        weekType: WeekType.NORMAL,
        course: '3U',
        teachingProgram: 'St George Girls',
        versionType: MaterialVersion.WORKED_SOLUTIONS,
        originalFilename:
          'Dr Du_Math__V6_Y09_Formulae_A1.R_for_Y09__F2F QZ.zip',
        uploadTimestamp: new Date('2025-05-02T01:38:23.926Z'),
        quizType: QuizType.F2F,
        totalMarks: 20,
      });

      expect(quiz).toBeDefined();
      expect(quiz.category).toBe(MaterialCategory.QUIZ);
      expect(quiz.subject).toBe('Math');
      expect(quiz.grade).toBe(12);
      expect(quiz.classLevel).toBe('A1');
      expect(quiz.lessonName).toBe('Formulae');
      expect(quiz.color).toBe('R');
      expect(quiz.year).toBe(2025);
      expect(quiz.term).toBe(1);
      expect(quiz.week).toBe(1);
      expect(quiz.weekType).toBe(WeekType.NORMAL);
      expect(quiz.course).toBe('3U');
      expect(quiz.teachingProgram).toBe('St George Girls');
      expect(quiz.versionType).toBe(MaterialVersion.WORKED_SOLUTIONS);
      expect(quiz.originalFilename).toBe(
        'Dr Du_Math__V6_Y09_Formulae_A1.R_for_Y09__F2F QZ.zip',
      );
      expect(quiz.uploadTimestamp).toEqual(
        new Date('2025-05-02T01:38:23.926Z'),
      );
      expect(quiz.quizType).toBe(QuizType.F2F);
      expect(quiz.totalMarks).toBe(20);
    });
  });

  describe('QuizAsset Entity', () => {
    it('should create a quiz asset instance with default values', () => {
      const asset = new QuizAsset();

      expect(asset).toBeDefined();
      expect(asset.type).toBe(AssetType.QUESTION);
    });

    it('should create a quiz asset instance with provided values', () => {
      const asset = new QuizAsset({
        assetId: '3421',
        type: AssetType.SOLUTION,
        filePath: '/questions-work-solutions/3421.gif',
        fileSize: 1024,
        mimeType: 'image/gif',
        originalFilename: '3421.gif',
        width: 800,
        height: 600,
      });

      expect(asset).toBeDefined();
      expect(asset.assetId).toBe('3421');
      expect(asset.type).toBe(AssetType.SOLUTION);
      expect(asset.filePath).toBe('/questions-work-solutions/3421.gif');
      expect(asset.fileSize).toBe(1024);
      expect(asset.mimeType).toBe('image/gif');
      expect(asset.originalFilename).toBe('3421.gif');
      expect(asset.width).toBe(800);
      expect(asset.height).toBe(600);
      expect(asset.quizzes).toBeUndefined();
    });
  });
});
