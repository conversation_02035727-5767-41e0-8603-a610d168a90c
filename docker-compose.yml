services:
  api:
    container_name: tms-api-dev-container
    image: tms-api-dev-container:latest
    build:
      context: .
      target: development
    volumes:
      - .:/usr/src/app
      - /usr/src/app/node_modules
    ports:
      - "3000:3000"
    command: npm run start:dev
    environment:
      - NODE_ENV=${NODE_ENV}
      - DB_HOST=postgres
      - DB_PORT=${DB_PORT}
      - DB_USERNAME=${DB_USERNAME}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_DATABASE=${DB_DATABASE}
      - DB_SYNC=${DB_SYNC}
      - DB_LOGGING=${DB_LOGGING}
      - MINIO_ENDPOINT=minio
      - MINIO_PORT=${MINIO_PORT}
      - MINIO_USE_SSL=${MINIO_USE_SSL}
      - MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY}
      - MINIO_SECRET_KEY=${MINIO_SECRET_KEY}
      - MINIO_DEFAULT_BUCKET=${MINIO_DEFAULT_BUCKET}
      - AUTH_USERNAME=${AUTH_USERNAME}
      - AUTH_PASSWORD=${AUTH_PASSWORD}
    depends_on:
      - postgres
      - minio
    restart: unless-stopped

  postgres:
    container_name: tms-postgres-dev-container
    image: postgres:16
    # Note: We keep the official postgres:16 image but use a descriptive container name
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=${DB_USERNAME}
      - POSTGRES_PASSWORD=${DB_PASSWORD}
      - POSTGRES_DB=${DB_DATABASE}
    volumes:
      - tms-postgres-dev-volume:/var/lib/postgresql/data
    restart: unless-stopped

  minio:
    container_name: tms-minio-dev-container
    image: minio/minio:latest
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      - MINIO_ROOT_USER=${MINIO_ACCESS_KEY}
      - MINIO_ROOT_PASSWORD=${MINIO_SECRET_KEY}
    volumes:
      - tms-minio-dev-volume:/data
    command: server /data --console-address ":9001"
    restart: unless-stopped

  pgadmin:
    container_name: tms-pgadmin-dev-container
    image: dpage/pgadmin4:latest
    ports:
      - "8080:80"
    environment:
      - PGADMIN_DEFAULT_EMAIL=${AUTH_USERNAME}@tms.dev
      - PGADMIN_DEFAULT_PASSWORD=${AUTH_PASSWORD}
    volumes:
      - tms-pgadmin-dev-volume:/var/lib/pgadmin
    depends_on:
      - postgres
    restart: unless-stopped

volumes:
  tms-postgres-dev-volume:
  tms-minio-dev-volume:
  tms-pgadmin-dev-volume:
